package com.file.system.file_system.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.aliyun.oss.OSS;
import com.aliyun.oss.model.ObjectMetadata;
import com.aliyun.oss.model.PutObjectResult;
import com.file.system.file_system.service.IOssFileService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.tika.Tika;
import org.apache.tomcat.util.http.fileupload.FileUploadException;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.net.*;
import java.nio.charset.StandardCharsets;
import java.util.Objects;

/**
 * @Description oss文件service层
 * <AUTHOR>
 * @Date 2023/12/19 10:50
 **/
@Service
@Slf4j
public class OssFileServiceImpl implements IOssFileService {
    @Resource
    private OSS ossClientUtils;

    @Value("${oss.bucketName}")
    private String bucketName;

    @Value("${oss.outer.bucket}")
    private String bucket;

    @Value("${spring.profiles.active}")
    private String env;

    private static final String DOT = "\\.";
    private static final String OBLIQUE_ROD = "/";

    private static final Tika TIKA = new Tika();


    @Override
    public String putObject(MultipartFile file, String key, String expireTime) {
        try {
            return putObject(key, file.getInputStream(), file.getOriginalFilename(), expireTime);
        } catch (IOException e) {
            log.error("获取文件输入流异常", e);
            throw new RuntimeException("获取文件输入流异常", e);
        }
    }

    @Override
    public String putObject(String key, String networkUrl, String expireTime) {
        try {
            URL url = new URI(networkUrl).toURL();
            URLConnection urlConnection = url.openConnection();
            urlConnection.setRequestProperty("User-Agent", "Mozilla/5.0 (compatible; MSIE 5.0; Windows NT; DigExt)");
            return putObject(key, urlConnection.getInputStream(), key.substring(key.lastIndexOf("/") + 1), expireTime);
        } catch (IOException | URISyntaxException e) {
            log.error("获取网络流异常，异常原因：", e);
            throw new RuntimeException("获取网络流异常，异常原因：", e);
        }

    }

    private String putObject(String key, InputStream inputStream, String fileName, String expireTime) {
        try {
            int lastIndex = Objects.requireNonNull(fileName, "文件名不能为空").lastIndexOf(".");
            String fileNmePrefix, fileNameSuffix;
            if (lastIndex != -1) {
                fileNmePrefix = fileName.substring(0, lastIndex);
                fileNameSuffix = fileName.substring(lastIndex + 1);
            } else {
                throw new RuntimeException("文件名错误");
            }
            String url = env + OBLIQUE_ROD + key;

            // meta设置请求头
            ObjectMetadata meta = new ObjectMetadata();
            meta.setContentType(TIKA.detect(key));
            meta.setContentDisposition("attachment; filename=" +
                    URLEncoder.encode(fileNmePrefix, StandardCharsets.UTF_8) + "." + fileNameSuffix + ";filename*=UTF-8''" +
                    URLEncoder.encode(fileNmePrefix, StandardCharsets.UTF_8) + "." + fileNameSuffix);
            //DateTime expireTime = DateUtil.offsetDay(new Date(), 7);

            if (StrUtil.isNotBlank(expireTime)) {
                meta.setExpirationTime(DateUtil.parseDateTime(expireTime));
            }

            PutObjectResult putObjectResult = ossClientUtils.putObject(bucketName, url, inputStream, meta);
            if (putObjectResult.getResponse() == null || putObjectResult.getResponse().isSuccessful()) {
                return "https://" + bucket + OBLIQUE_ROD + url;
            } else {
                throw new FileUploadException("文件上传失败，异常原因：" + putObjectResult.getResponse().getErrorResponseAsString());
            }
        } catch (IOException e) {
            log.error("获取文件输入流异常", e);
            throw new RuntimeException("获取文件输入流异常", e);
        }
    }

    /**
     * 生成访问地址
     *
     * @param url
     * @return
     */
    @Override
    public String getUrl(String url) {
        return "https://" + bucket + OBLIQUE_ROD + env + OBLIQUE_ROD + url;
    }

    @Override
    public Boolean doesObjectExist(String filePath) {

        String prefix = "https://" + bucket + OBLIQUE_ROD;
        String result = filePath.substring(prefix.length());

        return  ossClientUtils.doesObjectExist(bucketName, result);
    }
}
