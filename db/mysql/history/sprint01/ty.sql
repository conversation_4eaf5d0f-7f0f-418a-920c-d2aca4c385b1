CREATE TABLE `cm_factory_finished_inventory`
(
    `id`                           varchar(50)                                              NOT NULL,
    `factory_finished_date`        datetime                                                 NOT NULL COMMENT '工厂交货时间',
    `factory_remain_num`           int                                                      NOT NULL COMMENT '剩余数量',
    `factory_shipping_package_num` int                                                      NOT NULL COMMENT '工厂交货数量',
    `create_date`                  datetime                                                     DEFAULT NULL,
    `update_date`                  datetime                                                     DEFAULT NULL,
    `create_by`                    varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
    `update_by`                    varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
    `status`                       char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
    `shipping_project_id`          varchar(50)                                              NOT NULL COMMENT '发货计划id ',
    `destination_sku`              varchar(50)                                              NOT NULL,
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
CREATE TABLE `cm_mock_inventory_table`
(
    `id`                   varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '备注',
    `onshipping_inventory` json                                                         NOT NULL COMMENT '模拟在途库存',
    `remain_inventory`     json                                                         NOT NULL COMMENT '模拟剩余库存',
    `everyday_sale`        json                                                         NOT NULL COMMENT '模拟日销',
    `create_date`          datetime                                                     DEFAULT NULL COMMENT '创建时间',
    `update_date`          datetime                                                     DEFAULT NULL,
    `create_by`            varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
    `update_by`            varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
    `status`               char(1)                                                      NOT NULL,
    `shipping_project_id`  varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '发货计划id',
    `rules_id`             varchar(50)                                                  NOT NULL COMMENT '规则id',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
CREATE TABLE `cm_prepare_products_rules`
(
    `id`                    varchar(255)                                             NOT NULL,
    `virtual_sku`           varchar(50)                                              NOT NULL,
    `purchase_project_days` int                                                           DEFAULT NULL COMMENT '采购时间',
    `produce_days`          varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '生产周期（自定义sku中的采购交期）',
    `transit_days`          int                                                      NOT NULL COMMENT '中转天数',
    `safe_days`             int                                                      NOT NULL COMMENT '安全天数',
    `replenish_frequency`   int                                                      NOT NULL COMMENT '补货频次',
    `head_shipping_days`    json                                                     NOT NULL COMMENT '头程时间',
    `shipping_ratio`        json                                                     NOT NULL COMMENT '发货比例',
    `create_date`           datetime                                                      DEFAULT NULL COMMENT '创建时间',
    `update_date`           datetime                                                      DEFAULT NULL,
    `create_by`             varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  DEFAULT NULL,
    `update_by`             varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  DEFAULT NULL,
    `status`                char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
CREATE TABLE `cm_shipping_project`
(
    `id`                  varchar(50)                                              NOT NULL,
    `destination_sku`     varchar(50)                                              NOT NULL COMMENT '虚拟sku或者老sku',
    `shipping_start_date` datetime                                                 NOT NULL COMMENT '发货装柜时间',
    `create_date`         datetime                                                     DEFAULT NULL,
    `update_date`         datetime                                                     DEFAULT NULL,
    `create_by`           varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
    `update_by`           varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
    `status`              char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
    `trial_status`        char(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '试算状态（-1：作废；0：未发货；1：已发货）',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
CREATE TABLE `cm_trial_inventory_sale_destination`
(
    `id`                        varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
    `foreign_inventory`         json                                                         NOT NULL COMMENT '海外仓状态（根据当前时间与库存可用时间去判断是已经到货还是在途）',
    `destination_everyday_sale` json                                                         NOT NULL COMMENT '目标日销',
    `create_date`               datetime                                                     DEFAULT NULL COMMENT '创建时间',
    `update_date`               datetime                                                     DEFAULT NULL,
    `create_by`                 varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
    `update_by`                 varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
    `status`                    char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci     NOT NULL,
    `shipping_project_id`       varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '试算id',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
CREATE TABLE `cm_trial_shipping_inventory`
(
    `id`                       varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT ' ',
    `expected_arriving_date`   datetime                                                      NOT NULL COMMENT '预计到仓时间',
    `real_shipping_start_date` datetime     DEFAULT NULL,
    `package_num`              int                                                           NOT NULL COMMENT '箱数',
    `destination_warehouse`    varchar(50)                                                   NOT NULL COMMENT '目标仓',
    `remarks`                  varchar(255) DEFAULT NULL COMMENT '备注',
    `create_date`              datetime     DEFAULT NULL,
    `update_date`              datetime     DEFAULT NULL,
    `create_by`                varchar(50)  DEFAULT NULL,
    `update_by`                varchar(50)  DEFAULT NULL,
    `status`                   char(1)                                                       NOT NULL,
    `factory_finished_id`      varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  NOT NULL COMMENT '工厂交货表格主键（cm_factory_finished_inventory)',
    `shipping_num`             int          DEFAULT NULL COMMENT '发货数量',
    `is_package_full`          char(1)                                                       NOT NULL COMMENT '箱子是否装满；0——未装满；1——装满',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;