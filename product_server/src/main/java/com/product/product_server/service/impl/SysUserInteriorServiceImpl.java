package com.product.product_server.service.impl;

import com.alibaba.fastjson2.JSON;
import com.crafts_mirror.common.security.utils.SecurityUtils;
import com.crafts_mirror.utils.utils.RestTemplateUtils;
import com.product.product_server.entity.response.ResultDTO;
import com.product.product_server.entity.vo.UserInteriorVO;
import com.product.product_server.service.ISysUserInteriorService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.ArrayList;
import java.util.List;

import static com.crafts_mirror.utils.constant.SystemConstant.SYS_USER_LIST_URL;

/**
 * <AUTHOR>
 * @description 针对表【cm_replenishment_virtual_sku_purchase】的数据库操作Service实现
 * @createDate 2024-01-17 10:45:51
 */
@Service
@Slf4j
public class SysUserInteriorServiceImpl implements ISysUserInteriorService {
    @Resource
    protected RestTemplate restTemplate;

    @Override
    public List<UserInteriorVO> getUserList() {
        RestTemplateUtils restTemplateUtil = new RestTemplateUtils(restTemplate, SecurityUtils.getToken());
        ResultDTO resultDTO = restTemplateUtil.get(SYS_USER_LIST_URL, ResultDTO.class);
        List data = JSON.to(List.class, resultDTO.getData());
        List<UserInteriorVO> map = new ArrayList<>();
        data.forEach(i -> {
            UserInteriorVO userInteriorVO = JSON.to(UserInteriorVO.class, i);
            map.add(userInteriorVO);
        });
        return map;
    }
}




