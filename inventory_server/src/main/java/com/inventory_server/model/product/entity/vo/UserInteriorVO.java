package com.inventory_server.model.product.entity.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;

/**
 * 角色表 cm_sys_role
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class UserInteriorVO implements Serializable {

    @Serial
    private static final long serialVersionUID = 4610868672848815096L;
    /**
     * 角色id
     */
    private String id;

    /**
     * 用户账号
     */
    private String userName;
    /**
     * 用户昵称
     */
    private String nickName;

    /**
     * 手机号码
     */
    private String phoneNumber;
    /**
     * 帐号状态（0正常 1停用）
     */
    private String userStatus;

    private String createDate;

    private String postId;
}
