package com.inventory_server.model.redundancy.entity.dp;

import lombok.Getter;
import lombok.Setter;

import java.time.LocalDate;

/**
 * @Description 到货信息
 * <AUTHOR>
 * @Date 2024/5/25 9:32
 **/
@Getter
public class ArrivingInfoDp {

//    private final RedundancyInfo.RedundantType type;

    @Setter
    private Double arrivingNum;

    private final LocalDate arrivingDate;

//    private final Boolean isDelivery;

    private final String warehouse;

    public ArrivingInfoDp(Double arrivingNum, LocalDate arrivingDate, String warehouse) {
        this.arrivingNum = arrivingNum;
        this.arrivingDate = arrivingDate;
//        this.isDelivery = this.type.equals(PLAN_ARRIVING);
        this.warehouse = warehouse;
    }

//    public boolean needShippingDate() {
//        return isDelivery || type.equals(TODAY_PURCHASE_ARRIVING) || type.equals(N_DAYS_PURCHASE_ARRIVING);
//    }
}
