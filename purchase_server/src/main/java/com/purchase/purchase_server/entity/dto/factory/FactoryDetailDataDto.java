package com.purchase.purchase_server.entity.dto.factory;

import lombok.Data;
import lombok.experimental.SuperBuilder;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/5/15
 **/
@Data
@SuperBuilder
public class FactoryDetailDataDto implements Serializable {

    @Serial
    private static final long serialVersionUID = 3731921311655664026L;
    private FactoryInfoDto factoryInfoDto;

    private List<FactoryContainerDto> factoryContainerDtoList;

    private FactoryFinancialDto factoryFinancialDto;
}
