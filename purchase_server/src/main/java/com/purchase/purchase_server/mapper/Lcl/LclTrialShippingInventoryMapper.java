package com.purchase.purchase_server.mapper.Lcl;

import com.github.yulichang.base.MPJBaseMapper;
import com.purchase.purchase_server.entity.dataObject.Lcl.LclTrialShippingInventoryDO;
import org.apache.ibatis.annotations.Param;

/**
* <AUTHOR>
* @description 针对表【cm_lcl_trial_shipping_inventory(拼柜到仓数据表)】的数据库操作Mapper
* @createDate 2024-12-04 16:47:07
* @Entity generator.domain.CmLclTrialShippingInventory
*/
public interface LclTrialShippingInventoryMapper extends MPJBaseMapper<LclTrialShippingInventoryDO> {

    int delete(@Param("factoryFinishedId") String factoryFinishedId,
                                   @Param("shippingStartDate") String shippingStartDate);

    int deleteByFinId(@Param("finId") String finId);

    int deleteById(@Param("id") String id);

    int deleteByLclRecordId(@Param("lclRecordId") String lclRecordId);
}




