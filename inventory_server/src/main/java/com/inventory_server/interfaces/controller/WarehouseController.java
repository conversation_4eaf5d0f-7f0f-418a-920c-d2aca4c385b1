package com.inventory_server.interfaces.controller;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.crafts_mirror.common.security.annotation.RequiresPermissions;
import com.crafts_mirror.utils.common.entity.ChannelSearchVo;
import com.crafts_mirror.utils.enums.YesOrNoEnum;
import com.inventory_server.applications.service.IWarehouseAppService;
import com.inventory_server.applications.vo.response.ResultDTO;
import com.inventory_server.infrastructures.aop.PreventReSubmit;
import com.inventory_server.infrastructures.entity.LogTrackNumDto;
import com.inventory_server.infrastructures.exception.RequestTooFrequentlyException;
import com.inventory_server.model.channel.service.IChannelInfoService;
import com.inventory_server.model.warehouse.entity.dto.SenboWarehouseDto;
import com.inventory_server.model.warehouse.entity.form.ChangeSenboWarehouseForm;
import com.inventory_server.model.warehouse.entity.form.SenboWarehouseForm;
import com.inventory_server.model.warehouse.entity.form.WarehouseDetailForm;
import com.inventory_server.model.warehouse.entity.form.WarehouseDetailPageForm;
import com.inventory_server.model.warehouse.entity.vo.SenboWarehouseVo;
import com.inventory_server.model.warehouse.entity.vo.WarehouseDetailVo;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Set;
import java.util.concurrent.TimeUnit;

import static com.crafts_mirror.utils.constant.RedisKeyConstant.*;

/**
 * @Description 仓库管理模块
 * <AUTHOR>
 * @Date 2024/6/27 15:26
 **/
@Validated
@RestController
@RequestMapping(value = "/warehouse")
@Slf4j
public class WarehouseController {

    @Resource
    private IWarehouseAppService warehouseAppService;

    @Resource
    private IChannelInfoService channelInfoService;

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @RequiresPermissions("inventory:warehouse:synchronize")
    @PostMapping("/synchronize/info")
    @ResponseBody
    public ResultDTO<Boolean> synchronizeYiCangWarehouseDetailInfo(@RequestBody WarehouseDetailForm form) {
        try {
            if (Boolean.FALSE.equals(stringRedisTemplate.opsForValue().setIfAbsent(SYNCHRONIZE_WAREHOUSE_LOCK, "1", 3, TimeUnit.HOURS))) {
                log.error("同步易仓仓库信息成功失败，当前有正在同步易仓仓库计划");
                return ResultDTO.success("同步易仓仓库信息成功");
            }
            Set<String> strings = warehouseAppService.synchronizeYiCangWarehouse(form.getWarehouseCodeList());
            if (CollectionUtil.isNotEmpty(strings)) {
                return ResultDTO.success(String.format("这些仓库代码在易仓中不存在：%s", strings));
            }
            return ResultDTO.success("同步易仓仓库信息成功");
        } catch (Exception e) {
            throw new IllegalArgumentException(e.getMessage());
        } finally {
            stringRedisTemplate.delete(SYNCHRONIZE_WAREHOUSE_LOCK);
        }
    }

    @GetMapping("/list/channel/all")
    public ResultDTO<ChannelSearchVo> getAllChannels() {
        ChannelSearchVo channelSearchVo = channelInfoService.getAllChannel();
        return ResultDTO.success(channelSearchVo);
    }

    @RequiresPermissions("inventory:warehouse:list")
    @PostMapping("/page/list")
    @ResponseBody
    public ResultDTO<IPage<WarehouseDetailVo>> warehousePageList(@RequestBody WarehouseDetailPageForm form) {
        return ResultDTO.success(warehouseAppService.getWarehouseDetailList(form));
    }

    @RequiresPermissions("inventory:warehouse:list")
    @GetMapping("/senbo/warehouse")
    @ResponseBody
    public ResultDTO<SenboWarehouseVo> getSenboWarehouse() {
        return ResultDTO.success(warehouseAppService.getSenboWarehouseVo());
    }

    @RequiresPermissions("inventory:warehouse:list")
    @PostMapping("/change/senboWarehouse")
    @ResponseBody
    public ResultDTO<Boolean> changeSenboWarehouse(@RequestBody ChangeSenboWarehouseForm form) {
        warehouseAppService.changeSenboWarehouseInfo(form);
        return ResultDTO.success("修改成功");
    }

    @RequiresPermissions("inventory:warehouse:list")
    @GetMapping("/info/senboWarehouse")
    @ResponseBody
    public ResultDTO<SenboWarehouseDto> getInfoSenboWarehouse(@RequestParam("senboWarehouseId") String id) {
        return ResultDTO.success(warehouseAppService.getInfoSenboWarehouse(id));
    }

    @RequiresPermissions("inventory:senboWarehouse:list")
    @PostMapping("/page/senboWarehouse/list")
    @ResponseBody
    public ResultDTO<IPage<SenboWarehouseDto>> senboWarehousePageList(@RequestBody SenboWarehouseForm form) {
        return ResultDTO.success(warehouseAppService.getSenboWarehousePageList(form));
    }

    @RequiresPermissions("inventory:senboWarehouse:insert")
    @PostMapping("/insert/senboWarehouse")
    @ResponseBody
    @PreventReSubmit
    public ResultDTO<Boolean> insertSenboWarehouse(@RequestBody @Valid SenboWarehouseForm form) {
        warehouseAppService.insertSenboWarehouseInfo(form, new LogTrackNumDto(new HashMap<>()));
        return ResultDTO.success("修改成功");
    }

    @RequiresPermissions("inventory:senboWarehouse:update")
    @PostMapping("/update/senboWarehouse")
    @ResponseBody
    @PreventReSubmit
    public ResultDTO<Boolean> updateSenboWarehouse(@RequestBody @Valid SenboWarehouseForm form) {
        if (YesOrNoEnum.YES.getCodeStr().equals(stringRedisTemplate.opsForValue().get(REDUNDANCY_IN_CAL))) {
            throw new RequestTooFrequentlyException("加急数据计算中，请稍后重试");
        }
        if (YesOrNoEnum.YES.getCodeStr().equals(stringRedisTemplate.opsForValue().get(WAREHOUSE_UPDATE_LOCK))) {
            throw new RequestTooFrequentlyException("加急数据计算中，请稍后重试");
        }

        stringRedisTemplate.opsForValue().set(WAREHOUSE_UPDATE_LOCK, "1", 10, TimeUnit.MINUTES);
        try {
            warehouseAppService.updateSenboWarehouseInfo(form, new LogTrackNumDto(new HashMap<>()));
        } finally {
            stringRedisTemplate.delete(WAREHOUSE_UPDATE_LOCK);
        }
        return ResultDTO.success("修改成功");
    }
}
