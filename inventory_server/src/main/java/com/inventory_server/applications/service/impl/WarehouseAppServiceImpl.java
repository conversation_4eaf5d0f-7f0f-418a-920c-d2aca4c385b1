package com.inventory_server.applications.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.crafts_mirror.common.security.utils.SecurityUtils;
import com.crafts_mirror.utils.aop.OperationLog;
import com.crafts_mirror.utils.enums.ResponseCodeEnum;
import com.inventory_server.applications.service.IWarehouseAppService;
import com.inventory_server.infrastructures.entity.LogTrackNumDto;
import com.inventory_server.infrastructures.exception.BusinessException;
import com.inventory_server.model.warehouse.entity.dto.SenboWarehouseDto;
import com.inventory_server.model.warehouse.entity.dto.WarehouseSummaryDto;
import com.inventory_server.model.warehouse.entity.form.ChangeSenboWarehouseForm;
import com.inventory_server.model.warehouse.entity.form.SenboWarehouseForm;
import com.inventory_server.model.warehouse.entity.form.WarehouseDetailPageForm;
import com.inventory_server.model.warehouse.entity.vo.SenboWarehouseVo;
import com.inventory_server.model.warehouse.entity.vo.WarehouseDetailVo;
import com.inventory_server.model.warehouse.service.IFetchWarehouseFromECService;
import com.inventory_server.model.warehouse.service.IWarehouseSaveService;
import com.inventory_server.model.warehouse.service.IWarehouseService;
import com.inventory_server.model.warning.urgentPurchase.service.IUrgentPurchaseService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description 仓库管理模块应用服务
 * <AUTHOR>
 * @Date 2024/7/3 13:36
 **/
@Service
@Slf4j
public class WarehouseAppServiceImpl implements IWarehouseAppService {

    @Resource
    private IFetchWarehouseFromECService fetchWarehouseFromECService;

    @Resource
    private IWarehouseService warehouseService;

    @Resource
    private IWarehouseSaveService warehouseSaveService;

    @Resource
    private IUrgentPurchaseService urgentPurchaseService;

    @Override
    public Set<String> synchronizeYiCangWarehouse(List<String> yiCangWarehouseCodeList) {
        // 获取操作人
        String username = SecurityUtils.getUsername();
        Optional.ofNullable(username).orElseThrow(() -> new IllegalArgumentException("没有该功能权限"));

        // 如果输入的id为空的话，获取所有易仓的仓库数据
        Set<String> notExistedYcWarehouseCode = new HashSet<>();
        List<WarehouseSummaryDto> warehouseSummaryList = fetchWarehouseFromECService.fetchWarehouseSummaryList();
        if (CollectionUtil.isEmpty(yiCangWarehouseCodeList)) {
            yiCangWarehouseCodeList = warehouseSummaryList.stream().map(WarehouseSummaryDto::getWarehouseCode).toList();
        } else {
            Set<String> ycExistedWarehouseCodeList = warehouseSummaryList.stream().map(WarehouseSummaryDto::getWarehouseCode).collect(Collectors.toSet());
            Set<String> tempSet = new HashSet<>(yiCangWarehouseCodeList);
            tempSet.removeAll(ycExistedWarehouseCodeList);
            if (!tempSet.isEmpty()) {
                yiCangWarehouseCodeList.removeAll(tempSet);
                notExistedYcWarehouseCode = tempSet;
            }
        }

        // 根据仓库id获取到仓库的详细信息
        fetchWarehouseFromECService.asyncFetchAndSaveWarehouseDetailList(yiCangWarehouseCodeList);
        return notExistedYcWarehouseCode;
    }

    @Override
    public IPage<WarehouseDetailVo> getWarehouseDetailList(WarehouseDetailPageForm form) {
        return warehouseService.getWarehouseDetailList(form);
    }

    @Override
    public SenboWarehouseVo getSenboWarehouseVo() {
        return SenboWarehouseVo.builder().senboWarehouseList(warehouseService.getSenboWarehouseList(new SenboWarehouseDto())).build();
    }

    @Override
    public void changeSenboWarehouseInfo(ChangeSenboWarehouseForm form) {
        Map<String, String> map = new HashMap<>();
        map.put(String.valueOf(form.getId()), "修改易仓仓库信息");
        warehouseSaveService.changeWarehouseSenboInfo(form, new LogTrackNumDto(map));
    }

    @Override
    public SenboWarehouseDto getInfoSenboWarehouse(String id) {
        return warehouseService.getInfoSenboWarehouse(id);
    }

    @Override
    public IPage<SenboWarehouseDto> getSenboWarehousePageList(SenboWarehouseForm form) {
        return warehouseService.getSenboWarehousePageList(form);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @OperationLog(content = "创建仓库", operationType = "仓库管理")
    public void insertSenboWarehouseInfo(SenboWarehouseForm form, LogTrackNumDto logTrackNumDto) {
        Integer sort = Integer.valueOf(form.getSort());
        form.setSenboWarehouse(form.getSenboWarehouse().strip());
        if (StrUtil.isBlank(form.getSenboWarehouse())) {
            throw new BusinessException(ResponseCodeEnum.BAD_REQUEST, "仓库名不可以为空");
        }
        if (!warehouseService.checkWarehouseUnique(SenboWarehouseDto.builder().senboWarehouse(form.getSenboWarehouse()).build())) {
            throw new BusinessException(ResponseCodeEnum.BAD_REQUEST, "仓库名不可以重复");
        }
        if (!warehouseService.checkWarehouseUnique(SenboWarehouseDto.builder().sort(sort).build())) {
            throw new BusinessException(ResponseCodeEnum.BAD_REQUEST, "排序不可以重复");
        }

        String id = warehouseSaveService.saveOrUpdateSenBoWarehouseInfo(form);
        logTrackNumDto.getLogMap().put(id, "创建仓库");
    }

    @Override
    @OperationLog(content = "编辑仓库", operationType = "仓库管理")
    public void updateSenboWarehouseInfo(SenboWarehouseForm form, LogTrackNumDto logTrackNumDto) {
        Integer sort = Integer.valueOf(form.getSort());
        form.setSenboWarehouse(form.getSenboWarehouse().strip());
        if (StrUtil.isBlank(form.getSenboWarehouse())) {
            throw new BusinessException(ResponseCodeEnum.BAD_REQUEST, "仓库名不可以为空");
        }
        if (!warehouseService.checkWarehouseUnique(SenboWarehouseDto.builder()
                .senboWarehouseId(Integer.valueOf(form.getSenboWarehouseId()))
                .senboWarehouse(form.getSenboWarehouse()).build())) {
            throw new BusinessException(ResponseCodeEnum.BAD_REQUEST, "仓库名不可以重复");
        }
        if (!warehouseService.checkWarehouseUnique(SenboWarehouseDto.builder()
                .senboWarehouseId(Integer.valueOf(form.getSenboWarehouseId()))
                .sort(sort).build())) {
            throw new BusinessException(ResponseCodeEnum.BAD_REQUEST, "排序不可以重复");
        }

        boolean needReCalUrgentPurchase = false;
        SenboWarehouseDto warehouseInfo = warehouseService.getInfoSenboWarehouse(form.getSenboWarehouseId());
        if (warehouseInfo.getUrgentHeadShipDate() != Integer.parseInt(form.getUrgentHeadShippingDate())) {
            needReCalUrgentPurchase = true;
        }

        String id = warehouseSaveService.saveOrUpdateSenBoWarehouseInfo(form);
        logTrackNumDto.getLogMap().put(id, "编辑仓库");

        if (needReCalUrgentPurchase) {
            urgentPurchaseService.reCalUrgentPurchaseByWarehouse(form.getSenboWarehouseId());
        }
    }
}
