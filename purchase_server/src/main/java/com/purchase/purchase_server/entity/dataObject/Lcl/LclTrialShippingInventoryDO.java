package com.purchase.purchase_server.entity.dataObject.Lcl;

import com.baomidou.mybatisplus.annotation.TableName;
import com.purchase.purchase_server.entity.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.io.Serial;
import java.util.Date;

/**
 * 拼柜到仓数据表
 */
@TableName(value ="cm_lcl_trial_shipping_inventory")
@EqualsAndHashCode(callSuper = true)
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class LclTrialShippingInventoryDO extends BaseEntity {

    @Serial
    private static final long serialVersionUID = -4170551826951558085L;
    /**
     * 发货装柜时间
     */
    private Date shippingStartDate;

    /**
     * 箱数
     */
    private Integer packageNum;

    /**
     * 目标仓
     */
    private String destinationWarehouse;

    /**
     * 工厂交货表格主键（cm_delivery_factory_inventory)
     */
    private String lclFactoryFinishedId;

    /**
     * 发货数量
     */
    private Integer shippingNum;

    /**
     * 箱子是否装满
     */
    private String isPackageFull;

    /**
     * 备注
     */
    private String remarks;

}