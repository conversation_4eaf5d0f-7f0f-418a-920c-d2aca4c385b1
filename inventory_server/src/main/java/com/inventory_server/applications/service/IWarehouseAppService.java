package com.inventory_server.applications.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.inventory_server.infrastructures.entity.LogTrackNumDto;
import com.inventory_server.model.warehouse.entity.dto.SenboWarehouseDto;
import com.inventory_server.model.warehouse.entity.form.ChangeSenboWarehouseForm;
import com.inventory_server.model.warehouse.entity.form.SenboWarehouseForm;
import com.inventory_server.model.warehouse.entity.form.WarehouseDetailPageForm;
import com.inventory_server.model.warehouse.entity.vo.SenboWarehouseVo;
import com.inventory_server.model.warehouse.entity.vo.WarehouseDetailVo;

import java.util.List;
import java.util.Set;

public interface IWarehouseAppService {

    /**
     * 同步易仓的仓库数据到我们数据库
     *
     * @param yiCangWarehouseIdList 仓库id（如果为空的话，同步所有仓库数据）
     */
    Set<String> synchronizeYiCangWarehouse(List<String> yiCangWarehouseIdList);

    /**
     * 仓库管理列表页
     *
     * @param form 请求参数
     * @return 仓库列表页数据
     */
    IPage<WarehouseDetailVo> getWarehouseDetailList(WarehouseDetailPageForm form);

    SenboWarehouseVo getSenboWarehouseVo();

    void changeSenboWarehouseInfo(ChangeSenboWarehouseForm form);

    SenboWarehouseDto getInfoSenboWarehouse(String id);
    /**
     * 新增森帛仓库信息
     *
     */
    IPage<SenboWarehouseDto> getSenboWarehousePageList(SenboWarehouseForm form);

    /**
     * 新增森帛仓库信息
     *
     * @param form 森帛仓库信息表单
     */
    void insertSenboWarehouseInfo(SenboWarehouseForm form, LogTrackNumDto logTrackNumDto);

    /**
     * 更新森帛仓库信息
     *
     * @param form 森帛仓库信息表单
     */
    void updateSenboWarehouseInfo(SenboWarehouseForm form, LogTrackNumDto logTrackNumDto);
}
