package com.inventory_server.model.product.entity.dp;

import cn.hutool.core.util.StrUtil;
import com.crafts_mirror.utils.utils.PatternUtils;

/**
 * @Description 发货计划-虚拟SKU数据校验
 * <AUTHOR>
 * @Date 2023/12/28 10:56
 **/
public record ImportVirtualSkuInfoDp(String virtualSku) {
    public ImportVirtualSkuInfoDp {
        validVirtualSku(virtualSku);
    }
    private void validVirtualSku(String virtualSku) {
        if(StrUtil.isNotBlank(virtualSku)) {
            if(virtualSku.length() > 20) {
                throw new IllegalArgumentException("虚拟sku长度超出限制");
            } else if(StrUtil.isNotBlank(virtualSku) && !PatternUtils.UPPERCASE_LOWERCASE_NUMBER_DOT_PATTERN.matcher(virtualSku).matches()) {
                    throw new IllegalArgumentException("虚拟sku只能英文、数字和符号的组合");
            }
        }else {
            throw new IllegalArgumentException("虚拟sku不能为空");
        }
    }
}
