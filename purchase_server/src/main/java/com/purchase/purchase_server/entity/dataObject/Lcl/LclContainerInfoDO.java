package com.purchase.purchase_server.entity.dataObject.Lcl;

/**
 * <AUTHOR>
 * @date 2025/5/22
 **/

import com.baomidou.mybatisplus.annotation.TableName;
import com.purchase.purchase_server.entity.BaseEntity;
import com.purchase.purchase_server.enums.lcl.ShipmentStatusEnum;
import com.purchase.purchase_server.enums.shipment.LoadingStatusEnum;
import com.purchase.purchase_server.enums.shipment.TransportationModeEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.io.Serial;
import java.util.Date;

/**
 * 拼柜信息表
 */
@TableName(value = "cm_lcl_container_info")
@EqualsAndHashCode(callSuper = true)
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class LclContainerInfoDO extends BaseEntity {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 货件号
     */
    private String shipmentCode;

    /**
     * 发货装柜时间
     */
    private Date shippingStartDate;

    /**
     * 货件来源
     */
    private String sourceType;

    /**
     * 目的仓
     */
    private String destinationWarehouse;

    /**
     * 地址代号
     */
    private String addressCode;

    /**
     * 供应商数量
     */
    private Integer factoryCount;

    /**
     * 发货数量
     */
    private Integer shippingQuantity;

    /**
     * 发货体积
     */
    private Double shippingVolume;

    /**
     * 容器装载率(%)
     */
    private Double containerLoadingRate;

    /**
     * 盈余体积(m3)
     */
    private Double surplusVolume;

    /**
     * 商检体积占比(%)
     */
    private Double inspectionVolumeRatio;

    /**
     * 发货重量(kg)
     */
    private Double shippingWeight;

    /**
     * 货件大类
     */
    private String shipmentCategory;

    /**
     * 货件状态
     * @see ShipmentStatusEnum
     */
    private Integer shipmentStatus;
    /**
     * 装载状态
     * @see LoadingStatusEnum
     */
    private Integer loadingStatus;
    /**
     * 运输方式
     * @see TransportationModeEnum
     */
    private Integer transportationMode;

    /**
     * 拼柜记录id
     */
    private String lclRecordId;
}
