package com.purchase.purchase_server.service.lcl.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.handler.CellWriteHandler;
import com.alibaba.excel.write.handler.context.CellWriteHandlerContext;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.alibaba.fastjson2.JSON;
import com.crafts_mirror.common.security.utils.SecurityUtils;
import com.crafts_mirror.utils.enums.ResponseCodeEnum;
import com.crafts_mirror.utils.utils.RestTemplateUtils;
import com.purchase.purchase_server.entity.dataObject.Lcl.LclConsolidationRecordDO;
import com.purchase.purchase_server.entity.dto.ProductCategoryDTO;
import com.purchase.purchase_server.entity.dto.SenboWarehouseDto;
import com.purchase.purchase_server.entity.excelObject.Lcl.LclConsolidationFinExcel;
import com.purchase.purchase_server.entity.form.LclConsolidationRecordForm;
import com.purchase.purchase_server.entity.form.LclShippingNumEditForm;
import com.purchase.purchase_server.entity.response.ResultDTO;
import com.purchase.purchase_server.entity.vo.Lcl.*;
import com.purchase.purchase_server.enums.lcl.*;
import com.purchase.purchase_server.exception.BusinessException;
import com.purchase.purchase_server.repository.dataRepository.Lcl.*;
import com.purchase.purchase_server.repository.interiorRepository.WarehouseRepository;
import com.purchase.purchase_server.service.channel.IChannelInfoService;
import com.purchase.purchase_server.service.lcl.IExcelExportService;
import com.purchase.purchase_server.service.lcl.ILclConsolidationFinishedInventoryService;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.stream.Collectors;

import static com.crafts_mirror.utils.constant.DateFormatConstant.YY_MM_DD_DATE_FORMAT_CHINESE;
import static com.crafts_mirror.utils.constant.RedisKeyConstant.EDIT_LCL_DATA_CLEAN_LOCK;
import static com.crafts_mirror.utils.constant.SystemConstant.PRODUCTS_SELF_CATEGORY_LEAF_TREE_URL;

/**
 * <AUTHOR>
 * @description 针对表【cm_lcl_finished_inventory(拼柜装柜计划数据表)】的数据库操作Service实现
 * @createDate 2024-12-04 16:47:07
 */
@Service
@Slf4j
public class LclConsolidationFinishedInventoryServiceImpl implements ILclConsolidationFinishedInventoryService {

    @Resource
    private LclConsolidationRecordRepositoryImpl recordRepository;

    @Resource
    private LclConsolidationFinishedInventoryRepositoryImpl lclConsolidationFinRepository;

    @Resource
    private LclConsolidationTrialShippingInventoryRepositoryImpl lclConsolidationTrialRepository;

    @Resource
    private LclFinishedInventoryRepositoryImpl lclFinRepository;

    @Resource
    private LclShippingNumHistoryRepositoryImpl lclShippingNumHistoryRepository;

    @Resource
    private WarehouseRepository warehouseRepository;

    @Resource
    private IExcelExportService excelExportService;

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Resource
    protected RestTemplate restTemplate;

    @Resource
    private IChannelInfoService channelInfoService;


    @Override
    public LclConsolidationPageVO pageListLclFinishedInventory(LclConsolidationRecordForm form) {
        boolean isConsolidation = LclConsolidationEnum.NOT_CONSOLIDATION.getCode().equals(form.getIsConsolidation());
        LclConsolidationRecordDO recordDO = recordRepository.getById(form.getLclRecordId());

        if (ObjectUtil.isEmpty(recordDO)) {
            throw new BusinessException(ResponseCodeEnum.BAD_REQUEST, "装柜计划不存在");
        }
        // 设置日期范围
        if (StrUtil.isBlank(form.getShippingStartDate()) || StrUtil.isBlank(form.getShippingEndDate())) {
            Date startDate = isConsolidation ? recordDO.getShippingStartDate() : recordDO.getLclStartDate();
            Date endDate = isConsolidation ? recordDO.getShippingEndDate() : recordDO.getLclEndDate();
            form.setShippingStartDate(DateUtil.formatDate(startDate));
            form.setShippingEndDate(DateUtil.formatDate(endDate));
        }

        String lock = stringRedisTemplate.opsForValue().get(EDIT_LCL_DATA_CLEAN_LOCK + form.getLclRecordId());
        if (StrUtil.isNotBlank(lock)) {
            return new LclConsolidationPageVO(LclConsolidationEnum.CONSOLIDATION.getCode(), true, LclStatusEnum.DATA_PROCESSING.getCode(), form.getShippingStartDate(),
                    form.getShippingEndDate());
        }
        List<LclConsolidationFinishedInventoryVO> lclFinishedInventoryVO;


        // 获取日期和数据
        String shippingStartDate = DateUtil.formatDate(isConsolidation ? recordDO.getShippingStartDate() : recordDO.getLclStartDate());
        String shippingEndDate = DateUtil.formatDate(isConsolidation ? recordDO.getShippingEndDate() : recordDO.getLclEndDate());
        lclFinishedInventoryVO = isConsolidation ? lclFinRepository.pageList(form) : lclConsolidationFinRepository.pageList(form);
        List<SenboWarehouseDto> senboWarehouseList = warehouseRepository.getSenboWarehouseList();
        Map<String, String> warehouseMap = senboWarehouseList.stream().collect(Collectors.toMap(SenboWarehouseDto::getSenboWarehouseId, SenboWarehouseDto::getSenboWarehouse));

        if (CollectionUtil.isEmpty(lclFinishedInventoryVO)) {
            return new LclConsolidationPageVO(recordDO.getIsLclConsolidation(), false, form.getCurrent(), form.getSize(), senboWarehouseList,
                    form.getShippingStartDate(), form.getShippingEndDate(), recordDO.getLclStatus());
        }

        // 列表数据
        TreeMap<String, TreeMap<String, List<LclConsolidationFinishedInventoryVO>>> groupedResult =
                lclFinishedInventoryVO.stream()
                        .collect(Collectors.groupingBy(
                                LclConsolidationFinishedInventoryVO::getId,
                                () -> new TreeMap<>(String::compareTo),  // 使用TreeMap按id排序
                                Collectors.groupingBy(
                                        LclConsolidationFinishedInventoryVO::getShippingStartDate,
                                        TreeMap::new,  // 使用TreeMap保证日期顺序
                                        Collectors.toList()
                                )
                        ));

        // 统计
        LclPageTotalVO lclPageTotalVO = new LclPageTotalVO();
        // 未安排数量
        Map<String, Integer> unscheduledQuantity = new HashMap<>();
        List<LclConsolidationFinishedInventoryVO> inventoryVOList = new ArrayList<>();
        Integer total = 0;
        for (var lclFinByDate : groupedResult.entrySet()) {
            int sumShippingNum = 0;
            for (var item : lclFinByDate.getValue().values()) {
                for (LclConsolidationFinishedInventoryVO lclFinByItem : item) {
                    sumShippingNum += isConsolidation ? lclFinByItem.getShippingNum() : lclFinByItem.getLclShippingNum();
                }
            }
            ;
            for (var lclFinishedInventory : lclFinByDate.getValue().entrySet()) {
                // 值都一样所以可以去第一条
                var first = lclFinishedInventory.getValue().getFirst();
                first.setFactoryRemainNum(first.getFactoryShippingPackageNum() - sumShippingNum);
                // 记录未安排数量
                unscheduledQuantity.put(first.getId(), first.getFactoryShippingPackageNum() - sumShippingNum);
            }
        }

        // 计算总未安排数量
        int totalUnscheduledQuantity = unscheduledQuantity.values().stream()
                .filter(Objects::nonNull)
                .mapToInt(Integer::intValue)
                .sum();
        lclPageTotalVO.setUnscheduledQuantity(totalUnscheduledQuantity);

        // 遍历外层map
        if (StrUtil.isNotBlank(form.getIsPackageFull()) && !form.getIsPackageFull().equals(IsPackageFullEnum.ALL.getCode())) {
            Iterator<Map.Entry<String, TreeMap<String, List<LclConsolidationFinishedInventoryVO>>>> outerIterator = groupedResult.entrySet().iterator();
            while (outerIterator.hasNext()) {
                Map.Entry<String, TreeMap<String, List<LclConsolidationFinishedInventoryVO>>> outerEntry = outerIterator.next();
                TreeMap<String, List<LclConsolidationFinishedInventoryVO>> innerMap = outerEntry.getValue();

                // 遍历内层map
                Iterator<Map.Entry<String, List<LclConsolidationFinishedInventoryVO>>> innerIterator = innerMap.entrySet().iterator();
                while (innerIterator.hasNext()) {
                    Map.Entry<String, List<LclConsolidationFinishedInventoryVO>> innerEntry = innerIterator.next();
                    List<LclConsolidationFinishedInventoryVO> list = innerEntry.getValue();

                    // 检查当前日期下所有记录是否全部装满
                    boolean allFull = list.stream().allMatch(item -> item.getIsPackageFull().equals(IsPackageFullEnum.FULL.getCode()));

                    // 根据装满状态过滤
                    boolean shouldRemove = form.getIsPackageFull().equals(IsPackageFullEnum.FULL.getCode()) != allFull;
                    if (shouldRemove) {
                        innerIterator.remove();
                    }
                }
                // 如果内层map为空,删除外层entry
                if (innerMap.isEmpty()) {
                    outerIterator.remove();
                }
            }
        }
        // 遍历外层map
        if (StrUtil.isNotBlank(form.getIsChange()) && form.getIsChange().equals(IsChangeEnum.CHANGED.getCode())) {
            Iterator<Map.Entry<String, TreeMap<String, List<LclConsolidationFinishedInventoryVO>>>> outerIterator = groupedResult.entrySet().iterator();
            while (outerIterator.hasNext()) {
                Map.Entry<String, TreeMap<String, List<LclConsolidationFinishedInventoryVO>>> outerEntry = outerIterator.next();
                TreeMap<String, List<LclConsolidationFinishedInventoryVO>> innerMap = outerEntry.getValue();

                // 遍历内层map
                Iterator<Map.Entry<String, List<LclConsolidationFinishedInventoryVO>>> innerIterator = innerMap.entrySet().iterator();
                while (innerIterator.hasNext()) {
                    Map.Entry<String, List<LclConsolidationFinishedInventoryVO>> innerEntry = innerIterator.next();
                    List<LclConsolidationFinishedInventoryVO> list = innerEntry.getValue();

                    // 检查当前日期下所有记录的isChange是否都为0
                    boolean allZero = list.stream().allMatch(item -> item.getIsChange().equals(IsChangeEnum.UNCHANGED.getCode()));
                    if (allZero) {
                        innerIterator.remove();
                    }
                }

                // 如果内层map为空,删除外层entry
                if (innerMap.isEmpty()) {
                    outerIterator.remove();
                }
            }
        }

        RestTemplateUtils restTemplateUtil = new RestTemplateUtils(restTemplate, SecurityUtils.getToken());
        ResultDTO resultDTO = restTemplateUtil.get(PRODUCTS_SELF_CATEGORY_LEAF_TREE_URL, ResultDTO.class);
        Map<String, ProductCategoryDTO> categoryDTOMap = JSON.to(HashMap.class, resultDTO.getData());

        for (var lclFinByDate : groupedResult.entrySet()) {

            ;
            for (var lclFinishedInventory : lclFinByDate.getValue().entrySet()) {
                List<LclConsolidationTrialNumVO> shippingNumList = new ArrayList<>();
                // 是否装满
                String isPackageFull = IsPackageFullEnum.FULL.getCode();
                total += 1;
                // 计算总量
                for (LclConsolidationFinishedInventoryVO val : lclFinishedInventory.getValue()) {

                    // 获取装柜数量
                    Integer lclShippingNum = isConsolidation ? val.getShippingNum() : val.getLclShippingNum();
                    // 累加总发货量
                    lclPageTotalVO.setTotalShipments(lclPageTotalVO.getTotalShipments() + lclShippingNum);

                    // 计算所需集装箱数
                    Integer containerLoad = val.getContainerLoad();
                    // 装箱数
                    int neededContainers = (lclShippingNum + containerLoad - 1) / containerLoad;

                    // 计算总重量
                    Double singleCaseGrossWeight = val.getSingleCaseGrossWeight();
                    if (singleCaseGrossWeight != null) {
                        BigDecimal totalWeight = new BigDecimal(neededContainers)
                                .multiply(BigDecimal.valueOf(singleCaseGrossWeight))
                                .setScale(5, RoundingMode.HALF_UP);
                        lclPageTotalVO.setTotalWeight(lclPageTotalVO.getTotalWeight().add(totalWeight));
                    }
                    // 体积
                    BigDecimal volume = val.getCaseLength()
                            .multiply(val.getCaseWidth())
                            .multiply(val.getCaseHeight())
                            .multiply(BigDecimal.valueOf(neededContainers))
                            .divide(BigDecimal.valueOf(1000000), 5, RoundingMode.HALF_UP); // 转换为立方米并保留5位小数
                    lclPageTotalVO.setTotalVolume(lclPageTotalVO.getTotalVolume().add(volume));
                    val.setVolumeTotal(volume);

                    Integer shippingNum;
                    if (isConsolidation) {
                        shippingNum = val.getShippingNum();
                    } else {
                        shippingNum = val.getLclShippingNum();
                    }
                    shippingNumList.add(LclConsolidationTrialNumVO.builder()
                            .id(val.getLclConsolidationTrialId())
                            .warehouse(warehouseMap.get(val.getDestinationWarehouse()))
                            .shippingNum(shippingNum)
                            .volume(val.getVolumeTotal())
                            .isChange(val.getIsChange()).build());
                    if (val.getIsPackageFull().equals(IsPackageFullEnum.NOT_FULL.getCode())) {
                        isPackageFull = IsPackageFullEnum.NOT_FULL.getCode();
                    }
                }
                // 值都一样所以可以去第一条
                var first = lclFinishedInventory.getValue().getFirst();

                first.setIsPackageFull(isPackageFull);
                first.setShippingNumList(shippingNumList);
                if (StrUtil.isNotBlank(first.getCategory())) {
                    ProductCategoryDTO productCategoryDTO = JSON.to(ProductCategoryDTO.class, categoryDTOMap.get(first.getCategory()));
                    first.setCategory(productCategoryDTO.getCategoryName());
                }
                inventoryVOList.add(first);
            }
        }



        // 定义默认的次要排序比较器
        var factoryCodeComparator = Comparator.comparing(LclConsolidationFinishedInventoryVO::getFactoryCode, Comparator.nullsLast(String::compareTo));
        var contractCodeComparator = Comparator.comparing(LclConsolidationFinishedInventoryVO::getContractCode, Comparator.nullsLast(String::compareTo));
        var selfSkuComparator = Comparator.comparing(LclConsolidationFinishedInventoryVO::getSelfSku, Comparator.nullsLast(String::compareTo));

        Comparator<LclConsolidationFinishedInventoryVO> finalComparator;

        String directionField = form.getDirection();
        String sortOrder = form.getSort();

        if (StrUtil.isNotBlank(directionField)) {
            Comparator<LclConsolidationFinishedInventoryVO> primarySortComparator = null;
            boolean primaryFieldIsContractCode = false;

            if (LclDirectionAndSortEnum.FACTORY_REMAIN_NUM.getCode().equals(sortOrder)) {
                Comparator<Integer> order = Comparator.nullsLast(Integer::compareTo);
                if (LclDirectionAndSortEnum.DESC.getCode().equalsIgnoreCase(directionField)) {
                    order = order.reversed();
                }
                primarySortComparator = Comparator.comparing(LclConsolidationFinishedInventoryVO::getFactoryRemainNum, order);
            } else if (LclDirectionAndSortEnum.SHIPPING_START_DATE.getCode().equals(sortOrder)) {
                Comparator<String> order = Comparator.nullsLast(String::compareTo); // 日期作为字符串比较
                if (LclDirectionAndSortEnum.DESC.getCode().equalsIgnoreCase(directionField)) {
                    order = order.reversed();
                }
                primarySortComparator = Comparator.comparing(LclConsolidationFinishedInventoryVO::getShippingStartDate, order);
            } else if (LclDirectionAndSortEnum.SHIPMENT_CODE.getCode().equals(sortOrder)) {
                Comparator<String> order = Comparator.nullsLast(String::compareTo);
                if (LclDirectionAndSortEnum.DESC.getCode().equalsIgnoreCase(directionField)) {
                    order = order.reversed();
                }
                primarySortComparator = Comparator.comparing(LclConsolidationFinishedInventoryVO::getContractCode, order);
                primaryFieldIsContractCode = true;
            }

            if (primarySortComparator != null) {
                finalComparator = primarySortComparator;
                finalComparator = finalComparator.thenComparing(factoryCodeComparator);

                if (!primaryFieldIsContractCode) {
                    finalComparator = finalComparator.thenComparing(contractCodeComparator);
                }
                finalComparator = finalComparator.thenComparing(selfSkuComparator);
            } else {
                finalComparator = factoryCodeComparator.thenComparing(contractCodeComparator).thenComparing(selfSkuComparator);
            }
        } else {
            // 如果未提供 directionField，则使用默认排序: factoryCode, contractCode, selfSku
            finalComparator = factoryCodeComparator.thenComparing(contractCodeComparator).thenComparing(selfSkuComparator);
        }

        inventoryVOList.sort(finalComparator);
        LclConsolidationPageVO lclConsolidationPageVO = new LclConsolidationPageVO();
        lclConsolidationPageVO.setDynamicHeadSet(senboWarehouseList);
        lclConsolidationPageVO.setLclPageTotalVO(lclPageTotalVO);
        lclConsolidationPageVO.setRecords(inventoryVOList.stream()
                .skip((long) (form.getCurrent() - 1) * form.getSize())
                .limit(form.getSize())
                .collect(Collectors.toList()));
        lclConsolidationPageVO.setCurrent(form.getCurrent());
        lclConsolidationPageVO.setSize(form.getSize());
        lclConsolidationPageVO.setTotal(total);
        lclConsolidationPageVO.setShippingStartDate(form.getShippingStartDate());
        lclConsolidationPageVO.setShippingEndDate(form.getShippingEndDate());
        lclConsolidationPageVO.setIsLclConsolidation(recordDO.getIsLclConsolidation());
        lclConsolidationPageVO.setIsDataClean(false);
        lclConsolidationPageVO.setLclStatus(recordDO.getLclStatus());
        return lclConsolidationPageVO;
    }

    /**
     * 获取拼柜发货数量变更历史记录
     *
     * @param form 包含试算ID的表单
     * @return 变更历史记录列表, 包含原始发货记录和后续变更记录
     */
    @Override
    public List<LclShippingNumHistoryVO> getLclNumHistory(LclShippingNumEditForm form) {
        // 获取变更历史记录
        List<LclShippingNumHistoryVO> historyList = lclShippingNumHistoryRepository.list(form.getId());

        if (CollectionUtil.isEmpty(historyList)) {
            return historyList;
        }
        // 获取试算信息
        var lclTrialDO = lclConsolidationTrialRepository.getById(form.getId());

        // 转换变更类型编码为描述
        historyList.forEach(history -> {
                    if (StrUtil.isNotBlank(history.getChangeValue()) && Integer.parseInt(history.getChangeValue()) > 0) {
                        history.setChangeValue("+" + history.getChangeValue());
                    }
                }
        );

        // 添加原始发货记录到列表首位
        historyList.addFirst(LclShippingNumHistoryVO.builder()
                .changeValue(lclTrialDO.getShippingNum().toString())
                .changeType(ChangeTypeEnum.ORIGINAL_SHIPMENT.getDesc())
                .shippingStartDate(DateUtil.formatDate(lclTrialDO.getShippingStartDate()))
                .build());

        return historyList;
    }

    @Override
    public void exportLclInfo(LclConsolidationRecordForm form, HttpServletResponse response) {
        LclConsolidationRecordDO recordDO = recordRepository.getById(form.getLclRecordId());

        if (ObjectUtil.isEmpty(recordDO)) {
            throw new BusinessException(ResponseCodeEnum.BAD_REQUEST, "装柜计划不存在");
        }
        form.setCurrent(1);
        form.setSize(-1);
        var lclFinishedInventoryVO = lclConsolidationFinRepository.pageList(form);

        TreeMap<String, TreeMap<String, List<LclConsolidationFinishedInventoryVO>>> groupedResult =
                lclFinishedInventoryVO.stream()
                        .collect(Collectors.groupingBy(
                                LclConsolidationFinishedInventoryVO::getId,
                                () -> new TreeMap<>(String::compareTo),  // 使用TreeMap按id排序
                                Collectors.groupingBy(
                                        LclConsolidationFinishedInventoryVO::getShippingStartDate,
                                        TreeMap::new,  // 使用TreeMap保证日期顺序
                                        Collectors.toList()
                                )
                        ));

        // 遍历外层map
        if (StrUtil.isNotBlank(form.getIsPackageFull()) && !form.getIsPackageFull().equals(IsPackageFullEnum.ALL.getCode())) {
            Iterator<Map.Entry<String, TreeMap<String, List<LclConsolidationFinishedInventoryVO>>>> outerIterator = groupedResult.entrySet().iterator();
            while (outerIterator.hasNext()) {
                Map.Entry<String, TreeMap<String, List<LclConsolidationFinishedInventoryVO>>> outerEntry = outerIterator.next();
                TreeMap<String, List<LclConsolidationFinishedInventoryVO>> innerMap = outerEntry.getValue();

                // 遍历内层map
                Iterator<Map.Entry<String, List<LclConsolidationFinishedInventoryVO>>> innerIterator = innerMap.entrySet().iterator();
                while (innerIterator.hasNext()) {
                    Map.Entry<String, List<LclConsolidationFinishedInventoryVO>> innerEntry = innerIterator.next();
                    List<LclConsolidationFinishedInventoryVO> list = innerEntry.getValue();

                    // 检查当前日期下所有记录是否全部装满
                    boolean allFull = list.stream().allMatch(item -> item.getIsPackageFull().equals(IsPackageFullEnum.FULL.getCode()));

                    // 根据装满状态过滤
                    boolean shouldRemove = form.getIsPackageFull().equals(IsPackageFullEnum.FULL.getCode()) != allFull;
                    if (shouldRemove) {
                        innerIterator.remove();
                    }
                }
                // 如果内层map为空,删除外层entry
                if (innerMap.isEmpty()) {
                    outerIterator.remove();
                }
            }
        }
        // 遍历外层map
        if (StrUtil.isNotBlank(form.getIsChange()) && form.getIsChange().equals(IsChangeEnum.CHANGED.getCode())) {
            Iterator<Map.Entry<String, TreeMap<String, List<LclConsolidationFinishedInventoryVO>>>> outerIterator = groupedResult.entrySet().iterator();
            while (outerIterator.hasNext()) {
                Map.Entry<String, TreeMap<String, List<LclConsolidationFinishedInventoryVO>>> outerEntry = outerIterator.next();
                TreeMap<String, List<LclConsolidationFinishedInventoryVO>> innerMap = outerEntry.getValue();

                // 遍历内层map
                Iterator<Map.Entry<String, List<LclConsolidationFinishedInventoryVO>>> innerIterator = innerMap.entrySet().iterator();
                while (innerIterator.hasNext()) {
                    Map.Entry<String, List<LclConsolidationFinishedInventoryVO>> innerEntry = innerIterator.next();
                    List<LclConsolidationFinishedInventoryVO> list = innerEntry.getValue();

                    // 检查当前日期下所有记录的isChange是否都为0
                    boolean allZero = list.stream().allMatch(item -> item.getIsChange().equals(IsChangeEnum.UNCHANGED.getCode()));
                    if (allZero) {
                        innerIterator.remove();
                    }
                }

                // 如果内层map为空,删除外层entry
                if (innerMap.isEmpty()) {
                    outerIterator.remove();
                }
            }
        }

        List<LclConsolidationFinExcel> lclConsolidationFinExcelList = new ArrayList<>();

        for (var trialEntry : groupedResult.entrySet()) {
            TreeMap<String, List<LclConsolidationFinishedInventoryVO>> value = trialEntry.getValue();
            LclConsolidationFinishedInventoryVO first = value.firstEntry().getValue().getFirst();
            Integer originalShippingNum = 0;
            for (var entry : value.entrySet()) {
                List<LclConsolidationFinishedInventoryVO> list = entry.getValue();
                for (var voValue : list) {
                    originalShippingNum += voValue.getShippingNum();
                }
            }
            lclConsolidationFinExcelList.add(LclConsolidationFinExcel.builder()
                    .contractCode(first.getContractCode())
                    .destinationSku(first.getDestinationSku())
                    .factoryFinishedDate(first.getFactoryFinishedDate())
                    .lclShippingNum(first.getFactoryShippingPackageNum() - first.getFactoryRemainNum())
                    .factoryRemainNum(first.getFactoryRemainNum())
                    .originalShippingNum(originalShippingNum)
                    .build());
        }

        List<SenboWarehouseDto> senboWarehouseList = warehouseRepository.getSenboWarehouseList();
        List<List<String>> headers = excelExportService.createHeaders(senboWarehouseList);

        // 动态字段变更状态 - 用于标记哪些动态字段需要高亮显示
        Map<String, Boolean> sheet1Map = new HashMap<>();
        Map<String, Boolean> fieldChanges = new HashMap<>();
        Map<String, Boolean> dynamicFieldChanges = new HashMap<>();
        Map<String, String> channelIdNameMap = channelInfoService.getChannelIdNameMap();
        List<List<Object>> sheet2DataList = excelExportService.processData(form.getIsChange(), sheet1Map, fieldChanges, dynamicFieldChanges, groupedResult, senboWarehouseList,
                channelIdNameMap);

        sheet2DataList.sort((list1, list2) -> {
            // 首先按照list[0]比较
            int compareResult = compareObjects(list1.get(13), list2.get(13));
            if (compareResult != 0) {
                return compareResult;
            }

            // 然后按照list[2]比较
            compareResult = compareObjects(list1.get(0), list2.get(0));
            if (compareResult != 0) {
                return compareResult;
            }

            // 最后按照list[3]比较
            return compareObjects(list1.get(4), list2.get(4));
        });
        ExcelWriter excelWriter = null;
        try {
            response.setContentType("application/vnd.ms-excel");
            response.setCharacterEncoding("utf-8");

            String dateStr = DateUtil.format(DateUtil.date(), YY_MM_DD_DATE_FORMAT_CHINESE); // 获取当前日期并格式化为"241224"格式
            String fileName = URLEncoder.encode("装柜拼柜整理后数据" + dateStr, StandardCharsets.UTF_8).replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
            excelWriter = EasyExcel.write(response.getOutputStream()).build();

            WriteSheet writeSheet1 = EasyExcel.writerSheet(1, "分析结果").head(LclConsolidationFinExcel.class)
                    .registerWriteHandler(new CellWriteHandler() {
                        @Override
                        public void afterCellDispose(CellWriteHandlerContext context) {
                            Cell cell = context.getCell();
                            // 获取当前行数据
                            if (context.getRowIndex() > 0 && context.getRowIndex() <= lclConsolidationFinExcelList.size() && context.getColumnIndex() < 5) {
                                LclConsolidationFinExcel lclConsolidationFinExcel = lclConsolidationFinExcelList.get(context.getRowIndex() - 1);
                                int columnIndex = context.getColumnIndex();
                                String baseKey = String.format("%s-%s-%s",
                                        lclConsolidationFinExcel.getContractCode(), lclConsolidationFinExcel.getDestinationSku(), lclConsolidationFinExcel.getFactoryFinishedDate());
                                // 如果当前字段需要高亮显示
                                if (sheet1Map.getOrDefault(baseKey, false)) {
                                    // 拿到poi的workbook
                                    Workbook workbook = context.getWriteWorkbookHolder().getWorkbook();
                                    // 这里千万记住 想办法能复用的地方把他缓存起来 一个表格最多创建6W个样式
                                    // 不同单元格尽量传同一个 cellStyle
                                    CellStyle cellStyle = workbook.createCellStyle();
                                    cellStyle.setFillForegroundColor(IndexedColors.LIGHT_ORANGE.getIndex());
                                    // 这里需要指定 FillPatternType 为FillPatternType.SOLID_FOREGROUND
                                    cellStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);

                                    // 设置水平居中
                                    cellStyle.setAlignment(HorizontalAlignment.CENTER);
                                    // 设置垂直居中
                                    cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);

                                    cell.setCellStyle(cellStyle);
                                    // 由于这里没有指定dataformat 最后展示的数据 格式可能会不太正确
                                    // 这里要把 WriteCellData的样式清空， 不然后面还有一个拦截器 FillStyleCellWriteHandler 默认会将 WriteCellStyle 设置到
                                    // cell里面去 会导致自己设置的不一样（很关键）
                                    context.getFirstCellData().setWriteCellStyle(null);
                                }
                            }
                        }
                    }).registerWriteHandler(new LongestMatchColumnWidthStyleStrategy()).build();

            WriteSheet writeSheet2 = EasyExcel.writerSheet(2, "到仓明细").head(headers)
                    .registerWriteHandler(new CellWriteHandler() {
                        @Override
                        public void afterCellDispose(CellWriteHandlerContext context) {
                            Cell cell = context.getCell();
                            // 获取当前行数据
                            if ((context.getRowIndex() > 1 && context.getRowIndex() <= sheet2DataList.size() + 1) &&
                                    (context.getColumnIndex() == 0 || context.getColumnIndex() == 1 || context.getColumnIndex() == 12 ||
                                            context.getColumnIndex() > 14 && context.getColumnIndex() <= 14 + (senboWarehouseList.size() * 2))) {
                                List<Object> rowData = sheet2DataList.get(context.getRowIndex() - 2);
                                int columnIndex = context.getColumnIndex();
                                String baseKey = String.format("%s-%s-%s-%s",
                                        rowData.get(0), rowData.get(1), rowData.get(13), rowData.get(12));

                                Boolean orDefault;
                                if (columnIndex == 0 || columnIndex == 1 || columnIndex == 12) {
                                    orDefault = fieldChanges.getOrDefault(baseKey, false);
                                } else {
                                    if (columnIndex > 14 && columnIndex <= 14 + senboWarehouseList.size()) {
                                        columnIndex -= 15;
                                    } else {
                                        columnIndex -= (15 + senboWarehouseList.size());
                                    }
                                    String header = senboWarehouseList.get(columnIndex).getSenboWarehouse();
                                    String key = baseKey + "-" + header;
                                    orDefault = dynamicFieldChanges.getOrDefault(key, false);
                                }

                                // 如果当前字段需要高亮显示
                                if (orDefault) {
                                    // 拿到poi的workbook
                                    Workbook workbook = context.getWriteWorkbookHolder().getWorkbook();
                                    // 这里千万记住 想办法能复用的地方把他缓存起来 一个表格最多创建6W个样式
                                    // 不同单元格尽量传同一个 cellStyle
                                    CellStyle cellStyle = workbook.createCellStyle();
                                    cellStyle.setFillForegroundColor(IndexedColors.LIGHT_ORANGE.getIndex());
                                    // 这里需要指定 FillPatternType 为FillPatternType.SOLID_FOREGROUND
                                    cellStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
                                    cell.setCellStyle(cellStyle);
                                    // 由于这里没有指定dataformat 最后展示的数据 格式可能会不太正确
                                    // 这里要把 WriteCellData的样式清空， 不然后面还有一个拦截器 FillStyleCellWriteHandler 默认会将 WriteCellStyle 设置到
                                    // cell里面去 会导致自己设置的不一样（很关键）
                                    context.getFirstCellData().setWriteCellStyle(null);
                                }
                            }
                        }
                    })
                    .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                    .build();
            excelWriter.write(lclConsolidationFinExcelList, writeSheet1);
            excelWriter.write(sheet2DataList, writeSheet2);

        } catch (Exception e) {
            log.error("获取输出流异常", e);
            throw new RuntimeException("获取输出流异常", e);
        } finally {
            if (excelWriter != null) {
                excelWriter.finish();
            }
        }

    }

    private int compareObjects(Object o1, Object o2) {
        if (o1 == null && o2 == null) {
            return 0;
        }
        if (o1 == null) {
            return 1;
        }
        if (o2 == null) {
            return -1;
        }
        return o1.toString().compareTo(o2.toString());
    }
}




