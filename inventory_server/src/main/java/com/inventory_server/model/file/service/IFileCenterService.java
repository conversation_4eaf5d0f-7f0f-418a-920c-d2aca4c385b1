package com.inventory_server.model.file.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.crafts_mirror.utils.common.entity.MissionCenterVo;
import com.crafts_mirror.utils.utils.RestTemplateUtils;
import com.inventory_server.infrastructures.entity.form.FileMissionForm;
import com.inventory_server.model.file.entity.form.FileMissionPageForm;
import org.springframework.util.MultiValueMap;

/**
 * @Description 文件中心服务
 * <AUTHOR>
 * @Date 2024/5/9 10:36
 **/
public interface IFileCenterService {

    MultiValueMap<String, Object> putFile(byte[] byteArrayResource, String fileName, String key, String expireTime);

    void uploadFile(MultiValueMap<String, Object> httpEntity, RestTemplateUtils restTemplateUtil);

    IPage<MissionCenterVo> getMissionPage(FileMissionPageForm form);

    String uploadMissionStatus(FileMissionForm form);
}
