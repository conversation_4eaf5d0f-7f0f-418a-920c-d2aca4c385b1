package com.purchase.purchase_server.service.replenishment.impl;

import com.alibaba.fastjson2.JSON;
import com.purchase.purchase_server.entity.dataObject.ReplenishmentRulesDO;
import com.purchase.purchase_server.entity.dataObject.VirtualProductDO;
import com.purchase.purchase_server.entity.dto.ProductRulesDto;
import com.purchase.purchase_server.repository.dataRepository.ReplenishmentRulesRepositoryImpl;
import com.purchase.purchase_server.service.AbstractReplenishOrShippingRulesService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import static com.purchase.purchase_server.enums.RepStockingRulesImportHeadEnum.*;

/**
 * <AUTHOR>
 * @description 针对表【cm_replenishment_rules】的数据库操作Service实现
 * @createDate 2024-01-17 10:19:20
 */
@Service(value = "replenishmentRulesService")
@Slf4j
public class ReplenishmentRulesServiceImpl extends AbstractReplenishOrShippingRulesService {

    @Resource
    private ReplenishmentRulesRepositoryImpl replenishmentRulesRepository;

    @Override
    public ProductRulesDto getProductRulesFromDatabase(VirtualProductDO virtualSku) {
        ReplenishmentRulesDO rulesDO = replenishmentRulesRepository.getShippingProjectBaseParamByVirtualSku(virtualSku.getId());
        Optional.ofNullable(rulesDO).orElseThrow(() -> new IllegalArgumentException("虚拟sku：" + virtualSku.getVirtualSku() + "缺少备货规则，请重新导入"));

        return ProductRulesDto.builder()
                 .rulesId(rulesDO.getId())
                 .transitDays(rulesDO.getTransitDays())
                 .purchaseProjectDays(rulesDO.getPurchaseDays())
                 .purchaseCircle(rulesDO.getPurchaseCircle())
                .shippingCircle(rulesDO.getShippingCircle())
                 .headShippingDays(rulesDO.getHeadShippingDays())
                 .shippingFrequency(rulesDO.getShippingCircle())
                 .shippingRatio(rulesDO.getShippingRatio())
                 .safeDays(rulesDO.getSafeDays())
                .build();
    }

    @Override
    public ProductRulesDto getProductRulesFromDatabase(String rulesId) {
        ReplenishmentRulesDO rulesDO = replenishmentRulesRepository.getById(rulesId);
        return ProductRulesDto.builder()
                .rulesId(rulesDO.getId())
                .transitDays(rulesDO.getTransitDays())
                .purchaseProjectDays(rulesDO.getPurchaseDays())
                .purchaseCircle(rulesDO.getPurchaseCircle())
                .shippingCircle(rulesDO.getShippingCircle())
                .headShippingDays(rulesDO.getHeadShippingDays())
                .shippingFrequency(rulesDO.getShippingCircle())
                .shippingRatio(rulesDO.getShippingRatio())
                .safeDays(rulesDO.getSafeDays())
                .build();
    }

    @Override
    public ProductRulesDto getProductRulesFromImportInfo(Map<String, String> stockRulesMap, List<String> warehouseList) {
        Map<String, Double> shippingRatioMap = new HashMap<>(8);
        // 获取发货比例
        for (String warehouse : warehouseList) {
            double warehouseRatio = Double.parseDouble(stockRulesMap.getOrDefault(warehouse, "0").replaceAll("%", ""));
            shippingRatioMap.put(warehouse, warehouseRatio / 100);
        }
        return ProductRulesDto.builder()
                .purchaseProjectDays(Integer.parseInt(stockRulesMap.get(PURCHASE_DAYS.getCode())))
                .transitDays(Integer.parseInt(stockRulesMap.get(TRANSIT_DAYS.getCode())))
                .purchaseCircle(Integer.parseInt(stockRulesMap.get(PURCHASE_CYCLE.getCode())))
                .shippingFrequency(Integer.parseInt(stockRulesMap.get(REP_FREQUENCY.getCode())))
                .shippingRatio(JSON.toJSONString(shippingRatioMap))
                .safeDays(Integer.parseInt(stockRulesMap.get(SAFE_DAYS.getCode())))
                .build();
    }

    @Override
    public ProductRulesDto getProductRulesById(String id) {
        ReplenishmentRulesDO rulesDO = replenishmentRulesRepository.getById(id);

        return ProductRulesDto.builder()
                .rulesId(rulesDO.getId())
                .transitDays(rulesDO.getTransitDays())
                .purchaseProjectDays(rulesDO.getPurchaseDays())
                .purchaseCircle(rulesDO.getPurchaseCircle())
                .headShippingDays(rulesDO.getHeadShippingDays())
                .shippingFrequency(rulesDO.getShippingCircle())
                .shippingRatio(rulesDO.getShippingRatio())
                .safeDays(rulesDO.getSafeDays())
                .build();
    }
}