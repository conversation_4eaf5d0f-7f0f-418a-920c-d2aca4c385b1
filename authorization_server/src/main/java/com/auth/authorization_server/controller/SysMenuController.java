package com.auth.authorization_server.controller;


import com.auth.authorization_server.aop.PreventReSubmit;
import com.auth.authorization_server.enums.MenuFrameEnum;
import com.auth.authorization_server.exception.BaseException;
import com.auth.authorization_server.model.vo.TreeSelect;
import com.auth.authorization_server.req.MenuParams;
import com.auth.authorization_server.domain.dto.MenuDTO;
import com.auth.authorization_server.service.ISysMenuService;
import com.auth.authorization_server.utils.StringUtils;
import com.crafts_mirror.common.security.annotation.RequiresPermissions;
import com.crafts_mirror.common.security.utils.SecurityUtils;
import com.crafts_mirror.utils.enums.ResponseCodeEnum;
import com.crafts_mirror.utils.web.domain.ResultDTO;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * 菜单信息
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/menu")
@Slf4j
public class SysMenuController {
    @Resource
    private ISysMenuService menuService;

    /**
     * 获取菜单列表
     */
    @RequiresPermissions("system:menu:list")
    @GetMapping("/list")
    public ResultDTO<List<MenuDTO>> list(MenuParams menu) {
        String userId = SecurityUtils.getUserId();
        List<MenuDTO> menus = menuService.selectMenuList(menu, userId);
        return ResultDTO.success(menuService.buildMenuTree(menus));
    }

    /**
     * 根据菜单编号获取详细信息
     */
    //@RequiresPermissions("system:menu:query")
    @RequiresPermissions("system:menu:list")
    @GetMapping(value = "/detail/{menuId}")
    public ResultDTO<MenuDTO> getInfo(@PathVariable String menuId) {
        return ResultDTO.success(menuService.selectMenuById(menuId));
    }

    /**
     * 获取所有菜单树
     */
    @GetMapping("/info/listToTree")
    public ResultDTO<List<TreeSelect>> listToTree(MenuParams menu) {
        List<MenuDTO> menus = menuService.selectMenuList(menu, "1");
        return ResultDTO.success(menuService.buildMenuTreeSelect(menus));
    }

    /**
     * 获取菜单下拉树列表-角色管理用
     */
    @GetMapping("/info/treeselect")
    public ResultDTO<List<TreeSelect>> treeselect(MenuParams menu) {
        String userId = SecurityUtils.getUserId();
        List<MenuDTO> menus = menuService.selectMenuList(menu, userId);
        return ResultDTO.success(menuService.buildMenuTreeSelect(menus));
    }

    /**
     * 加载对应角色菜单列表树
     */
    @GetMapping(value = "/info/roleMenuTreeselect/{roleId}")
    public ResultDTO<Map<String, Object>> roleMenuTreeselect(@PathVariable("roleId") String roleId) {
        String userId = SecurityUtils.getUserId();
        List<MenuDTO> menus = menuService.selectMenuList(userId);
        Map<String, Object> ajax = new HashMap<>();
        ajax.put("checkedKeys", menuService.selectMenuListByRoleId(roleId));
        ajax.put("menus", menuService.buildMenuTreeSelect(menus));
        return ResultDTO.success(ajax);
    }

    /**
     * 新增菜单
     */
    //@RequiresPermissions("system:menu:add")
    @RequiresPermissions("system:menu:list")
    @PostMapping("/insert")
    @PreventReSubmit
    public ResultDTO<Boolean> add(@Valid @RequestBody MenuParams menu) {
        if (!menuService.checkMenuNameUnique(menu)) {
            throw new BaseException(ResponseCodeEnum.BAD_REQUEST, "新增菜单'" + menu.getMenuName() + "'失败，菜单名称已存在");
        } else if (MenuFrameEnum.YES_FRAME.getCode().equals(menu.getIsFrame()) && !StringUtils.ishttp(menu.getPath())) {
            throw new BaseException(ResponseCodeEnum.BAD_REQUEST, "新增菜单'" + menu.getMenuName() + "'失败，地址必须以http(s)://开头");
        }

        return ResultDTO.success(menuService.insertMenu(menu));
    }

    /**
     * 修改菜单
     */
    //@RequiresPermissions("system:menu:edit")
    @RequiresPermissions("system:menu:list")
    @PutMapping("/update")
    @PreventReSubmit
    public ResultDTO<Boolean> edit(@Valid @RequestBody MenuParams menu) {
        if (!menuService.checkMenuNameUnique(menu)) {
            throw new BaseException(ResponseCodeEnum.BAD_REQUEST, "修改菜单'" + menu.getMenuName() + "'失败，菜单名称已存在");
        } else if (MenuFrameEnum.YES_FRAME.getCode().equals(menu.getIsFrame()) && !StringUtils.ishttp(menu.getPath())) {
            throw new BaseException(ResponseCodeEnum.BAD_REQUEST, "修改菜单'" + menu.getMenuName() + "'失败，地址必须以http(s)://开头");
        } else if (menu.getId().equals(menu.getParentId())) {
            throw new BaseException(ResponseCodeEnum.BAD_REQUEST, "修改菜单'" + menu.getMenuName() + "'失败，上级菜单不能选择自己");
        }
        return ResultDTO.success(menuService.updateMenu(menu));
    }

    /**
     * 状态修改
     */
    //@RequiresPermissions("system:user:edit")
    @RequiresPermissions("system:menu:list")
    @PutMapping("/update/changeStatus")
    @PreventReSubmit
    public ResultDTO<Boolean> changeStatus(@RequestBody MenuParams menu) {
        return ResultDTO.success(menuService.updateMenuStatus(menu));
    }

    /**
     * 删除菜单
     */
    //@RequiresPermissions("system:menu:remove")
    @RequiresPermissions("system:menu:list")
    @DeleteMapping("/delete/{menuId}")
    @PreventReSubmit
    public ResultDTO<Boolean> remove(@PathVariable("menuId") String menuId) {
        if (menuService.hasChildByMenuId(menuId)) {
            throw new BaseException(ResponseCodeEnum.BAD_REQUEST, "存在子菜单,不允许删除");
        }
        if (menuService.checkMenuExistRole(menuId)) {
            throw new BaseException(ResponseCodeEnum.BAD_REQUEST, "菜单已分配,不允许删除");
        }
        return ResultDTO.success(menuService.deleteMenuById(menuId));
    }

    ///**
    // * 获取路由信息
    // *
    // * @return 路由信息
    // */
    //@GetMapping("/getRouters")
    //public ResultDTO<List<RouterVo>> getRouters() {
    //    String userId = SecurityUtils.getUserId();
    //    List<MenuDTO> menus = menuService.selectMenuTreeByUserId(userId);
    //    return ResultDTO.success(menuService.buildMenus(menus));
    //}
}