package com.purchase.purchase_server.entity.form;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDate;

/**
 * <AUTHOR>
 * @Date 2025/5/27 17:04
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class LclUpdateShippingStartDateForm implements Serializable {

    private LocalDate shippingDateStart;

    private LocalDate shippingDateEnd;

    private LocalDate changingShippingDate;

    private String containerId;
}
