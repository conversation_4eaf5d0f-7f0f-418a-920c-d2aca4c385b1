package com.file.system.file_system.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.file.system.file_system.entity.form.FileMissionForm;
import com.file.system.file_system.entity.form.FileMissionPageForm;
import com.file.system.file_system.entity.response.ResultDTO;
import com.file.system.file_system.entity.vo.MissionCenterVo;
import com.file.system.file_system.service.IFileMissionCenterService;
import com.file.system.file_system.service.IOssFileService;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;


/**
 * @Description 文档中心controller层
 * <AUTHOR>
 * @Date 2023/12/11 14:11
 **/
@RestController
@RequestMapping("/center/interior")
public class FileCenterInteriorController {

    @Resource
    private IFileMissionCenterService fileMissionCenterService;

    @Resource
    private IOssFileService ossFileService;

    @PostMapping("mission")
    public ResultDTO<String> saveFileMission(@RequestBody FileMissionForm form) {
        String missionId = fileMissionCenterService.saveFileMission(form);
        return ResultDTO.success("ok", missionId);
    }

    @PostMapping("putObject/file")
    public ResultDTO<String> putObjectFile(@RequestParam("avatar") MultipartFile avatarFile, String key, String expireTime) {
        return ResultDTO.success(ossFileService.putObject(avatarFile, key, expireTime));
    }

    @GetMapping("putObject/url")
    public ResultDTO<String> putObjectUrl(@RequestParam("networkUrl") String networkUrl, @RequestParam("url") String key, @RequestParam("expireTime") String expireTime) {
        return ResultDTO.success("success", ossFileService.putObject(key, networkUrl, expireTime));
    }

    @GetMapping("getUrl")
    public ResultDTO<String> getUrl(String url) {
        return ResultDTO.success("success", ossFileService.getUrl(url));
    }

    @PostMapping("list")
    @ResponseBody
    public MissionCenterVo getMissionList(@RequestBody FileMissionPageForm form) {
        return fileMissionCenterService.getMissionPage(form).getRecords().getFirst();
    }

    @PostMapping("delete")
    @ResponseBody
    public void deleteMission(@RequestBody FileMissionForm form) {
        fileMissionCenterService.deleteMission(form);
    }

    @PostMapping("pageList")
    @ResponseBody
    public ResultDTO<IPage<MissionCenterVo>> getMissionPage(@RequestBody FileMissionPageForm form) {
        return ResultDTO.success(fileMissionCenterService.getMissionPage(form));
    }

    @PostMapping("doesObjectExist")
    @ResponseBody
    public ResultDTO<Boolean> doesObjectExist(@RequestBody FileMissionForm form) {
        return ResultDTO.success(ossFileService.doesObjectExist(form.getFilePath()));
    }
}
