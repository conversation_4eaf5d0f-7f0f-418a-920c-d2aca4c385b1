package com.inventory_server.model.warehouse.entity.dto;

import com.alibaba.fastjson2.annotation.JSONField;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @Description 易仓获取的仓库详情
 * <AUTHOR>
 * @Date 2024/7/3 13:57
 **/
@Data
@Builder
public class WarehouseDetailDto implements Serializable {
    /**
     * 易仓仓库id
     */
    @JSONField(name = "warehouse_id")
    private Integer ycWarehouseId;
    /**
     * 易仓仓库代码
     */
    @JSONField(name = "warehouse_code")
    private String ycWarehouseCode;
    /**
     * 仓库名称
     */
    @JSONField(name = "warehouse_desc")
    private String ycWarehouseDesc;
    /**
     * 仓库类型：0标准，1中转
     */
    @JSONField(name = "warehouse_type")
    private Integer warehouseType;
    /**
     * 状态：-1已废弃,0不可用,1可用
     */
    @JSONField(name = "warehouse_status")
    private Integer warehouseStatus;
    /**
     * 运营方式：0自营,1第三方
     */
    @JSONField(name = "warehouse_virtual")
    private Integer warehouseVirtual;
    /**
     * 第三方仓储服务
     */
    @JSONField(name = "warehouse_service")
    private String warehouseService;
    /**
     * 是否需要头程（应用于库存成本是否包含头程费用）:0否,1是
     */
    @JSONField(name = "is_transfer")
    private Integer isTransfer;
    /**
     * 国家二字码
     */
    @JSONField(name = "country_code")
    private String countryCode;
    /**
     * 国家ID
     */
    @JSONField(name = "country_id")
    private Integer countryId;
    /**
     * 省份
     */
    private String state;
    /**
     * 城市
     */
    private String city;
    /**
     * 联系人
     */
    @JSONField(name = "contacter")

    private String contactor;
    /**
     * 公司名称
     */
    private String company;
    /**
     * 电话
     */
    private String phoneNo;
    /**
     * 地址1
     */
    @JSONField(name = "street_address1")
    private String streetAddress1;
    /**
     * 地址2
     */
    @JSONField(name = "street_address2")
    private String streetAddress2;
    /**
     * 邮编
     */
    private String postcode;
    /**
     * 添加时间
     */
    @JSONField(name = "warehouse_add_time")
    private LocalDateTime warehouseAddTime;
    /**
     * 最后更新时间
     */
    @JSONField(name = "warehouse_update_time")
    private LocalDateTime warehouseUpdateTime;
}
