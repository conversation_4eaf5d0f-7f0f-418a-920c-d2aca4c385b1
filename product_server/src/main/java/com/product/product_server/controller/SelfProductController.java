package com.product.product_server.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.crafts_mirror.common.security.annotation.RequiresPermissions;
import com.crafts_mirror.utils.dp.LogTrackNumDto;
import com.crafts_mirror.utils.enums.ResponseCodeEnum;
import com.crafts_mirror.utils.utils.JwtUtil;
import com.crafts_mirror.utils.web.domain.ResultDTO;
import com.product.product_server.entity.LogTrackNumMapDto;
import com.product.product_server.entity.dto.ProductCategoryDTO;
import com.product.product_server.entity.form.RemarksSaveForm;
import com.product.product_server.entity.form.SelfProductForm;
import com.product.product_server.entity.form.SelfProductUpdateForm;
import com.product.product_server.entity.vo.*;
import com.product.product_server.service.ISelfProductService;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;

import static com.crafts_mirror.utils.constant.Constants.AUTHORIZATION;

/**
 * @Description 产品管理Web层
 * <AUTHOR>
 * @Date 2023/12/6 10:48
 **/
@RestController
@RequestMapping(value = "selfProduct")
@Slf4j
public class SelfProductController {

    @Resource
    private ISelfProductService selfProductService;

    @RequiresPermissions("product:selfProduct:list")
    @PostMapping("/pageList")
    @ResponseBody
    public ResultDTO<IPage<SelfProductListVo>> productListData(@RequestBody SelfProductForm form) {
        IPage<SelfProductListVo> selfProductVo = selfProductService.pageList(form);
        return ResultDTO.success(selfProductVo);
    }

    @RequiresPermissions("product:selfProduct:list")
    @GetMapping("/detail")
    @ResponseBody
    public ResultDTO<SelfProductDetailVo> productDetail(@RequestParam("selfProductId") String selfProductId) {
        SelfProductDetailVo detail = selfProductService.getDetail(selfProductId);
        return ResultDTO.success(detail);
    }

    @RequiresPermissions("product:selfProduct:list")
    @PostMapping("/import/product")
    public ResultDTO<String> importProductInfo(@RequestParam("file") MultipartFile file) {
        log.warn("导入自定义商品-------------准备开始导入日志");
        try {
            selfProductService.importProductInfo(file.getInputStream(),file.getBytes(), file.getOriginalFilename());
        } catch (IOException e) {
            log.error("获取输入流异常：", e);
            throw new RuntimeException(e);
        }
        log.warn("导入自定义商品-------------导入成功，返回消息至前端");
        return ResultDTO.success("正在导入");
    }

    @RequiresPermissions("product:selfProduct:list")
    @PostMapping("/update/commodityInspection")
    public ResultDTO<String> updateCommodityInspection(@RequestParam("file") MultipartFile file) {
        log.warn("导入更新属性-------------准备开始导入日志");
        try {
            selfProductService.updateSelfProductCommodityInspection(file.getInputStream(),file.getBytes(), file.getOriginalFilename());
        } catch (IOException e) {
            log.error("获取输入流异常：", e);
            throw new RuntimeException(e);
        }
        log.warn("导入更新属性-------------导入成功，返回消息至前端");
        return ResultDTO.success("正在导入");
    }

    @GetMapping ("buyer/set")
    @ResponseBody
    public ResultDTO<List<BuyerSearchVo>> getBuyerSet() {
        return ResultDTO.success(selfProductService.getBuyerSet());
    }

    @RequiresPermissions("product:selfProduct:list")
    @PostMapping ("save/remarks")
    @ResponseBody
    public ResultDTO<Boolean> saveRemarks(@RequestBody RemarksSaveForm form) {
        try {
            return ResultDTO.success(selfProductService.saveSelfProductRemarks(form, new LogTrackNumDto(form.getId())));
        } catch (NullPointerException e) {
            return ResultDTO.error(ResponseCodeEnum.ERROR.getCode(), e.getMessage());
        }
    }

    @RequiresPermissions("product:selfProduct:delete")
    @GetMapping ("delete/selfProduct")
    @ResponseBody
    public ResultDTO<Boolean> deleteSelfProduct(@RequestParam("selfProductId") String selfProductId) {
        try {
            return ResultDTO.success(selfProductService.deleteSelfProduct(selfProductId, new LogTrackNumDto(selfProductId)));
        } catch (NullPointerException e) {
            return ResultDTO.error(ResponseCodeEnum.ERROR.getCode(), e.getMessage());
        }
    }

    @RequiresPermissions("product:selfProduct:delete")
    @PostMapping ("delete/selfProductBatch")
    @ResponseBody
    public ResultDTO<Boolean> deleteSelfProductBatch(@RequestBody SelfProductForm form) {
        try {
            return ResultDTO.success(selfProductService.deleteSelfProductBatch(form.getSelfSkuIdList(), new LogTrackNumMapDto()));
        } catch (NullPointerException e) {
            return ResultDTO.error(ResponseCodeEnum.ERROR.getCode(), e.getMessage());
        }
    }

    @RequiresPermissions("product:selfProduct:list")
    @PostMapping("/update")
    @ResponseBody
    public ResultDTO<SelfProductUpdateVo> updateSelfProduct(@RequestBody SelfProductUpdateForm form, HttpServletRequest request) {
        String authorization = request.getHeader(AUTHORIZATION);
        String username = JwtUtil.getClaimsByToken(authorization).get("username", String.class);
        try {
            return ResultDTO.success(selfProductService.updateSelfProduct(form,username));
        } catch (NullPointerException e) {
            return ResultDTO.error(ResponseCodeEnum.ERROR.getCode(), e.getMessage());
        }
    }

    @RequiresPermissions("product:selfProduct:list")
    @PostMapping("/update/check")
    @ResponseBody
    public ResultDTO<SelfProductUpdateVo> updateBeforeCheck(@RequestBody SelfProductUpdateForm form) {
        try {
            return ResultDTO.success(selfProductService.updateBeforeCheck(form));
        } catch (NullPointerException e) {
            return ResultDTO.error(ResponseCodeEnum.ERROR.getCode(), e.getMessage());
        }
    }
    
    @RequiresPermissions("product:selfProduct:list")
    @GetMapping ("/update/virtual/list")
    @ResponseBody
    public ResultDTO<List<SelfAndVirtualVo>> virtualList(@RequestParam("selfProductId") String selfProductId) {
        try {
            return ResultDTO.success(selfProductService.virtualList(selfProductId));
        } catch (NullPointerException e) {
            return ResultDTO.error(ResponseCodeEnum.ERROR.getCode(), e.getMessage());
        }
    }

    /**
     * 导出
     * @param form
     * @param response
     */
    @RequiresPermissions("product:selfProduct:export")
    @PostMapping("/export")
    public void exportRepInfo(@RequestBody SelfProductForm form, HttpServletResponse response) {
        selfProductService.exportInfo(form, response);
    }

    /**
     * 获取产品品类列表
     */
    @RequiresPermissions("product:selfProduct:list")
    @GetMapping("/category")
    public ResultDTO<List<ProductCategoryDTO>> categoryList() {
        return ResultDTO.success(selfProductService.category());
    }
}
