package com.purchase.purchase_server.utils.easyExcelUtil.listener.Shipment;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.context.AnalysisContext;
import com.crafts_mirror.utils.provider.ApplicationContextProvider;
import com.purchase.purchase_server.assembler.ShipmentAssembler;
import com.purchase.purchase_server.entity.LogTrackNumDto;
import com.purchase.purchase_server.entity.dataObject.ShipmentPlanDO;
import com.purchase.purchase_server.entity.dto.SenboWarehouseDto;
import com.purchase.purchase_server.entity.dto.Shipment.ShipmentPlanDto;
import com.purchase.purchase_server.entity.excelObject.ShipmentPlanExcel;
import com.purchase.purchase_server.enums.shipment.ShipmentTypeEnum;
import com.purchase.purchase_server.repository.dataRepository.Shipment.ShipmentPlanRepositoryImpl;
import lombok.extern.slf4j.Slf4j;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Description 导入货件计划
 * <AUTHOR>
 * @Date 2024/11/18 15:49
 **/
@Slf4j
public class ShipmentPlanImportListener extends AbstractImportListener<ShipmentPlanExcel> {

    protected static final Integer TOTAL_ROW_NUMBER = 10000;
    private final Map<String, String> warehouseNameIdMap;

    private final List<ShipmentPlanDO> shipmentPlanDOList;
    private final ShipmentPlanRepositoryImpl shipmentPlanRepository = ApplicationContextProvider.getBean(ShipmentPlanRepositoryImpl.class);

    private final ShipmentAssembler shipmentAssembler = ApplicationContextProvider.getBean(ShipmentAssembler.class);



    public ShipmentPlanImportListener(List<ShipmentPlanDO> shipmentPlanDOList,List<SenboWarehouseDto> senboWarehouseList, List<String> errorList) {
        super(errorList);
        this.shipmentPlanDOList = shipmentPlanDOList;
        this.warehouseNameIdMap = senboWarehouseList.stream().collect(Collectors.toMap(SenboWarehouseDto::getSenboWarehouse, SenboWarehouseDto::getSenboWarehouseId));

    }

    @Override
    public void invoke(ShipmentPlanExcel data, AnalysisContext analysisContext) {
        Integer approximateRowNumber = analysisContext.readSheetHolder().getApproximateTotalRowNumber();
        if (approximateRowNumber != null && approximateRowNumber > TOTAL_ROW_NUMBER) {
            throw new RuntimeException(String.format("导入数据超过%d行，请缩小导入数据量", TOTAL_ROW_NUMBER));
        }
        // 判空
        check(data);
        String warehouseName =  warehouseNameIdMap.entrySet().stream()
                    .filter(entry -> entry.getKey().equalsIgnoreCase(data.getWarehouseName()))
                    .map(Map.Entry::getKey)
                    .findFirst()
                    .orElse(null);

        if (StrUtil.isBlank(warehouseName)) {
            throw new IllegalArgumentException("位置填写错误");
        }

        ShipmentPlanDO shipmentPlanDO = shipmentAssembler.planExcelToDO(data);
        List<ShipmentPlanDO> shipmentPlanList = shipmentPlanRepository.list(ShipmentPlanDto.builder().shipmentCode(data.getShipmentCode()).build());

        if (CollectionUtil.isNotEmpty(shipmentPlanList)){
            shipmentPlanDO.setId(shipmentPlanList.getFirst().getId());
        }
        shipmentPlanDO.setWarehouseId(warehouseNameIdMap.get(warehouseName));
        shipmentPlanDO.setWarehouseName(warehouseName);
        shipmentPlanDO.setShipmentType(ShipmentTypeEnum.ofDesc(data.getShipmentType()).getCode());
        shipmentPlanRepository.saveOrUpdatePlan(shipmentPlanDO, new LogTrackNumDto());
        shipmentPlanDOList.add(shipmentPlanDO);
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext analysisContext) {

    }

    /**
     * 这里会一行行的返回头
     *
     * @param headMap 表头map
     * @param context 上下文
     */
    @Override
    public void invokeHeadMap(Map<Integer, String> headMap, AnalysisContext context) {
        //删除headMap中的空值
        headMap.entrySet().removeIf(entry -> entry.getValue() == null || entry.getValue().isEmpty());
        List<String> headMapList = new ArrayList<>(headMap.values());

        List<String> headList = new ArrayList<>();
        Field[] fields = ShipmentPlanExcel.class.getDeclaredFields();
        for (Field field : fields) {
            if (field.isAnnotationPresent(ExcelProperty.class)) {
                ExcelProperty declaredAnnotation = field.getDeclaredAnnotation(ExcelProperty.class);
                String headValue = declaredAnnotation.value()[0];
                headList.add(headValue);
            }
        }
        if (!headMapList.equals(headList)) {
            throw new RuntimeException("导入表头不正确");
        }
    }
}
