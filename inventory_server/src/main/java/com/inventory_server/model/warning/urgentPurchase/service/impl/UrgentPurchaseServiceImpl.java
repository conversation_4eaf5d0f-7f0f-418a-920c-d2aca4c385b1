package com.inventory_server.model.warning.urgentPurchase.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.excel.write.style.HorizontalCellStyleStrategy;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.TypeReference;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.inventory_server.infrastructures.handler.ExcelWidthStyleStrategy;
import com.inventory_server.model.channel.service.IChannelInfoService;
import com.inventory_server.model.product.entity.dos.SelfProductDO;
import com.inventory_server.model.product.entity.dos.VirtualProductDO;
import com.inventory_server.model.product.entity.vo.UserInteriorVO;
import com.inventory_server.model.product.repository.databaseRepository.VirtualProductRepositoryImpl;
import com.inventory_server.model.product.repository.interiorRepository.IProductCategoryRepository;
import com.inventory_server.model.product.repository.interiorRepository.ISnapshotRepository;
import com.inventory_server.model.redundancy.entity.dos.InventorySaleRulesDO;
import com.inventory_server.model.redundancy.entity.dto.InterventionalTimeDto;
import com.inventory_server.model.redundancy.entity.dto.ProductCategoryDTO;
import com.inventory_server.model.redundancy.entity.vo.NormalDeliveryWatchBoardVo;
import com.inventory_server.model.redundancy.service.IInventorySaleRulesService;
import com.inventory_server.model.redundancy.service.IRedundantInventoryService;
import com.inventory_server.model.redundancy.service.InterventionalTimeService;
import com.inventory_server.model.system.service.ISysUserInteriorService;
import com.inventory_server.model.warehouse.entity.dto.SenboWarehouseDto;
import com.inventory_server.model.warehouse.service.IWarehouseService;
import com.inventory_server.model.warning.unsalable.entity.form.UnsalablePageForm;
import com.inventory_server.model.warning.urgentPurchase.constants.UrgentPurchaseConstants;
import com.inventory_server.model.warning.urgentPurchase.entity.dos.WarningUrgentPurchaseDO;
import com.inventory_server.model.warning.urgentPurchase.entity.excel.UrgentPurchaseExcel;
import com.inventory_server.model.warning.urgentPurchase.entity.form.UrgentPurchaseMQForm;
import com.inventory_server.model.warning.urgentPurchase.entity.vo.UrgentPurchasePageVo;
import com.inventory_server.model.warning.urgentPurchase.repository.dateRepository.UrgentPurchaseRepository;
import com.inventory_server.model.warning.urgentPurchase.service.IUrgentPurchaseSaveService;
import com.inventory_server.model.warning.urgentPurchase.service.IUrgentPurchaseService;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.stream.Collectors;

import static com.crafts_mirror.utils.constant.MQConstants.URGENT_PURCHASE_TOPIC;
import static com.crafts_mirror.utils.enums.product.VirtualProductStatusEnum.NEW_ARRIVAL_NORMAL_SAMPLE;
import static com.crafts_mirror.utils.enums.product.VirtualProductStatusEnum.NORMAL;
import static com.crafts_mirror.utils.utils.DateUtils.YYYY_MM_DD_DATE_FORMAT_HYPHEN_FORMAT;
import static com.crafts_mirror.utils.utils.DateUtils.YYYY_MM_DD_DATE_FORMAT_SLASH_FORMAT;
import static java.math.RoundingMode.HALF_UP;

/**
 * @Description 加急补货Service层
 * <AUTHOR>
 * @Date 2025/4/21 13:45
 **/
@Service
@Slf4j
public class UrgentPurchaseServiceImpl implements IUrgentPurchaseService {

    @Resource
    private InterventionalTimeService interventionalTimeService;

    @Resource
    private VirtualProductRepositoryImpl virtualProductRepository;

    @Resource
    private ISnapshotRepository snapshotRepository;

    @Resource
    private IRedundantInventoryService redundantInventoryService;

    @Resource
    private IUrgentPurchaseSaveService urgentPurchaseSaveService;

    @Resource
    private UrgentPurchaseRepository urgentPurchaseRepository;

    @Resource
    private IProductCategoryRepository productCategoryRepository;

    @Resource
    private ISysUserInteriorService sysUserInteriorService;

    @Resource
    private IInventorySaleRulesService inventorySaleRulesService;

    @Resource
    private IWarehouseService warehouseService;

    @Resource
    private IChannelInfoService channelInfoService;

    @Resource
    private RocketMQTemplate rocketMQTemplate;

    @Resource(name = "reCalUrgentPurchaseThreadPool1")
    private Executor reCalUrgentPurchaseThreadPool1;

    @Resource(name = "reCalUrgentPurchaseThreadPool2")
    private Executor reCalUrgentPurchaseThreadPool2;

    @Resource
    private UrgentPurchaseConstants urgentPurchaseConstants;

    @Override
    public void calUrgentPurchaseNum(UrgentPurchaseMQForm purchaseForm) {
        // 可干预时间
        LocalDate calFinishedDate = purchaseForm.getCalFinishedDate();
        InventorySaleRulesDO salesRules = purchaseForm.getSalesRules();
        InterventionalTimeDto interventionalTimeDto = interventionalTimeService.calculateInterventionalTime(
                calFinishedDate, salesRules);

        LocalDate normalPurchaseDate = LocalDate.parse(interventionalTimeDto.getNormalPurchaseDate(), YYYY_MM_DD_DATE_FORMAT_HYPHEN_FORMAT);
        LocalDate urgentPurchaseDate = LocalDate.parse(interventionalTimeDto.getUrgentPurchaseDate(), YYYY_MM_DD_DATE_FORMAT_HYPHEN_FORMAT);
        LocalDate urgentCalEndDate;
        if (normalPurchaseDate.isEqual(urgentPurchaseDate)) {
            urgentCalEndDate = urgentPurchaseDate;
        } else {
            String redundantDays = urgentPurchaseConstants.getRedundantDays();
            urgentCalEndDate = normalPurchaseDate.plusDays(Integer.parseInt(redundantDays));
        }

        var everydaySaleProductMap = purchaseForm.getNormalDeliveryResultDto().getEverydaySaleProductMap();
        BigDecimal totalSales = everydaySaleProductMap.entrySet().stream()
                .filter(f -> {
                    LocalDate localDate;
                    if (f.getKey().contains("-")) {
                        localDate = LocalDate.parse(f.getKey(), YYYY_MM_DD_DATE_FORMAT_HYPHEN_FORMAT);
                    } else {
                        localDate = LocalDate.parse(f.getKey(), YYYY_MM_DD_DATE_FORMAT_SLASH_FORMAT);
                    }
                    return !urgentPurchaseDate.isAfter(localDate) && localDate.isBefore(normalPurchaseDate);
                })
                .flatMap(entry -> entry.getValue().values().stream())
                .map(BigDecimal::valueOf)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        BigDecimal totalTargetSales = purchaseForm.getTargetSalesMap().entrySet().stream()
                .filter(f -> {
                    LocalDate localDate = LocalDate.parse(f.getKey(), YYYY_MM_DD_DATE_FORMAT_SLASH_FORMAT);
                    return !urgentPurchaseDate.isAfter(localDate) && localDate.isBefore(normalPurchaseDate);
                })
                .map(Map.Entry::getValue)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal saleDifference = totalTargetSales.subtract(totalSales);
        int urgentPurchaseNum = 0;
        if (saleDifference.compareTo(BigDecimal.ZERO) > 0) {
            BigDecimal redundancyPurchase = purchaseForm.getTargetSalesMap().entrySet().stream()
                    .filter(f -> {
                        LocalDate localDate = LocalDate.parse(f.getKey(), YYYY_MM_DD_DATE_FORMAT_SLASH_FORMAT);
                        return !normalPurchaseDate.isAfter(localDate) && localDate.isBefore(urgentCalEndDate);
                    })
                    .map(Map.Entry::getValue)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            SelfProductDO selfProduct = snapshotRepository.getSelfProductSnapshotBySnapshotId(purchaseForm.getSnapShotId());
            Integer containerLoad = selfProduct.getContainerLoad();
            saleDifference = saleDifference.add(redundancyPurchase);

            // 凑整箱
            BigDecimal[] bigDecimals = saleDifference.divideAndRemainder(BigDecimal.valueOf(containerLoad));
            BigDecimal reminder = bigDecimals[1];
            saleDifference = reminder.compareTo(BigDecimal.ZERO) == 0 ? saleDifference : saleDifference.add(BigDecimal.valueOf(containerLoad)).subtract(reminder);
            urgentPurchaseNum = saleDifference.setScale(0, HALF_UP).intValue();
        }

        // 加急采购下单天数为1天
        int purchaseDays = 1;
        int produceDays = salesRules.getUrgentProduceDays();
        LocalDate produceEndDate = calFinishedDate.plusDays(purchaseDays + produceDays);

        // 不计算产品状态为新品不断货以及正常款之外的加急补货数据，因此此处将加急补货量置0，页面搜索时会过滤掉
        VirtualProductDO virtualProduct = snapshotRepository.getVirtualProductSnapshotBySnapshotId(purchaseForm.getSnapShotId());
        if (!virtualProduct.getProductStatus().equals(NEW_ARRIVAL_NORMAL_SAMPLE.getCode()) && !virtualProduct.getProductStatus().equals(NORMAL.getCode())) {
            urgentPurchaseNum = 0;
        }

        WarningUrgentPurchaseDO urgentPurchaseDO = WarningUrgentPurchaseDO.builder()
                .inventoryInfoId(purchaseForm.getInventoryInfoId())
                .snapshotId(purchaseForm.getSnapShotId())
                .urgentFactoryFinishedDate(produceEndDate)
                .urgentPurchaseNum(urgentPurchaseNum)
                .urgentPurchaseDateRange(urgentPurchaseDate + " ~ " + urgentCalEndDate.minusDays(1))
                .urgentPurchaseDate(calFinishedDate)
                .build();
        urgentPurchaseSaveService.saveUrgentPurchase(urgentPurchaseDO);
    }

    public IPage<UrgentPurchasePageVo> getUrgentPurchasePage(UnsalablePageForm form) {
        String childCategoryId = getChildCategoryId(form.getCategoryId());
        form.setCategoryId(childCategoryId);
        IPage<UrgentPurchasePageVo> unsalableWaringPage = urgentPurchaseRepository.getUrgentPurchasePage(form);
        convertUnsalableInventoryList(unsalableWaringPage.getRecords());
        return unsalableWaringPage;
    }

    @Override
    public List<UrgentPurchasePageVo> getUrgentPurchaseList(UnsalablePageForm form) {
        String childCategoryId = getChildCategoryId(form.getCategoryId());
        form.setCategoryId(childCategoryId);
        List<UrgentPurchasePageVo> unsalableWaringList = urgentPurchaseRepository.getUrgentPurchaseList(form);
        convertUnsalableInventoryList(unsalableWaringList);
        return unsalableWaringList;
    }

    @Override
    public void exportUrgentPurchase(List<UrgentPurchasePageVo> urgentPurchaseList, HttpServletResponse response) {
        List<UrgentPurchaseExcel> urgentPurchaseExcelList = new ArrayList<>(urgentPurchaseList.size());
        for (var urgentPurchase : urgentPurchaseList) {
            urgentPurchaseExcelList.add(new UrgentPurchaseExcel(urgentPurchase));
        }

        ExcelWriter excelWriter = null;
        try {
            response.setContentType("application/vnd.ms-excel");
            response.setCharacterEncoding("utf-8");
            String fileName = URLEncoder.encode("加急补货预警" + DateUtil.today(), StandardCharsets.UTF_8).replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
            excelWriter = EasyExcel.write(response.getOutputStream()).build();
            WriteCellStyle headWriteCellStyle = new WriteCellStyle();
            WriteCellStyle contentWriteCellStyle = new WriteCellStyle();
            contentWriteCellStyle.setWrapped(true);
            HorizontalCellStyleStrategy horizontalCellStyleStrategy = new HorizontalCellStyleStrategy(headWriteCellStyle, contentWriteCellStyle);

            WriteSheet writeSheet1 = EasyExcel.writerSheet(1, "sheet").head(UrgentPurchaseExcel.class)
                    .registerWriteHandler(new ExcelWidthStyleStrategy())
                    .registerWriteHandler(horizontalCellStyleStrategy).build();
            excelWriter.write(urgentPurchaseExcelList, writeSheet1);

        } catch (Exception e) {
            log.error("获取输出流异常", e);
            throw new RuntimeException("获取输出流异常", e);
        } finally {
            if (excelWriter != null) {
                excelWriter.finish();
            }
        }
    }

    @Override
    public void reCalUrgentPurchaseByWarehouse(String warehouse) {
        List<InventorySaleRulesDO> saleRulesDOList = inventorySaleRulesService.getAllRatioBiggerThanZeroWarehouseSaleRules(warehouse);
        Map<String, InventorySaleRulesDO> saleRulesMap = saleRulesDOList.stream()
                .collect(Collectors.toMap(InventorySaleRulesDO::getInventoryInfoId, m -> m));
        // 需要重新计算加急补货的数据
        List<WarningUrgentPurchaseDO> urgentPurchaseList = urgentPurchaseRepository.getUrgentPurchaseListByInventoryIdCollection(saleRulesMap.keySet());

        List<SenboWarehouseDto> senboWarehouseList = warehouseService.getSenboWarehouseListWithoutMiddleTransit();
        List<CompletableFuture<Void>> allFutures = urgentPurchaseList.stream()
                .map(urgentPurchase -> {
                    String inventoryInfoId = urgentPurchase.getInventoryInfoId();
                    // 第一阶段：异步获取NormalDelivery数据
                    CompletableFuture<NormalDeliveryWatchBoardVo> normalDeliveryFuture = CompletableFuture
                            .supplyAsync(() -> redundantInventoryService.getNormalShippingCalculation(inventoryInfoId, senboWarehouseList),
                                    reCalUrgentPurchaseThreadPool1);

                    // 第二阶段：同步处理后续逻辑
                    return normalDeliveryFuture.thenAcceptAsync(watchBoardVo -> {
                        InventorySaleRulesDO inventorySale = saleRulesMap.get(inventoryInfoId);
                        Map<String, BigDecimal> targetSalesMap = JSON.parseObject(inventorySale.getSaleDestination(), new TypeReference<>() {
                        });

                        // 构建MQ消息
                        UrgentPurchaseMQForm mqForm = UrgentPurchaseMQForm.builder()
                                .snapShotId(urgentPurchase.getSnapshotId())
                                .inventoryInfoId(inventoryInfoId)
                                .targetSalesMap(targetSalesMap)
                                .calFinishedDate(urgentPurchase.getUrgentPurchaseDate())
                                .salesRules(inventorySale)
                                .normalDeliveryResultDto(watchBoardVo.getDeliveryCalResultDto())
                                .build();

                        // 发送MQ
                        rocketMQTemplate.convertAndSend(URGENT_PURCHASE_TOPIC, mqForm);
                    }, reCalUrgentPurchaseThreadPool2); // 使用相同线程池继续执行
                })
                .toList();

        // 等待所有任务完成
        CompletableFuture.allOf(allFutures.toArray(new CompletableFuture[0])).join();
    }

    private void convertUnsalableInventoryList(List<UrgentPurchasePageVo> urgentPurchaseList) {
        // 获取用户信息
        List<UserInteriorVO> userList = sysUserInteriorService.getUserList();
        Map<String, String> collect = userList.stream().collect(Collectors.toMap(UserInteriorVO::getUserName, UserInteriorVO::getNickName));

        // 获取大类信息
        Map<String, ProductCategoryDTO> categoryDTOMap = productCategoryRepository.getCategoryDTOMap();

        //工厂库存,海外仓在途，海外仓库存
        List<String> inventoryInfoList = urgentPurchaseList.stream().map(UrgentPurchasePageVo::getInventoryInfoId).toList();
        List<InventorySaleRulesDO> saleRuleList = inventorySaleRulesService.getInventorySaleByInfoIds(inventoryInfoList);
        Map<String, InventorySaleRulesDO> saleRuleMap = saleRuleList.stream().collect(Collectors.toMap(InventorySaleRulesDO::getInventoryInfoId, i -> i));

        // 获取渠道信息
        Map<String, String> channelIdNameMap = channelInfoService.getChannelIdNameMap();

        for (var vo : urgentPurchaseList) {
            String operator = vo.getOperator();
            if (StrUtil.isNotBlank(operator)) {
                String operatorNames = Arrays.stream(operator.split(",", 0))
                        .map(op -> StrUtil.isNotBlank(collect.get(op)) ? collect.get(op) : op)
                        .collect(Collectors.joining(","));
                vo.setOperator(operatorNames);
            }
            String preUpgradeVirtualSku = virtualProductRepository.getPreUpgradeSkuByUpgradeSkuId(vo.getVirtualSkuId());
            vo.setPreUpgradeVirtualSku(StrUtil.isNotBlank(preUpgradeVirtualSku) ? preUpgradeVirtualSku : "");

            // 设置大类信息
            ProductCategoryDTO parentDTO = categoryDTOMap.getOrDefault(String.valueOf(vo.getCategory()), null);
            String parentName = null;
            if (parentDTO != null) {
                parentName = parentDTO.getCategoryName();
            }
            vo.setCategory(parentName);

            // 可干预时间
            InventorySaleRulesDO salesRules = saleRuleMap.get(vo.getInventoryInfoId());
            vo.setInterventionalTimeDto(interventionalTimeService.calculateInterventionalTime(vo.getUrgentPurchaseDate(), salesRules));

            Map<String, Double> saleRatioMap = JSON.parseObject(salesRules.getSaleRatio(), new TypeReference<>() {
            });
            String warehouse = warehouseService.getMinHeadShipDateWarehouse(saleRatioMap);
            vo.setUrgentWarehouse(warehouse);

            String channel = channelIdNameMap.getOrDefault(vo.getChannel(), vo.getChannel());
            vo.setChannel(channel);
        }
    }

    private String getChildCategoryId(String parentId) {
        if (StrUtil.isBlank(parentId)) {
            return null;
        }

        Map<String, ProductCategoryDTO> categoryDTOMap = productCategoryRepository.getCategoryDTOMap();
        for (Map.Entry<String, ProductCategoryDTO> category : categoryDTOMap.entrySet()) {
            if (category.getValue().getId().toString().equals(parentId)) {
                return category.getKey();
            }
        }
        return null;
    }
}
