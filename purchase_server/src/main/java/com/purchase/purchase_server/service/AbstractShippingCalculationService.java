package com.purchase.purchase_server.service;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.purchase.purchase_server.entity.dto.FactoryFinishedInventoryDto;
import com.purchase.purchase_server.entity.dto.FactoryRemainInventoryDto;
import com.purchase.purchase_server.repository.dataRepository.DeliveryPurchaseRepositoryImpl;
import com.purchase.purchase_server.repository.dataRepository.VirtualProductRepositoryImpl;
import com.purchase.purchase_server.repository.interiorRepository.WarehouseRepository;
import com.purchase.purchase_server.service.impl.CalRemainInventoryServiceImpl;
import com.purchase.purchase_server.service.shipping.IShippingCalculationService;
import com.purchase.purchase_server.utils.easyExcelUtil.manager.AbstractMapManager;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static cn.hutool.core.date.DateField.DAY_OF_YEAR;
import static com.crafts_mirror.utils.constant.DateFormatConstant.YYYY_MM_DD_DATE_FORMAT_SLASH;
import static java.math.RoundingMode.HALF_UP;

/**
 * @Description 发货抽象类
 * <AUTHOR>
 * @Date 2024/3/25 16:53
 **/
@Slf4j
public abstract class AbstractShippingCalculationService extends CalRemainInventoryServiceImpl implements IShippingCalculationService {

    @Value("${spring.profiles.active}")
    protected String environment;

    @Resource
    protected DeliveryPurchaseRepositoryImpl deliveryPurchaseRepository;

    @Resource
    private VirtualProductRepositoryImpl virtualProductRepository;

    @Resource
    protected WarehouseRepository warehouseRepository;

    @Resource
    protected ICommonConsumptionService commonConsumptionService;

    @Override
    public Map<String, String> convertOldSkuInfoToVirtualSkuInfo(AbstractMapManager mapManager) {
        Map<String, String> virtualSkuAndOldSkuMap = new HashMap<>();
        // 库存
        var skuStockQuantityMap = mapManager.getStockQuantityMap();
        // 计划
        var skuDeliveryMap = mapManager.getFactoryPlanMap();

        for (String sku : skuDeliveryMap.keySet()) {
            String oldSku;
            // 此处获取的sku既有可能是虚拟sku，也有可能是老sku，需要先将老sku转换成虚拟sku，再进行试算
            String virtualSku = virtualProductRepository.getVirtualSkuByDestinationSku(sku);
            if (virtualSku == null) {
                virtualSku = sku;
                oldSku = virtualProductRepository.getOldSkuByDestinationSku(sku);
            } else {
                oldSku = sku;
            }
            List<FactoryRemainInventoryDto> virtualFactoryRemainInventoryList = skuStockQuantityMap.getOrDefault(virtualSku, new ArrayList<>());
            skuStockQuantityMap.putIfAbsent(virtualSku, virtualFactoryRemainInventoryList);
            List<FactoryFinishedInventoryDto> virtualFactoryFinishedList = skuDeliveryMap.getOrDefault(virtualSku, new ArrayList<>());
            skuDeliveryMap.putIfAbsent(virtualSku, virtualFactoryFinishedList);

            if (oldSku == null || virtualSkuAndOldSkuMap.containsKey(virtualSku)) {
                continue;
            }
            // 将老sku对应的库存信息存入虚拟sku对应的库存信息中去
            List<FactoryRemainInventoryDto> oldFactoryRemainInventoryDtos = skuStockQuantityMap.getOrDefault(oldSku, new ArrayList<>());
            virtualFactoryRemainInventoryList.addAll(oldFactoryRemainInventoryDtos);

            virtualSkuAndOldSkuMap.put(virtualSku, sku);
            List<FactoryFinishedInventoryDto> oldFactoryFinishedList = skuDeliveryMap.getOrDefault(oldSku, new ArrayList<>());
            virtualFactoryFinishedList.addAll(oldFactoryFinishedList);
        }
        return virtualSkuAndOldSkuMap;
    }

    /**
     * 计算总仓的安全库存
     *
     * @param safeDate 安全天数
     * @param startDate 最早到货时间
     * @param targetSalesMap 每个月的目标日销
     * @return 总仓的安全库存
     */
    protected BigDecimal calTotalSafeInventory(int safeDate, DateTime startDate, Map<String, Double> targetSalesMap, int redundantDate) {
        safeDate += redundantDate;
        BigDecimal totalSafeInventory = BigDecimal.ZERO;
        DateTime calDate = new DateTime(startDate);
        for (int i = 0; i < safeDate; i++) {
            String dateKey = calDate.offsetNew(DAY_OF_YEAR, i).toString(YYYY_MM_DD_DATE_FORMAT_SLASH);
            totalSafeInventory = totalSafeInventory.add(BigDecimal.valueOf(targetSalesMap.getOrDefault(dateKey, 0.0)));
        }
        return totalSafeInventory.setScale(3, HALF_UP);
    }

    protected Set<String> getArrivingOrFullInventoryWarehouseSet(Map<String, Double> warehouseLackNumMap, Map<String, Double> inventorySaleDefRatio) {
        return warehouseLackNumMap.entrySet().stream()
                .filter(entry -> entry.getValue() == 0 && inventorySaleDefRatio.get(entry.getKey()) != 0)
                .map(Map.Entry::getKey)
                .collect(Collectors.toSet());
    }

    protected void modifyNextDayInventory(Map<String, Map<String, Double>> everydayRetainMap,
                                          List<FactoryRemainInventoryDto> factoryRemainInventoryList,
                                          Map<String, Double> daySaleMap, String today, String tomorrow) {
        Map<String, Double> todayRetainMap = everydayRetainMap.get(today);
        Map<String, Double> tomorrowRetainMap = everydayRetainMap.getOrDefault(tomorrow, new HashMap<>());
        DateTime parse = DateUtil.parse(tomorrow, YYYY_MM_DD_DATE_FORMAT_SLASH);
        for (var todayRetainEntry : todayRetainMap.entrySet()) {
            String warehouse = todayRetainEntry.getKey();
            double nextDayArrivingNum = factoryRemainInventoryList.stream()
                    .filter(f -> f.getWarehouse().equals(warehouse) && parse.compareTo(f.getEnableUsingDate()) == 0)
                    .mapToDouble(FactoryRemainInventoryDto::getStoreNum)
                    .sum();
            double todayAfterSaleRetainNum = Math.max(todayRetainEntry.getValue() - daySaleMap.getOrDefault(warehouse, 0D), 0);
            daySaleMap.putIfAbsent(warehouse, 0D);
            if (CollectionUtil.isNotEmpty(tomorrowRetainMap)) {
                tomorrowRetainMap.put(warehouse, BigDecimal.valueOf(todayAfterSaleRetainNum + nextDayArrivingNum).setScale(3, HALF_UP).doubleValue());
            }
        }
    }

    protected String getEnableUsingDateString(String warehouse, DateTime shippingStartDate, Map<String, Integer> headShippingDateMap) {
        int dayGap = headShippingDateMap.get(warehouse);
        DateTime enableUsingDate = shippingStartDate.offsetNew(DAY_OF_YEAR, dayGap);
        return enableUsingDate.toString(YYYY_MM_DD_DATE_FORMAT_SLASH);
    }

    protected double getRealTotalRatio(Map<String, Double> inventorySaleDefRatio, Set<String> warehouseSet) {
        return inventorySaleDefRatio.entrySet().stream()
                .filter(entry -> warehouseSet.contains(entry.getKey()))
                .mapToDouble(Map.Entry::getValue)
                .sum();
    }

    protected BigDecimal calWarehouseSafeInventory(
            int safeDate, DateTime lastArrivingDate, Map<String, Double> targetSalesMap, Map<String, Double> inventorySaleDefRatio,
            String warehouse, int minHeadShippingDate, List<FactoryRemainInventoryDto> importFactoryRemainInventoryList,
            Map<String, Integer> headShippingDateMap, DateTime earliestArrivingDate, Map<String, Map<String, Double>> everydayRemainInventoryMap,
            Set<String> everArrivedWarehouseSet, int redundantDays, String virtualSku) {
        BigDecimal warehouseSafeInventory = BigDecimal.ZERO;
        safeDate += redundantDays;
        var newHashMap = deepCopyNestedMap(everydayRemainInventoryMap);
        for(DateTime calDate = new DateTime(lastArrivingDate);
            calDate.compareTo(lastArrivingDate.offsetNew(DAY_OF_YEAR, safeDate)) < 0;
            calDate.offset(DAY_OF_YEAR, 1)) {
            // 计算一天的仓库库存消耗情况
            double targetSales = targetSalesMap.getOrDefault(calDate.toString(YYYY_MM_DD_DATE_FORMAT_SLASH), 0D);

            double importArrivedNum = importFactoryRemainInventoryList.stream()
                    .filter(f -> calDate.compareTo(f.getEnableUsingDate()) == 0 && warehouse.equals(f.getWarehouse()))
                    .mapToDouble(FactoryRemainInventoryDto::getStoreNum)
                    .sum();
            BigDecimal warehouseSale = calDaysConsumption(earliestArrivingDate, calDate, warehouse, headShippingDateMap,
                    targetSales, newHashMap, inventorySaleDefRatio, minHeadShippingDate, everArrivedWarehouseSet, importArrivedNum,
                    virtualSku);
            warehouseSafeInventory = warehouseSafeInventory.add(warehouseSale);
        }
        return warehouseSafeInventory.setScale(3, HALF_UP);
    }

    protected BigDecimal calDaysConsumption(
            DateTime earliestArrivingDate, DateTime calDate, String warehouse, Map<String, Integer> headShippingDateMap,
            double targetSales, Map<String, Map<String, Double>> everydayRemainInventoryMap, Map<String, Double> inventorySaleDefRatio,
            int minHeadShippingDate, Set<String> everArrivedWarehouseSet,double importArrivedNum, String virtualSku) {
        String calDateStr = calDate.toString(YYYY_MM_DD_DATE_FORMAT_SLASH);
        String nextDateStr = calDate.offsetNew(DAY_OF_YEAR, 1).toString(YYYY_MM_DD_DATE_FORMAT_SLASH);
        Map<String, Double> warehouseRemainMap = everydayRemainInventoryMap.get(calDateStr);

        if(warehouseRemainMap == null) {
            return BigDecimal.ZERO;
        }
        BigDecimal afterZeroTargetSales = consumeZeroRatioWarehouseInventory(inventorySaleDefRatio, warehouseRemainMap,
                new HashMap<>(), new HashMap<>(), targetSales);
        // 先消耗目标日销为0的仓库
        if(afterZeroTargetSales.compareTo(BigDecimal.ZERO) <= 0) {
            return BigDecimal.ZERO;
        } else {
            Map<String, Double> nextDayInventoryMap = everydayRemainInventoryMap.getOrDefault(nextDateStr, new HashMap<>());
            inventorySaleDefRatio.entrySet().stream()
                    .filter(entry -> entry.getValue() <= 0)
                    .forEach(entry -> nextDayInventoryMap.compute(entry.getKey(), (k, v) -> Optional.ofNullable(v).orElse(0D)));
            everydayRemainInventoryMap.put(nextDateStr, nextDayInventoryMap);
        }

        Set<String> arrivingWarehouseSet = headShippingDateMap.entrySet().stream()
                // 如果计算当天，能到货的仓都当做其有货
                .filter(entry -> {
                    DateTime willArriveDate = earliestArrivingDate.offsetNew(DAY_OF_YEAR, entry.getValue() - minHeadShippingDate);
                    return willArriveDate.compareTo(calDate) <= 0;
                })
                .map(Map.Entry::getKey)
                .collect(Collectors.toSet());

        // 未到货的仓库
        Map<String, Double> finalWarehouseRemainMap = warehouseRemainMap;
        List<String> notArrivedWarehouseList = headShippingDateMap.keySet().stream()
                .filter(integer -> {
                    if (!finalWarehouseRemainMap.containsKey(integer)) {
                        log.error("计算该商品发货计划：{}，获取未到货仓库时该仓库剩余库存为空：{}", virtualSku, integer);
                    }
                    return !arrivingWarehouseSet.contains(integer) && finalWarehouseRemainMap.get(integer) > 0;
                })
                .collect(Collectors.toList());

        // 计算未到货仓库的消耗情况，如果有不够销售的情况，需要优先计算，防止数据出现偏差
        BigDecimal sales = BigDecimal.ZERO;
        boolean needRepeat = CollectionUtil.isNotEmpty(notArrivedWarehouseList);
        BigDecimal tempAfterZeroTargetSales = new BigDecimal(String.valueOf(afterZeroTargetSales));
        while (needRepeat) {
            needRepeat = false;
            Iterator<String> iterator = notArrivedWarehouseList.iterator();
            while (iterator.hasNext()) {
                double totalRatio = getTotalRatio(warehouseRemainMap, inventorySaleDefRatio, everArrivedWarehouseSet, arrivingWarehouseSet);
                String arrivedWarehouse = iterator.next();
                warehouseRemainMap = everydayRemainInventoryMap.get(calDateStr);
                BigDecimal remainInventory = BigDecimal.valueOf(warehouseRemainMap.get(arrivedWarehouse));
                double ratio = inventorySaleDefRatio.get(arrivedWarehouse);
                BigDecimal realRatio = BigDecimal.valueOf(ratio).divide(BigDecimal.valueOf(totalRatio), 3, HALF_UP);
                BigDecimal realSales = tempAfterZeroTargetSales.multiply(realRatio);
                Map<String, Double> nextdayInventoryMap = everydayRemainInventoryMap.getOrDefault(nextDateStr, new HashMap<>());
                if(remainInventory.compareTo(realSales) < 0) {
                    sales = sales.add(remainInventory);
                    needRepeat = true;
                    iterator.remove();
                    tempAfterZeroTargetSales = tempAfterZeroTargetSales.subtract(remainInventory);
                    warehouseRemainMap.put(arrivedWarehouse, 0D);
                    nextdayInventoryMap.compute(arrivedWarehouse, (k, v) -> Optional.ofNullable(v).orElse(0D));
                } else {
                    BigDecimal importArrivedNumDec = BigDecimal.valueOf(importArrivedNum);
                    nextdayInventoryMap.compute(arrivedWarehouse, (k, v) -> remainInventory.add(importArrivedNumDec).subtract(realSales).setScale(3, HALF_UP).doubleValue());
                }
            }
        }

        double totalRatio = getTotalRatio(warehouseRemainMap, inventorySaleDefRatio, everArrivedWarehouseSet, arrivingWarehouseSet);
        BigDecimal realRatio = BigDecimal.valueOf(inventorySaleDefRatio.get(warehouse)).divide(BigDecimal.valueOf(totalRatio), 3, HALF_UP);
        afterZeroTargetSales = afterZeroTargetSales.subtract(sales);
        return afterZeroTargetSales.multiply(realRatio).setScale(3, HALF_UP);
    }

    private double getTotalRatio(Map<String, Double> warehouseRemainMap, Map<String, Double> inventorySaleDefRatio,
                                 Set<String> everArrivedWarehouseSet, Set<String> arrivingWarehouseSet) {
        return warehouseRemainMap.entrySet().stream()
                .filter(entry -> entry.getValue() > 0 ||
                        arrivingWarehouseSet.contains(entry.getKey()) ||
                        everArrivedWarehouseSet.contains(entry.getKey()))
                .mapToDouble(entry -> inventorySaleDefRatio.get(entry.getKey()))
                .sum();
    }

    @SuppressWarnings("unchecked")
    private <K extends Comparable<K>, V> Map<K, V> deepCopyNestedMap(Map<K, V> originalMap) {
        Map<K, V> copiedMap = new TreeMap<>(Comparator.comparing(k -> k));
        for (Map.Entry<K, V> entry : originalMap.entrySet()) {
            K key = entry.getKey();
            V value = entry.getValue();
            // 如果值是Map类型，则递归进行深度复制
            if (value instanceof Map) {
                Map<K, V> nestedMap = (Map<K, V>) value;
                copiedMap.put(key, (V) deepCopyNestedMap(nestedMap));
            } else {
                // 如果值不是Map类型，则直接复制
                if(value instanceof  Double) {
                    copiedMap.put(key, (V) Double.valueOf((Double) value));
                } else {
                    copiedMap.put(key, value);
                }
            }
        }
        return copiedMap;
    }

}
