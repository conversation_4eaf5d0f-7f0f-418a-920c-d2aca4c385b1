package com.demo_server.infrastructures.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

/**
 * @Description 日志追踪号，要在注解上直接获取追踪号比较耗时间，所以先在此处传入一个追踪号的类来获取
 * <AUTHOR>
 * @Date 2023/12/13 19:26
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
public class LogTrackNumDto {

    /**
     * key:查询日志记录的id；
     * value：日志类型
     */
    private Map<String, String> logMap;

    private String authorization;

    public LogTrackNumDto(Map<String, String> logMap) {
        this.logMap = logMap;
    }
}
