package com.inventory_server.model.warehouse.entity.form;

import com.crafts_mirror.utils.aop.validator.PositiveInteger;
import com.crafts_mirror.utils.aop.validator.PositiveIntegerOrZero;
import com.crafts_mirror.utils.dp.BasePageForm;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.NotBlank;
import lombok.*;
import org.hibernate.validator.constraints.Length;

import java.io.Serial;

/**
 * <AUTHOR>
 * @Date 2024/7/4 17:01
 **/
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SenboWarehouseForm extends BasePageForm {

    @Serial
    private static final long serialVersionUID = 2178663439224007839L;

    private String senboWarehouseId;

    @NotBlank( message = "仓库名称不能为空")
    @Length(max = 20, message = "仓库名称长度不能超过20")
    private String senboWarehouse;

    @PositiveIntegerOrZero(message = "正常头程时间只能录入非负整数")
    private String headShippingDate;

    @PositiveIntegerOrZero(message = "加急头程时间只能录入非负整数")
    private String urgentHeadShippingDate;

    @PositiveIntegerOrZero(message = "排序只能录入正整数")
    @Max(value = 999, message = "排序最大999")
    private String sort;

    @Length(max = 500, message = "备注最多500个字符")
    private String remarks;
}
