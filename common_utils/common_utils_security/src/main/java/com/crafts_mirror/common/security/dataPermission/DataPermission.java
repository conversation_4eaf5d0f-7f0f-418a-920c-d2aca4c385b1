package com.crafts_mirror.common.security.dataPermission;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.crafts_mirror.common.security.dataPermission.vo.RoleDataPerVO;
import com.crafts_mirror.common.security.utils.SecurityUtils;
import com.crafts_mirror.utils.provider.ApplicationContextProvider;
import com.crafts_mirror.utils.utils.RestTemplateUtils;
import com.crafts_mirror.utils.web.domain.ResultDTO;
import org.springframework.web.client.RestTemplate;

import java.util.List;
import java.util.stream.Collectors;

import static com.crafts_mirror.utils.constant.SecurityConstants.*;
import static com.crafts_mirror.utils.constant.SystemConstant.SYS_USER_DATA_PERMISSION;

/**
 * <AUTHOR>
 * @Date 2024/8/19 11:35
 **/
public class DataPermission {


    protected final RestTemplate restTemplate = ApplicationContextProvider.getBean(RestTemplate.class);


    public String getDataPermissionUser(String moduleId, String tableAlias) {

        // 拿请求url与权限字符去菜单管理中获取菜单名称
        String token = SecurityUtils.getLoginUser().getToken();
        RestTemplateUtils restTemplateUtils = new RestTemplateUtils(restTemplate, token);

        ResultDTO resultDTO = restTemplateUtils.get(SYS_USER_DATA_PERMISSION + moduleId,
                ResultDTO.class);

        RoleDataPerVO roleDataPerVO = JSON.to(RoleDataPerVO.class, resultDTO.getData());
        String resultSql;
        if (ObjectUtil.isEmpty(roleDataPerVO)) {
            return null;
        }

        switch (moduleId) {
            // 自定义产品
            case ROLE_DATA_PER_SELF -> resultSql = this.buildSelfSql(roleDataPerVO, tableAlias);
            // 目标销售、库存预警的数据权限与虚拟商品一致
            case ROLE_DATA_PER_VIR, ROLE_DATA_PER_WARNING, ROLE_DATA_PER_TARGET_SALES, ROLE_DATA_PER_URGENT_PURCHASE ->
                    resultSql = this.buildVirSql(roleDataPerVO, tableAlias);
            // 供应商
            case ROLE_DATA_PER_FACTORY -> resultSql = this.buildFactorySql(roleDataPerVO, tableAlias);
            // 发货
            case ROLE_DATA_PER_DELIVERY -> resultSql = this.buildDeliverySql(roleDataPerVO, tableAlias);
            // 补货
            case ROLE_DATA_PER_REPLENISH -> resultSql = this.buildReplenishSql(roleDataPerVO, tableAlias);
            // 库存
            case ROLE_DATA_PER_INVENTORY -> resultSql = this.buildInventorySql(roleDataPerVO, tableAlias);
            default -> throw new IllegalArgumentException("暂不支持 [%s] 数据权限".formatted(moduleId));
        }

        return resultSql;
    }

    private String buildSelfSql(RoleDataPerVO roleDataPerVO, String tableAlias) {
        StringBuilder resultSql = new StringBuilder();
        resultSql.append("(").append(tableAlias).append(".id in (SELECT DISTINCT self_product_sku_id ");
        this.buildDataScopeFilter(roleDataPerVO, resultSql);

        List<String> userNames = roleDataPerVO.getUserNames();
        List<String> deptIds = roleDataPerVO.getDeptIds();
        String userSql = "", deptSql = "";

        if (CollectionUtil.isNotEmpty(userNames)) {
            userSql = "sp.buyer in ( " + joinWithQuotes(userNames, ",") + ")";

        }
        if (CollectionUtil.isNotEmpty(deptIds)) {
            deptSql = "sp.buyer in ( " +
                    "SELECT DISTINCT u.user_name " +
                    "FROM cm_sys_user u " +
                    "JOIN cm_sys_user_dept ud ON ud.user_id = u.id " +
                    "JOIN cm_sys_dept d ON d.id = ud.dept_id " +
                    "WHERE u.status = '0' " +
                    "AND d.status = '0' " +
                    "AND (d.id in (" +
                    joinWithQuotes(deptIds, ",") + ") OR d.ancestors REGEXP '(^|,)(" +
                    String.join("|", deptIds) + ")(,|$)')) ";
        }
        if (StrUtil.isNotBlank(userSql) || StrUtil.isNotBlank(deptSql)) {
            resultSql.append("OR ").append(tableAlias).append(".id in ( SELECT DISTINCT sp.id ")
                    .append("FROM cm_self_product sp ")
                    .append("WHERE sp.STATUS = '0' ");
            if (StrUtil.isNotBlank(userSql) && StrUtil.isNotBlank(deptSql)) {
                resultSql.append("AND ").append(userSql).append("OR ").append(deptSql);
            } else if (StrUtil.isNotBlank(userSql)) {
                resultSql.append("AND ").append(userSql);
            } else if (StrUtil.isNotBlank(deptSql)) {
                resultSql.append("AND ").append(deptSql);
            }
            resultSql.append(")");
        }
        resultSql.append(")");
        return resultSql.toString();
    }

    private String buildVirSql(RoleDataPerVO roleDataPerVO, String tableAlias) {
        StringBuilder resultSql = new StringBuilder();
        resultSql.append("(").append(tableAlias).append(".id in (SELECT DISTINCT vp.id ");
        this.buildDataScopeFilter(roleDataPerVO, resultSql);
        resultSql.append(")");
        return resultSql.toString();
    }

    private String buildWarningInventorySql(RoleDataPerVO roleDataPerVO, String tableAlias) {
        StringBuilder resultSql = new StringBuilder();
        resultSql.append("(").append(tableAlias).append(".spu_id in (SELECT DISTINCT spu_id ");
        this.buildDataScopeFilter(roleDataPerVO, resultSql);
        resultSql.append(")");
        return resultSql.toString();
    }

    private String buildInventorySql(RoleDataPerVO roleDataPerVO, String tableAlias) {
        StringBuilder resultSql = new StringBuilder();
        resultSql.append("(").append(tableAlias).append(".virtual_data->>'$.spuId' in (SELECT DISTINCT spu_id ");
        this.buildDataScopeFilter(roleDataPerVO, resultSql);
        resultSql.append(")");
        return resultSql.toString();
    }

    private String buildFactorySql(RoleDataPerVO roleDataPerVO, String tableAlias) {

        StringBuilder resultSql = new StringBuilder();
        List<String> userNames = roleDataPerVO.getUserNames();
        List<String> deptIds = roleDataPerVO.getDeptIds();
        String userSql = "", deptSql = "";

        if (CollectionUtil.isNotEmpty(userNames)) {
            userSql = tableAlias + ".order_tracker in ( " + joinWithQuotes(userNames, ",") + ")" +
                    "OR " + tableAlias + ".purchaser in ( " + joinWithQuotes(userNames, ",") + ")" +
                    "OR " + tableAlias + ".id in ( SELECT DISTINCT sp.factory_id "
                    + "FROM cm_self_product sp "
                    + "WHERE sp.STATUS = '0' AND sp.buyer in ( " + joinWithQuotes(userNames, ",") + "))";
        }
        if (CollectionUtil.isNotEmpty(deptIds)) {
            deptSql = tableAlias + ".order_tracker in ( " +
                    "SELECT DISTINCT u.user_name " +
                    "FROM cm_sys_user u " +
                    "JOIN cm_sys_user_dept ud ON ud.user_id = u.id " +
                    "JOIN cm_sys_dept d ON d.id = ud.dept_id " +
                    "WHERE u.status = '0' " +
                    "AND d.status = '0' " +
                    "AND (d.id in (" + joinWithQuotes(deptIds, ",") + ") OR d.ancestors REGEXP '(^|,)(" + String.join("|", deptIds) + ")(,|$)')) " +
                    "OR " + tableAlias + ".purchaser in ( " +
                    "SELECT DISTINCT u.user_name " +
                    "FROM cm_sys_user u " +
                    "JOIN cm_sys_user_dept ud ON ud.user_id = u.id " +
                    "JOIN cm_sys_dept d ON d.id = ud.dept_id " +
                    "WHERE u.status = '0' " +
                    "AND d.status = '0' " +
                    "AND (d.id in (" + joinWithQuotes(deptIds, ",") + ") OR d.ancestors REGEXP '(^|,)(" + String.join("|", deptIds) + ")(,|$)')) " +
                    "OR " + tableAlias + ".id in ( SELECT DISTINCT sp.factory_id "
                    + "FROM cm_self_product sp "
                    + "WHERE sp.STATUS = '0' AND sp.buyer in ( " +
                    "SELECT DISTINCT u.user_name " +
                    "FROM cm_sys_user u " +
                    "JOIN cm_sys_user_dept ud ON ud.user_id = u.id " +
                    "JOIN cm_sys_dept d ON d.id = ud.dept_id " +
                    "WHERE u.status = '0' " +
                    "AND d.status = '0' " +
                    "AND (d.id in (" +
                    joinWithQuotes(deptIds, ",") + ") OR d.ancestors REGEXP '(^|,)(" +
                    String.join("|", deptIds) + ")(,|$)'))) ";
        }
        if (StrUtil.isNotBlank(userSql) && StrUtil.isNotBlank(deptSql)) {
            resultSql.append("(").append(userSql).append("OR ").append(deptSql).append(")");
        } else if (StrUtil.isNotBlank(userSql)) {
            resultSql.append("(").append(userSql).append(")");
        } else if (StrUtil.isNotBlank(deptSql)) {
            resultSql.append("(").append(deptSql).append(")");
        }
        return resultSql.toString();
    }

    private String buildDeliverySql(RoleDataPerVO roleDataPerVO, String tableAlias) {
        StringBuilder resultSql = new StringBuilder();
        resultSql.append("(");
        this.buildDataScopeFilterBySnap(roleDataPerVO, resultSql, tableAlias);
        resultSql.append(")");
        return resultSql.toString();
    }

    private String buildReplenishSql(RoleDataPerVO roleDataPerVO, String tableAlias) {
        StringBuilder resultSql = new StringBuilder();
        resultSql.append("(");
        this.buildDataScopeFilterBySnap(roleDataPerVO, resultSql, tableAlias);
        resultSql.append(")");
        return resultSql.toString();
    }

    private void buildDataScopeFilter(RoleDataPerVO roleDataPerVO, StringBuilder resultSql) {
        resultSql.append("FROM cm_virtual_product vp ")
                .append("WHERE vp.STATUS = '0' ")
                .append("AND ( ");
        List<String> userNames = roleDataPerVO.getUserNames();
        List<String> deptIds = roleDataPerVO.getDeptIds();
        String userSql = "", deptSql = "";
        if (CollectionUtil.isNotEmpty(userNames)) {
            userSql = "vp.operator REGEXP '(^|,)(" + String.join("|", userNames) + ")(,|$)' ";
        }
        if (CollectionUtil.isNotEmpty(deptIds)) {
            deptSql = "vp.operator REGEXP CONCAT( " +
                    "'(^|,)(', " +
                    "(SELECT GROUP_CONCAT(DISTINCT u.user_name SEPARATOR '|') " +
                    "FROM cm_sys_user u " +
                    "JOIN cm_sys_user_dept ud ON ud.user_id = u.id " +
                    "JOIN cm_sys_dept d ON d.id = ud.dept_id " +
                    "WHERE u.status = '0' " +
                    "AND d.status = '0' " +
                    "AND (d.id in (" + joinWithQuotes(deptIds, ",") + ") OR d.ancestors REGEXP '(^|,)(" + String.join("|", deptIds) + ")(,|$)') " +
                    "), " +
                    "')(,|$)' " +
                    ") ";
        }
        if (StrUtil.isNotBlank(userSql) && StrUtil.isNotBlank(deptSql)) {
            resultSql.append(userSql).append("OR ").append(deptSql);
        } else if (StrUtil.isNotBlank(userSql)) {
            resultSql.append(userSql);
        } else if (StrUtil.isNotBlank(deptSql)) {
            resultSql.append(deptSql);
        }

        resultSql.append(")) ");
    }

    // 快照
    private void buildDataScopeFilterBySnap(RoleDataPerVO roleDataPerVO, StringBuilder resultSql, String tableAlias) {

        List<String> userNames = roleDataPerVO.getUserNames();
        List<String> deptIds = roleDataPerVO.getDeptIds();
        String userSql = "", deptSql = "";
        if (CollectionUtil.isNotEmpty(userNames)) {
            userSql = tableAlias + ".virtual_data ->> '$.operator' REGEXP '(^|,)(" + String.join("|", userNames) + ")(,|$)' ";
        }
        if (CollectionUtil.isNotEmpty(deptIds)) {
            deptSql = tableAlias + ".virtual_data ->> '$.operator' REGEXP CONCAT( " +
                    "'(^|,)(', " +
                    "(SELECT GROUP_CONCAT(DISTINCT u.user_name SEPARATOR '|') " +
                    "FROM cm_sys_user u " +
                    "JOIN cm_sys_user_dept ud ON ud.user_id = u.id " +
                    "JOIN cm_sys_dept d ON d.id = ud.dept_id " +
                    "WHERE u.status = '0' " +
                    "AND d.status = '0' " +
                    "AND (d.id in (" + joinWithQuotes(deptIds, ",") + ") OR d.ancestors REGEXP '(^|,)(" + String.join("|", deptIds) + ")(,|$)') " +
                    "), " +
                    "')(,|$)' " +
                    ") ";
        }
        if (StrUtil.isNotBlank(userSql) && StrUtil.isNotBlank(deptSql)) {
            resultSql.append(userSql).append("OR ").append(deptSql);
        } else if (StrUtil.isNotBlank(userSql)) {
            resultSql.append(userSql);
        } else if (StrUtil.isNotBlank(deptSql)) {
            resultSql.append(deptSql);
        }
    }

    private String joinWithQuotes(List<String> list, String quotes) {
        return list.stream()
                .map(s -> "'" + s + "'")
                .collect(Collectors.joining(quotes));
    }
}
