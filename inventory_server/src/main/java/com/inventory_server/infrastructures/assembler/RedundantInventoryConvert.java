package com.inventory_server.infrastructures.assembler;


import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.TypeReference;

import java.util.Map;

/**
 * 转换工具类
 *
 * <AUTHOR>
 * @date 2021/8/26
 */

public class RedundantInventoryConvert {

    public static Map<String, Integer> StringToMapInteger(String value) {
        if (ObjectUtil.isNull(value)) {
            return null;
        }
        return JSONObject.parseObject(value, new TypeReference<>() {
        });
    }

    public static Map<String, Double> StringToMapDouble(String value) {
        if (ObjectUtil.isNull(value)) {
            return null;
        }
        return JSONObject.parseObject(value, new TypeReference<>() {
        });
    }

    public static String MapIntegerToString(Map<String, Integer> value) {
        StringBuffer sb = new StringBuffer();
        if (ObjectUtil.isNull(value)) {
            return sb.toString();
        }
        for (Map.Entry<String, Integer> entry : value.entrySet()) {
            sb.append(entry.getKey()).append(":").append(entry.getValue()).append("\n");
        }
        if (!sb.isEmpty()) {
            sb.setLength(sb.length() - 1);
        }
        return sb.toString();
    }

    public static String MapDoubleToString(Map<String, Double> value) {
        StringBuffer sb = new StringBuffer();
        if (ObjectUtil.isNull(value)) {
            return sb.toString();
        }
        for (Map.Entry<String, Double> entry : value.entrySet()) {
            sb.append(entry.getKey()).append(":").append(entry.getValue()).append("\n");
        }
        if (!sb.isEmpty()) {
            sb.setLength(sb.length() - 1);
        }
        return sb.toString();
    }

    public static String MapStringToString(Map<String, String> value) {
        StringBuffer sb = new StringBuffer();
        if (ObjectUtil.isNull(value)) {
            return sb.toString();
        }
        for (Map.Entry<String, String> entry : value.entrySet()) {
            sb.append(entry.getKey()).append(":").append(entry.getValue()).append("\n");
        }
        if (!sb.isEmpty()) {
            sb.setLength(sb.length() - 1);
        }
        return sb.toString();
    }
}
