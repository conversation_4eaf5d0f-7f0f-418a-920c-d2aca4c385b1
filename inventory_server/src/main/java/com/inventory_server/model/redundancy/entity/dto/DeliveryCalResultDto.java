package com.inventory_server.model.redundancy.entity.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/5/9 16:49
 **/
@EqualsAndHashCode(callSuper = true)
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class DeliveryCalResultDto extends TrialCalReplenishmentDto {

    private List<DeliveryCalResultFactoryFinishedDto> factoryFinishedResultList;

    private String snapShotId;
}
