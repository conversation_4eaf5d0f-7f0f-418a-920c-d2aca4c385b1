package com.product.product_server.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.crafts_mirror.utils.dp.LogTrackNumDto;
import com.product.product_server.entity.LogTrackNumMapDto;
import com.product.product_server.entity.dto.ProductCategoryDTO;
import com.product.product_server.entity.form.RemarksSaveForm;
import com.product.product_server.entity.form.SelfProductForm;
import com.product.product_server.entity.form.SelfProductUpdateForm;
import com.product.product_server.entity.vo.*;
import jakarta.servlet.http.HttpServletResponse;

import java.io.InputStream;
import java.util.List;

/**
 * <AUTHOR>
 */
public interface ISelfProductService {

    IPage<SelfProductListVo> pageList(SelfProductForm form);

    SelfProductDetailVo getDetail(String productId);

    void importProductInfo(InputStream file, byte[] byteArrayResource, String fileName);

    void updateSelfProductCommodityInspection(InputStream file, byte[] byteArrayResource, String fileName);

    List<BuyerSearchVo> getBuyerSet();

    boolean saveSelfProductRemarks(RemarksSaveForm form, LogTrackNumDto trackNumDto);

    boolean deleteSelfProduct(String selfProductId, LogTrackNumDto dto);

    boolean deleteSelfProductBatch(List<String> selfProductIds, LogTrackNumMapDto dto);

    SelfProductUpdateVo updateSelfProduct(SelfProductUpdateForm form, String name);

    SelfProductUpdateVo updateBeforeCheck(SelfProductUpdateForm form);

    List<SelfAndVirtualVo> virtualList(String selfProductId);

    void exportInfo(SelfProductForm form, HttpServletResponse response);

    List<ProductCategoryDTO> category();

}
