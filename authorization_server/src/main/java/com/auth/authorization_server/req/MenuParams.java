package com.auth.authorization_server.req;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2024/7/22
 **/
@Data
public class MenuParams implements Serializable {
    @Serial
    private static final long serialVersionUID = -4525094738019095634L;

    /**
     * 菜单id
     */
    private String id;

    /**
     * 菜单名称
     */
    @NotBlank(message = "菜单名称不能为空")
    @Size(max = 50, message = "菜单名称长度不能超过50个字符")
    private String menuName;

    /**
     * 父级菜单id
     */
    private String parentId;

    /**
     * 显示顺序
     */
    @NotNull(message = "显示顺序不能为空")
    private Integer orderNum;

    /**
     * 路由地址
     */
    @Size(max = 200, message = "路由地址不能超过200个字符")
    private String path;

    /**
     * 路由名称
     */
    private String pathName;

    /**
     * 组件路径
     */
    @Size(max = 200, message = "组件路径不能超过255个字符")
    private String component;

    /**
     * 是否为外链（0是 1否）
     */
    private String isFrame;

    /**
     * 类型（0目录 1菜单 2按钮）
     */
    @NotBlank(message = "菜单类型不能为空")
    private String menuType;

    /**
     * 菜单状态（0正常 1停用）
     */
    private String menuStatus;

    /**
     * 权限字符串
     */
    @Size(max = 100, message = "权限标识长度不能超过100个字符")
    private String perms;

    /**
     * 菜单图标
     */
    private String icon;


    private String status;


    public String getMenuName() {
        return Optional.ofNullable(menuName)
                .map(String::strip)
                .orElse(null);
    }
}
