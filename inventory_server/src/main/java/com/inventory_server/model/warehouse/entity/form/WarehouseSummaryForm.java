package com.inventory_server.model.warehouse.entity.form;

import com.alibaba.fastjson2.annotation.JSONField;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @Description 易仓Api getWarehouseList 接口的请求参数
 * <AUTHOR>
 * @Date 2024/6/28 15:09
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WarehouseSummaryForm implements Serializable {
    private Integer page;

    @JSONField(name = "page_size")
    private Integer pageSize;
}
