package com.purchase.purchase_server.entity.vo;

import com.purchase.purchase_server.entity.dto.replenishment.ReplenishmentTrialConfirmQuantityDto;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * @Description 补货建议试算看板返回给前端的数据
 * <AUTHOR>
 * @Date 2024/1/22 15:13
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ReplenishmentTrialConfirmQuantityVo implements Serializable {
    private List<ReplenishmentTrialConfirmQuantityDto> watchBoardDtoList;

    private Integer advicePurchaseNum;

    private Integer actualReplenishmentNum;

    private Integer operationConfirmedNum;

    private String advicePurchaseStartDate;

    private String advicePurchaseEndDate;

    private String operationReason;

    // 建议原因
    private String reason;

    // 运营确认理由
    private String confirmedReason;

    /**
     * 运营补充说明
     */
    private String operatorRemark;

    private Map<String, String> confirmedReasonList;

}
