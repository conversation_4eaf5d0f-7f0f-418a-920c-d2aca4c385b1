package com.purchase.purchase_server.model.targetSales.entity.form;

import com.purchase.purchase_server.model.targetSales.entity.dto.TargetDaySalesDto;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * @Description 编辑目标日销表单
 * <AUTHOR>
 * @Date 2025/3/11 17:13
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DaySalesEditForm implements Serializable {
    private String virtualSkuId;

    private List<TargetDaySalesDto> targetDaySalesList;

    private String content;
}
