package com.purchase.purchase_server.service.shipping.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.crafts_mirror.utils.utils.DateUtils;
import com.purchase.purchase_server.consts.SpecialDeliveryCollection;
import com.purchase.purchase_server.entity.dataObject.SelfProductDO;
import com.purchase.purchase_server.entity.dataObject.TrialShippingInventoryDO;
import com.purchase.purchase_server.entity.dataObject.VirtualProductDO;
import com.purchase.purchase_server.entity.dto.FactoryRemainInventoryDto;
import com.purchase.purchase_server.entity.dto.SenboWarehouseDto;
import com.purchase.purchase_server.entity.dto.delivery.*;
import com.purchase.purchase_server.entity.form.DeliveryCalculationForm;
import com.purchase.purchase_server.entity.form.InteriorInfoQuery;
import com.purchase.purchase_server.entity.vo.DeliveryCalculationVo;
import com.purchase.purchase_server.repository.dataRepository.*;
import com.purchase.purchase_server.repository.interiorRepository.WarehouseRepository;
import com.purchase.purchase_server.service.ICommonConsumptionService;
import com.purchase.purchase_server.service.IProductSnapshotService;
import com.purchase.purchase_server.service.shipping.IShippingCalculationService;
import com.purchase.purchase_server.service.shipping.IShippingSaveCalResultService;
import com.purchase.purchase_server.service.shipping.InteriorDeliveryCalService;
import com.purchase.purchase_server.utils.commonUtils.WarehouseSortUtil;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.stream.Collectors;

import static com.crafts_mirror.utils.constant.DateFormatConstant.YYYY_MM_DD_DATE_FORMAT_CHINESE;

/**
 * <AUTHOR>
 * @Date 2024/5/9 17:26
 **/
@Service
@Slf4j
public class InteriorDeliveryCalServiceImpl implements InteriorDeliveryCalService {

    @Resource(name = "noRepeatableCalculationServiceImpl")
    private IShippingCalculationService shippingCalculationService;

    @Resource
    private IShippingSaveCalResultService shippingSaveCalResultService;

    @Resource(name = "deliveryCalThreadPool")
    private Executor deliveryCalThreadPool;

    @Resource
    private SelfProductRepositoryImpl selfProductRepository;

    @Resource
    private VirtualProductRepositoryImpl virtualProductRepository;

    @Resource
    private IProductSnapshotService productSnapshotService;

    @Resource
    private TrialShippingInventoryRepositoryImpl shippingInventoryRepository;

    @Resource
    private FactoryFinishedInventoryRepositoryImpl factoryFinishedInventoryRepository;

    @Resource
    private DeliveryForeignInventoryRepositoryImpl deliveryForeignInventoryRepository;

    @Resource
    private WarehouseRepository warehouseRepository;

    @Resource
    protected ICommonConsumptionService commonConsumptionService;

    @Override
    public DeliveryCalculationVo calDelivery(DeliveryCalculationForm form, boolean needReCalFactoryInventory) {
        DateTime startDate = DateUtil.parse(form.getShippingStartDateStr());

        List<SenboWarehouseDto> senboWarehouseList = warehouseRepository.getSenboWarehouseList();

        List<DeliveryCalRequestDto> deliveryCalRequestDtoList = form.getDeliveryCalRequestDtoList();
        var futureList = deliveryCalRequestDtoList.stream()
                .map(dto -> getDeliveryCalFuture(dto, startDate, form.getShippingStartDateStr(),
                        needReCalFactoryInventory, senboWarehouseList))
                .toList();

        List<DeliveryCalResultDto> resultList = futureList.stream().map(CompletableFuture::join).toList();

        return DeliveryCalculationVo.builder().deliveryCalResultList(resultList).build();
    }

    @Override
    public DeliveryCalculationVo calMockResult(DeliveryCalculationForm form) {
        DateTime startDate = DateUtil.parse(form.getShippingStartDateStr());
        List<DeliveryCalRequestDto> deliveryCalRequestDtoList = form.getDeliveryCalRequestDtoList();
        Map<String, Double> targetSalesMap = deliveryCalRequestDtoList.getFirst().getTargetSalesMap();
        DateTime maxCalDate = targetSalesMap.keySet().stream().map(DateUtil::parse).max(DateUtil::compare).orElse(startDate);
        int calCircle = (int) DateUtil.betweenDay(startDate, DateUtil.endOfMonth(maxCalDate), true);
        DateTime calculatedDate = DateUtil.parseDate(form.getCalculatedDateStr());
        List<SenboWarehouseDto> senboWarehouseList = warehouseRepository.getSenboWarehouseList();

        List<DeliveryCalResultDto> list = new ArrayList<>(deliveryCalRequestDtoList.size());
        for (var dto : deliveryCalRequestDtoList) {
            Map<String, Integer> headShippingDays = dto.getHeadShippingDays();
            targetSalesMap = dto.getTargetSalesMap();
            Map<String, Double> shippingRatioMap = dto.getShippingRatio();

            Map<String, Integer> sortedMap = senboWarehouseList.stream().collect(Collectors.toMap(SenboWarehouseDto::getSenboWarehouseId, SenboWarehouseDto::getSort));
            List<String> sortedWarehouseList = WarehouseSortUtil.getSortedWarehouseList(shippingRatioMap, sortedMap);

            int minShippingDate = headShippingDays.entrySet().stream()
                    .filter(h -> shippingRatioMap.containsKey(h.getKey()) && shippingRatioMap.get(h.getKey()) > 0)
                    .map(Map.Entry::getValue)
                    .sorted().findFirst().orElseThrow(() -> new NullPointerException("头程时间为空"));
            List<FactoryRemainInventoryDto> deliveryResultInventoryList = dto.getDeliveryResultInventoryList() == null ? new ArrayList<>() : dto.getDeliveryResultInventoryList();
            List<FactoryRemainInventoryDto> remainInventoryListForCal = new ArrayList<>(dto.getFactoryRemainInventoryList());

            MockTableCalDto mockTableCalDto = new MockTableCalDto(deliveryResultInventoryList, calCircle, new ArrayList<>(),
                    headShippingDays, calculatedDate, remainInventoryListForCal, dto.getFactoryRemainInventoryList(),
                    dto.getPriorDeliveryList(), minShippingDate, targetSalesMap, shippingRatioMap, sortedWarehouseList);
            TrialCalReplenishmentDto trialCalReplenishmentDto = commonConsumptionService.calDeliveryResults(mockTableCalDto);

            // 剔除掉为数据为0的项，方便进行传输以及json转化
            Map<String, Map<String, Double>> everydayOnShippingInventoryMap = trialCalReplenishmentDto.getEverydayOnShippingInventoryMap();
            cleanMap(everydayOnShippingInventoryMap);
            Map<String, Map<String, Double>> everydayRemainInventoryMap = trialCalReplenishmentDto.getEverydayRemainInventoryMap();
            cleanMap(everydayRemainInventoryMap);
            Map<String, Map<String, Double>> everydaySaleProductMap = trialCalReplenishmentDto.getEverydaySaleProductMap();
            cleanMap(everydaySaleProductMap);

//            VirtualProductDO virtualProduct = virtualProductRepository.getOneByVirtualSkuOrOldSku(dto.getVirtualSku());
            list.add(DeliveryCalResultDto.builder()
                    .virtualSku(dto.getVirtualSku())
                    .everydayOnShippingInventoryMap(everydayOnShippingInventoryMap)
                    .everydaySaleProductMap(trialCalReplenishmentDto.getEverydaySaleProductMap())
                    .everydayRemainInventoryMap(trialCalReplenishmentDto.getEverydayRemainInventoryMap())
                    .build());
        }
        return DeliveryCalculationVo.builder().deliveryCalResultList(list).build();
    }

    @Override
    public List<ShippingProjectWatchBoardWarehouseDto> getWatchBoardInfoList(List<String> factoryFinishedIdList) {
        List<TrialShippingInventoryDO> list = shippingInventoryRepository.getListByFactoryFinishedIdList(factoryFinishedIdList);
        return list.stream()
                .map(m -> ShippingProjectWatchBoardWarehouseDto.builder()
                        .warehouse(m.getDestinationWarehouse())
                        .shippingAmount(m.getShippingNum())
                        .shippingStartDate(DateUtil.format(m.getRealShippingStartDate(), YYYY_MM_DD_DATE_FORMAT_CHINESE))
                        .arrivalDate(DateUtil.format(m.getExpectedArrivingDate(), YYYY_MM_DD_DATE_FORMAT_CHINESE))
                        .caseAmount(m.getPackageNum())
                        .factoryFinishedId(m.getFactoryFinishedId())
                        .build()
                )
                .toList();
    }



    public void cleanMap(Map<String, Map<String, Double>> map) {
        Iterator<Map.Entry<String, Map<String, Double>>> outerIterator = map.entrySet().iterator();

        while (outerIterator.hasNext()) {
            Map.Entry<String, Map<String, Double>> entry = outerIterator.next();
            Map<String, Double> innerMap = entry.getValue();

            // 使用迭代器遍历并删除innerMap中的项
            innerMap.entrySet().removeIf(innerEntry -> innerEntry.getValue() == 0.0);

            // 如果innerMap变为空，则删除outerMap中的该项
            if (innerMap.isEmpty()) {
                outerIterator.remove();
            }
        }
    }

    private CompletableFuture<DeliveryCalResultDto> getDeliveryCalFuture(DeliveryCalRequestDto dto, DateTime startDate,
                                                                         String projectCreateDate, boolean needReCalFactoryInventory,
                                                                         List<SenboWarehouseDto> senboWarehouseList) {
        Map<String, Integer> headShippingDays = dto.getHeadShippingDays();
        Map<String, Double> shippingRatioMap = dto.getShippingRatio();
        ShippingProjectBaseParamDto baseParamDto = ShippingProjectBaseParamDto.builder()
                .shippingRatio(shippingRatioMap)
                .headShippingDays(headShippingDays)
                .safeDays(dto.getSafeDays())
                .shippingFrequency(dto.getShippingCircle())
                .transitDays(dto.getTransitDays())
                .containLoader(dto.getContainLoader())
                .build();

        Map<String, Double> targetSalesMap = dto.getTargetSalesMap();
        DateTime maxCalDate = targetSalesMap.keySet().stream().map(DateUtil::parse).max(DateUtil::compare).orElse(startDate);
        int calGap = (int) DateUtil.betweenDay(startDate, maxCalDate, true);

        VirtualProductDO virtualProduct = virtualProductRepository.getOneByVirtualSkuOrOldSku(dto.getVirtualSku());
        SelfProductDO selfProductInfo = selfProductRepository.getSelfProductInfoByVirtualSkuId(virtualProduct.getId());

        DateTime dateTime = DateUtil.beginOfDay(DateUtil.parseDate(projectCreateDate));
        return CompletableFuture.supplyAsync(() -> {
                    int productStatus = virtualProduct.getProductStatus();
                    TrialCalReplenishmentDto trialCalReplenishment;

                    Map<String, Integer> sortedMap = senboWarehouseList.stream().collect(Collectors.toMap(SenboWarehouseDto::getSenboWarehouseId, SenboWarehouseDto::getSort));
                    List<String> sortedWarehouseList = WarehouseSortUtil.getSortedWarehouseList(baseParamDto.getShippingRatio(), sortedMap);
                    if (SpecialDeliveryCollection.NOT_DELIVERY_SET.contains(productStatus)) {
                        int minShippingDate = headShippingDays.entrySet().stream()
                                .filter(h -> shippingRatioMap.containsKey(h.getKey()) && shippingRatioMap.get(h.getKey()) > 0)
                                .min(Map.Entry.comparingByValue())
                                .orElseThrow(() -> new NullPointerException("头程时间为空"))
                                .getValue();
                        List<FactoryRemainInventoryDto> remainInventoryListForCal = new ArrayList<>(dto.getFactoryRemainInventoryList());

                        MockTableCalDto mockTableCalDto = new MockTableCalDto(new ArrayList<>(), calGap,
                                new ArrayList<>(), headShippingDays, dateTime, remainInventoryListForCal,
                                dto.getFactoryRemainInventoryList(), new ArrayList<>(), minShippingDate, targetSalesMap,
                                shippingRatioMap, sortedWarehouseList);
                        trialCalReplenishment = commonConsumptionService.calDeliveryResults(mockTableCalDto);
                    } else {
                        trialCalReplenishment = shippingCalculationService.trialShippingCalculation(startDate, dto.getChangeableSafeDays(),
                                new ArrayList<>(), dto.getFactoryRemainInventoryList(), virtualProduct, calGap,
                                targetSalesMap, dateTime, dto.getFinishedInventoryList(), baseParamDto, sortedWarehouseList,
                                selfProductInfo.getProductName(), senboWarehouseList);
                    }

                    return trialCalReplenishment;
                }, deliveryCalThreadPool)
                .thenApplyAsync(result -> {
                    String snapShotId = productSnapshotService.selecetAndSaveSnapList(virtualProduct.getId(), "徐波");
                    List<FactoryRemainInventoryDto> shippingInventoryList = result.getShippingInventoryList();
                    if (CollectionUtil.isNotEmpty(shippingInventoryList)) {
                        result.setShippingInventoryList(shippingInventoryList.stream().filter(f -> !f.getEnableUsingDate().after(DateUtils.convertToDate(dto.getCalEndDate()))).collect(Collectors.toList()));
                    }
                    shippingInventoryList = result.getShippingInventoryList();

                    if (!needReCalFactoryInventory) {
                        return DeliveryCalResultDto.builder()
                                .virtualSku(virtualProduct.getVirtualSku())
                                .snapShotId(snapShotId)
                                .everydayOnShippingInventoryMap(result.getEverydayOnShippingInventoryMap())
                                .everydaySaleProductMap(result.getEverydaySaleProductMap())
                                .everydayRemainInventoryMap(result.getEverydayRemainInventoryMap())
                                .shippingInventoryList(result.getShippingInventoryList())
                                .priorDeliveryList(result.getPriorDeliveryList())
                                .build();
                    }
                    // 获取工厂对应的发货量
                    List<FactoryRemainInventoryDto> newShippingInventoryList = new ArrayList<>(shippingInventoryList.size());
                    var factoryFinishedInventoryList = shippingSaveCalResultService.getFactoryFinishedInventoryDto(shippingInventoryList,
                            virtualProduct, dto.getTransitDays(), dto.getFinishedInventoryList(), newShippingInventoryList, headShippingDays,
                            result.getPriorDeliveryList());

                    int minShippingDate = headShippingDays.entrySet().stream()
                            .filter(h -> shippingRatioMap.containsKey(h.getKey()) && shippingRatioMap.get(h.getKey()) > 0)
                            .map(Map.Entry::getValue)
                            .sorted().findFirst().orElseThrow(() -> new NullPointerException("头程时间为空"));

                    // 根据最早发出日发货的数据重新计算出一份库存衰减数据，并返回
                    Map<String, Integer> sortedMap = senboWarehouseList.stream().collect(Collectors.toMap(SenboWarehouseDto::getSenboWarehouseId, SenboWarehouseDto::getSort));
                    List<String> sortedWarehouseList = WarehouseSortUtil.getSortedWarehouseList(baseParamDto.getShippingRatio(), sortedMap);

                    List<FactoryRemainInventoryDto> remainInventoryListForCal = new ArrayList<>(dto.getFactoryRemainInventoryList());

                    MockTableCalDto mockTableCalDto = new MockTableCalDto(newShippingInventoryList, calGap, new ArrayList<>(),
                            headShippingDays, dateTime, remainInventoryListForCal, dto.getFactoryRemainInventoryList(),
                            result.getPriorDeliveryList(), minShippingDate, targetSalesMap, shippingRatioMap, sortedWarehouseList);
                    TrialCalReplenishmentDto trialCalReplenishmentDto = commonConsumptionService.calDeliveryResults(mockTableCalDto);
                    return DeliveryCalResultDto.builder()
                            .factoryFinishedResultList(factoryFinishedInventoryList)
                            .virtualSku(virtualProduct.getVirtualSku())
                            .snapShotId(snapShotId)
                            .everydayOnShippingInventoryMap(trialCalReplenishmentDto.getEverydayOnShippingInventoryMap())
                            .everydaySaleProductMap(trialCalReplenishmentDto.getEverydaySaleProductMap())
                            .everydayRemainInventoryMap(trialCalReplenishmentDto.getEverydayRemainInventoryMap())
                            .shippingInventoryList(newShippingInventoryList)
                            .priorDeliveryList(result.getPriorDeliveryList())
                            .build();
                }, deliveryCalThreadPool)
                .thenApplyAsync(result -> {
                    // 剔除掉为数据为0的项，方便进行传输以及json转化
                    Map<String, Map<String, Double>> everydayOnShippingInventoryMap = result.getEverydayOnShippingInventoryMap();
                    cleanMap(everydayOnShippingInventoryMap);
                    Map<String, Map<String, Double>> everydayRemainInventoryMap = result.getEverydayRemainInventoryMap();
                    cleanMap(everydayRemainInventoryMap);
                    Map<String, Map<String, Double>> everydaySaleProductMap = result.getEverydaySaleProductMap();
                    cleanMap(everydaySaleProductMap);
                    return result;
                });
    }

    @Override
    public Set<String> getSnapIds(InteriorInfoQuery info) {

        Set<String> snapIds = factoryFinishedInventoryRepository.getSnapIds(info);
        snapIds.addAll(deliveryForeignInventoryRepository.getSnapIds(info));

        return snapIds;
    }
}
