package com.auth.authorization_server.repository;

import com.auth.authorization_server.mapper.UserDeptMapper;
import com.auth.authorization_server.model.dataObject.UserDeptDO;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class UserDeptRepositoryImpl extends ServiceImpl<UserDeptMapper, UserDeptDO> {


    /**
     * 查询部门是否存在用户
     *
     * @param deptId 部门ID
     * @return 结果
     */
    public Long checkDeptExistUser(String deptId){
        return baseMapper.selectCount(Wrappers.<UserDeptDO>lambdaQuery()
                .eq(UserDeptDO::getDeptId, deptId));
    };

    /**
     * 通过用户ID删除用户和部门关联
     *
     * @param userId 用户ID
     */
    public void deleteUserDeptByUserId(String userId) {
        baseMapper.delete(Wrappers.<UserDeptDO>lambdaQuery()
                .eq(UserDeptDO::getUserId, userId));
    }

    /**
     * 批量取消授权用户部门
     *
     * @param deptId  部门ID
     * @param userIds 需要删除的用户数据ID
     */
    public void deleteUserDeptInfos(String deptId, List<String> userIds) {
        baseMapper.delete(Wrappers.<UserDeptDO>lambdaQuery()
                .eq(UserDeptDO::getDeptId, deptId)
                .in(UserDeptDO::getUserId, userIds));
    }
}
