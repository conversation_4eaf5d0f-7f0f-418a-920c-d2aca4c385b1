package com.auth.authorization_server.repository;

import cn.hutool.core.util.StrUtil;
import com.auth.authorization_server.mapper.RoleMapper;
import com.auth.authorization_server.model.dataObject.RoleDO;
import com.auth.authorization_server.model.dataObject.RoleMenuDO;
import com.auth.authorization_server.model.dataObject.UserDO;
import com.auth.authorization_server.model.dataObject.UserRoleDO;
import com.auth.authorization_server.req.RoleParams;
import com.auth.authorization_server.domain.dto.RoleDTO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class RoleRepositoryImpl extends ServiceImpl<RoleMapper, RoleDO> {

    public static MPJLambdaWrapper<RoleDO> buildQueryWrapper() {
        MPJLambdaWrapper<RoleDO> LambdaWrapper = new MPJLambdaWrapper<>();
        LambdaWrapper.distinct()
                .selectAll(RoleDO.class)
                .leftJoin(UserRoleDO.class, UserRoleDO::getRoleId, RoleDO::getId)
                .leftJoin(UserDO.class, UserDO::getId, UserRoleDO::getUserId);
        return LambdaWrapper;
    }

    public IPage<RoleDTO> selectRoleList(RoleParams role) {
        IPage<RoleDTO> iPage = new Page<>(role.getCurrent(), role.getSize());
        return baseMapper.selectJoinPage(iPage, RoleDTO.class, buildQueryWrapper()
                .eq(StrUtil.isNotBlank(role.getId()), RoleDO::getId, role.getId())
                .like(StrUtil.isNotBlank(role.getRoleName()), RoleDO::getRoleName, role.getRoleName())
                .eq(StrUtil.isNotBlank(role.getRoleStatus()), RoleDO::getRoleStatus, role.getRoleStatus())
                .orderByDesc(RoleDO::getRoleSort)
                .orderByAsc(RoleDO::getRoleName));
    }

    public RoleDTO selectRoleById(String roleId) {
        return baseMapper.selectJoinOne(RoleDTO.class, buildQueryWrapper()
                .eq(RoleDO::getId, roleId));
    }

    /**
     * 校验角色名称是否唯一
     *
     * @param roleName 角色名称
     * @return 角色信息
     */
    public RoleDTO checkRoleNameUnique(String roleName) {
        return baseMapper.selectJoinOne(RoleDTO.class, buildQueryWrapper()
                .eq(RoleDO::getRoleName, roleName)
                .last("limit 1"));
    }


    /**
     * 根据用户ID查询角色
     *
     * @param userId 用户ID
     * @return 角色列表
     */
    public List<RoleDTO> selectRolePermissionByUserId(String userId) {
        return baseMapper.selectJoinList(RoleDTO.class, buildQueryWrapper()
                .eq(UserRoleDO::getUserId, userId));
    }

    ;

    /**
     * 根据菜单Id查询角色
     *
     * @param menuIds 菜单ID
     * @return 角色列表
     */
    public List<RoleDO> selectRolesByMenuIds(List<String> menuIds) {
        return baseMapper.selectJoinList(RoleDO.class, new MPJLambdaWrapper<RoleDO>()
                .leftJoin(RoleMenuDO.class, RoleMenuDO::getRoleId, RoleDO::getId)
                .in(RoleMenuDO::getMenuId, menuIds));
    }

    ;
}
