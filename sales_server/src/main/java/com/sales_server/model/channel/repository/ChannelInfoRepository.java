package com.sales_server.model.channel.repository;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.yulichang.base.MPJBaseServiceImpl;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.sales_server.model.channel.entity.dos.ChannelInfoDO;
import com.sales_server.model.channel.entity.dto.ChannelSearchDto;
import com.sales_server.model.channel.entity.form.ChannelSearchPageForm;
import com.sales_server.model.channel.entity.form.SaveChannelForm;
import com.sales_server.model.channel.entity.vo.ChannelPageVo;
import com.sales_server.model.channel.entity.vo.NicheChannelDetailVo;
import com.sales_server.model.channel.mapper.ChannelInfoMapper;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/4/30 14:53
 **/
@Service
public class ChannelInfoRepository extends MPJBaseServiceImpl<ChannelInfoMapper, ChannelInfoDO> {

    public void saveSaleChannel(String saleChannel) {
        ChannelInfoDO build = ChannelInfoDO.builder()
                .ancestors("0")
                .parentId("0")
                .isLeaf(0)
                .channelName(saleChannel)
                .build();
        baseMapper.insert(build);
    }

    public void saveNicheChannel(SaveChannelForm form) {
        ChannelInfoDO build = ChannelInfoDO.builder()
                .id(form.getChannelId())
                // 暂时只会有二级节点
                .ancestors(form.getParentChannelId())
                .parentId(form.getParentChannelId())
                .isLeaf(1)
                .channelName(form.getChannelName())
                .remarks(form.getRemarks())
                .sort(form.getSort())
                .build();
        baseMapper.insertOrUpdate(build);
    }

    public boolean checkSaleChannelIdExisted(String parentId) {
        return baseMapper.exists(Wrappers.<ChannelInfoDO>lambdaQuery()
                .eq(ChannelInfoDO::getId, parentId)
                .eq(ChannelInfoDO::getIsLeaf, 0));
    }

    public boolean checkSaleChannelNameExisted(String saleChannel) {
        return baseMapper.exists(Wrappers.<ChannelInfoDO>lambdaQuery()
                .eq(ChannelInfoDO::getChannelName, saleChannel)
                .eq(ChannelInfoDO::getIsLeaf, 0)
        );
    }

    public boolean checkNicheChannelNameExisted(SaveChannelForm form) {
        return baseMapper.exists(Wrappers.<ChannelInfoDO>lambdaQuery()
                .eq(ChannelInfoDO::getChannelName, form.getChannelName())
                .eq(ChannelInfoDO::getIsLeaf, 1)
                .ne(StrUtil.isNotBlank(form.getChannelId()), ChannelInfoDO::getId, form.getChannelId()));
    }

    public boolean checkNicheChannelSortExisted(SaveChannelForm form) {
        return baseMapper.exists(Wrappers.<ChannelInfoDO>lambdaQuery()
                .eq(ChannelInfoDO::getSort, form.getSort())
                .eq(ChannelInfoDO::getIsLeaf, 1)
                .ne(StrUtil.isNotBlank(form.getChannelId()), ChannelInfoDO::getId, form.getChannelId()));
    }

    public List<ChannelSearchDto> getAllChannelByLeaf(int isLeaf) {
        return baseMapper.selectJoinList(ChannelSearchDto.class, new MPJLambdaWrapper<ChannelInfoDO>()
                .selectAs(ChannelInfoDO::getId, "channelId")
                .select(ChannelInfoDO::getChannelName)
                .eq(ChannelInfoDO::getIsLeaf, isLeaf)
                .orderByDesc(isLeaf == 1, ChannelInfoDO::getSort)
        );
    }

    public List<ChannelInfoDO> getAllNicheChannel() {
        return baseMapper.selectList(Wrappers.<ChannelInfoDO>lambdaQuery()
                .eq(ChannelInfoDO::getIsLeaf, 1)
        );
    }

    public IPage<ChannelPageVo> getChannelPage(ChannelSearchPageForm form) {
        IPage<ChannelPageVo> iPage = new Page<>(form.getCurrent(), form.getSize());
        return baseMapper.selectJoinPage(iPage, ChannelPageVo.class, buildSearchWrapper(form));
    }

    public NicheChannelDetailVo getNicheChannelDetail(String nicheChannelId) {
        ChannelSearchPageForm channelSearchPageForm = new ChannelSearchPageForm();
        channelSearchPageForm.setNicheChannelId(nicheChannelId);
        return baseMapper.selectJoinOne(NicheChannelDetailVo.class, buildSearchWrapper(channelSearchPageForm));
    }

    private MPJLambdaWrapper<ChannelInfoDO> buildSearchWrapper(ChannelSearchPageForm form) {
        return new MPJLambdaWrapper<ChannelInfoDO>()
                .selectAs(ChannelInfoDO::getId, "nicheChannelId")
                .selectAs(ChannelInfoDO::getChannelName, "nicheChannelName")
                .selectAs(ChannelInfoDO::getParentId, "saleChannelId")
                .select(ChannelInfoDO::getSort)
                .select(ChannelInfoDO::getCreateDate)
                .select(ChannelInfoDO::getUpdateDate)
                .select(ChannelInfoDO::getRemarks)
                .leftJoin(ChannelInfoDO.class, ChannelInfoDO::getId, ChannelInfoDO::getParentId,
                        ext -> ext.selectAs(ChannelInfoDO::getChannelName, "saleChannelName")
                )
                .eq(StrUtil.isNotBlank(form.getNicheChannelId()), ChannelInfoDO::getId, form.getNicheChannelId())
                .eq(StrUtil.isNotBlank(form.getSaleChannelId()), ChannelInfoDO::getParentId, form.getSaleChannelId())
                .eq(ChannelInfoDO::getIsLeaf, 1)
                .orderByDesc(ChannelInfoDO::getSort);
    }

    public List<ChannelPageVo> getChannelList(ChannelSearchPageForm form) {
        return baseMapper.selectJoinList(ChannelPageVo.class, buildSearchWrapper(form));
    }
}
