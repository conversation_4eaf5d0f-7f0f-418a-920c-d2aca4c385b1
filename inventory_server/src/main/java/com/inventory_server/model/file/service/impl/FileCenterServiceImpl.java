package com.inventory_server.model.file.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.crafts_mirror.common.security.utils.SecurityUtils;
import com.crafts_mirror.utils.common.entity.MissionCenterVo;
import com.crafts_mirror.utils.enums.ResponseCodeEnum;
import com.crafts_mirror.utils.utils.RestTemplateUtils;
import com.inventory_server.applications.vo.response.ResultDTO;
import com.inventory_server.infrastructures.entity.form.FileMissionForm;
import com.inventory_server.infrastructures.exception.BusinessException;
import com.inventory_server.model.file.entity.form.FileMissionPageForm;
import com.inventory_server.model.file.service.IFileCenterService;
import com.inventory_server.model.product.entity.vo.UserInteriorVO;
import com.inventory_server.model.system.service.ISysUserInteriorService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.crafts_mirror.utils.constant.SystemConstant.*;

@Service
@Slf4j
public class FileCenterServiceImpl implements IFileCenterService {

    @Resource
    private RestTemplate restTemplate;

    @Resource
    private ISysUserInteriorService sysUserService;

    @Override
    public MultiValueMap<String, Object> putFile(byte[] byteArrayResource, String fileName, String key, String expireTime) {
        try {
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.MULTIPART_FORM_DATA);
            MultiValueMap<String, Object> params = new LinkedMultiValueMap<>();
            ByteArrayResource byteFile  = new ByteArrayResource(byteArrayResource) {
                @Override
                public String getFilename() {
                    return fileName;
                }
            };
            params.clear();
            params.add("avatar", byteFile);
            params.add("key", key);
            params.add("expireTime", expireTime);
            return params;
        }catch (Exception e){
            log.error("上传文件异常", e);
            return null;
        }
    }

    @Override
    @Async(value = "fileUploadThreadPool")
    public void uploadFile(MultiValueMap<String, Object> httpEntity, RestTemplateUtils restTemplateUtil) {
        restTemplateUtil.post(httpEntity, ResultDTO.class, FILE_SYSTEM_PUT_OBJECT_BY_FILE);
    }

    @Override
    public IPage<MissionCenterVo> getMissionPage(FileMissionPageForm form) {
        RestTemplateUtils restTemplateUtil = new RestTemplateUtils(restTemplate, SecurityUtils.getToken());
        ResultDTO page = restTemplateUtil.post(form, ResultDTO.class, FILE_SYSTEM_MISSION_CENTER_MISSION_PAGE_LIST);
        IPage iPage = JSON.to(IPage.class, page.getData());
        List list = JSON.to(List.class, iPage.getRecords());
        if(CollectionUtil.isEmpty(list)) {
            return new Page<>(form.getCurrent(), form.getSize(), 0);
        }
        IPage<MissionCenterVo> resultPage = new Page<>();
        List<MissionCenterVo> missionCenterVoList = new ArrayList<>();
        List<UserInteriorVO> userList = sysUserService.getUserList();
        Map<String, String> userMap = userList.stream().collect(Collectors.toMap(UserInteriorVO::getUserName, UserInteriorVO::getNickName));

        for(var i : list) {
            MissionCenterVo missionCenterVo = JSON.to(MissionCenterVo.class, i);
            missionCenterVo.setCreateBy(userMap.getOrDefault(missionCenterVo.getCreateBy(), missionCenterVo.getCreateBy()));
            missionCenterVo.setFilePath(null);
            missionCenterVoList.add(missionCenterVo);
        }
        resultPage.setRecords(missionCenterVoList);
        resultPage.setTotal(iPage.getTotal());
        resultPage.setCurrent(iPage.getCurrent());
        resultPage.setSize(iPage.getSize());
        return resultPage;
    }

    public String uploadMissionStatus(FileMissionForm form) {
        var restTemplateUtil = new RestTemplateUtils(restTemplate, SecurityUtils.getToken());
        ResultDTO<String> restResult = restTemplateUtil.post(form, ResultDTO.class, FILE_SYSTEM_MISSION_CENTER_URL);
        if (!Objects.equals(restResult.getStatus(), ResponseCodeEnum.OK.getCode())) {
            log.error("导入补货计划时插入文件中心失败，异常原因：{}", restResult.getMessage());
            throw new BusinessException(ResponseCodeEnum.BAD_REQUEST, "导入补货计划时插入文件中心失败，异常原因：" + restResult.getMessage());
        }
        return restResult.getData();
    }
}
