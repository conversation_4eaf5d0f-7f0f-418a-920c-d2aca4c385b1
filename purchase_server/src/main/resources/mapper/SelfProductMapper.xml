<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.purchase.purchase_server.mapper.SelfProductMapper">


    <select id="selectSelfProductListAndDeleted" resultType="com.purchase.purchase_server.entity.dataObject.SelfProductDO" parameterType="arraylist">
        select * from cm_self_product where status in ('0', '1') and id in
        <foreach collection="selfProductIds" item="item" index="index" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>

</mapper>