package com.crafts_mirror.utils.enums.product;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

@Getter
@AllArgsConstructor
public enum VirtualSubTypeEnum {
    TRAFFIC(0, "流量子体"),
    CONVERSION(1, "转化子体"),
    PROFIT(2, "利润子体"),
    THREE_NO(3, "三无子体"),
    TEST(4, "测试子体");

    private final Integer code;
    private final String desc;

    public static VirtualSubTypeEnum ofDesc(String value) {
        return Arrays.stream(VirtualSubTypeEnum.values())
                .filter(it -> it.getDesc().equals(value))
                .findFirst()
                .orElse(null);
    }
    public static VirtualSubTypeEnum ofCode(Integer code) {
        if (code == null){
            return null;
        }
        return Arrays.stream(VirtualSubTypeEnum.values())
                .filter(it -> it.getCode().equals(code))
                .findFirst()
                .orElse(null);
    }
}
