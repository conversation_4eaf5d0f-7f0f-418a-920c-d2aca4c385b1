package com.inventory_server.model.redundancy.entity.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;

/**
 * @Description 导入excel 日销
 * <AUTHOR>
 * @Date 2024/5/8 11:39
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ImportActualDestinationSalesDto implements Serializable {

    @Serial
    private static final long serialVersionUID = 275144831792663643L;
    private String virtualSku;

    private String sevenDaySales;

    private String fourteenDaySales;

    private String thirtyDaySales;

    private String actualDailySales;
}
