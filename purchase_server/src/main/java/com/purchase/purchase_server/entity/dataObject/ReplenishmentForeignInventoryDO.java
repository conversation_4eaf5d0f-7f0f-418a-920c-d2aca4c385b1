package com.purchase.purchase_server.entity.dataObject;

import com.baomidou.mybatisplus.annotation.TableName;
import com.purchase.purchase_server.entity.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.Date;


/**
 * @Date 2024/5/8 9:15
 **/
@EqualsAndHashCode(callSuper = true)
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@TableName("cm_replenishment_foreign_inventory")
public class ReplenishmentForeignInventoryDO extends BaseEntity {

  private Date enableUsingDate;
  private Date startShippingDate;
  private String warehouseId;
  private String virtualSku;
  private String shipmentCode;
  private String replenishmentVirtualPurchaseId;
  private Integer storeNum;
  /**
   * 备注
   */
  private String remarks;
}
