package com.purchase.purchase_server.repository.dataRepository.Lcl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.purchase.purchase_server.entity.dataObject.Lcl.LclContainerDetailDO;
import com.purchase.purchase_server.entity.dataObject.Lcl.LclContainerInfoDO;
import com.purchase.purchase_server.entity.dataObject.ProductSnapshotDO;
import com.purchase.purchase_server.entity.dto.Lcl.LclContainerDetailExportDTO;
import com.purchase.purchase_server.entity.form.InteriorInfoQuery;
import com.purchase.purchase_server.entity.form.LclSearchPageForm;
import com.purchase.purchase_server.mapper.Lcl.LclContainerDetailMapper;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2025/5/25 16:13
 **/
@Service
public class LclContainerDetailRepositoryImpl extends ServiceImpl<LclContainerDetailMapper, LclContainerDetailDO> {

    public List<LclContainerDetailDO> getLclContainerDetailProductSnapshotIdByRecordId(LclSearchPageForm form) {
        return baseMapper.selectJoinList(LclContainerDetailDO.class, new MPJLambdaWrapper<LclContainerDetailDO>()
                .distinct()
                .selectAll(LclContainerDetailDO.class)
                .leftJoin(LclContainerInfoDO.class, LclContainerInfoDO::getId, LclContainerDetailDO::getContainerInfoId)
                .eq(StrUtil.isNotBlank(form.getRecordId()), LclContainerInfoDO::getLclRecordId, form.getRecordId())
                .eq(StrUtil.isNotBlank(form.getContainerId()), LclContainerDetailDO::getContainerInfoId, form.getContainerId())
                .like(StrUtil.isNotBlank(form.getContractCode()), LclContainerDetailDO::getContractCode, form.getContractCode())
                .eq(StrUtil.isNotBlank(form.getContainerId()), LclContainerDetailDO::getContainerInfoId, form.getContainerId())
                .eq(form.getIsPackageFull() != null, LclContainerDetailDO::getIsPackageFull, form.getIsPackageFull())
                .between(StrUtil.isNotBlank(form.getFactoryFinishedStartDate()) && StrUtil.isNotBlank(form.getFactoryFinishedEndDate()),
                        LclContainerDetailDO::getFactoryFinishedDate, form.getFactoryFinishedStartDate(), form.getFactoryFinishedEndDate())
        );
    }

    public List<String> getContainerIdList(List<String> detailIdList, String recordId) {
        if (CollectionUtil.isEmpty(detailIdList)) {
            return new ArrayList<>();
        }
        return baseMapper.selectJoinList(String.class, new MPJLambdaWrapper<LclContainerDetailDO>()
                .select(LclContainerInfoDO::getId)
                .eq(LclContainerInfoDO::getLclRecordId, recordId)
                .leftJoin(LclContainerInfoDO.class, LclContainerInfoDO::getId, LclContainerDetailDO::getContainerInfoId)
                .in(CollectionUtil.isNotEmpty(detailIdList), LclContainerDetailDO::getId, detailIdList)
        );
    }

    public int deleteByLclRecordId(String lclRecordId) {
        return baseMapper.deleteByLclRecordId(lclRecordId);
    }

    public List<LclContainerDetailExportDTO> listByLclInfoIds(List<String> infoIds) {
        return baseMapper.selectJoinList(LclContainerDetailExportDTO.class, new MPJLambdaWrapper<LclContainerDetailDO>()
                .selectAll(LclContainerDetailDO.class)

                .select("t1.self_data->>'$.productName' as product_name")
                .select("t1.self_data->>'$.sku' as self_sku")
                .select("t1.self_data->>'$.containerLoad' as container_load")
                .select("t1.self_data->>'$.caseLength' as case_length")
                .select("t1.self_data->>'$.caseWidth' as case_width")
                .select("t1.self_data->>'$.caseHeight' as case_height")
                .select("t1.self_data->>'$.singleCaseGrossWeight' as single_case_gross_weight")
                .select("t1.self_data->>'$.commodityInspection' as commodity_inspection")
                .select("t1.self_data->>'$.buyer' as buyer")
                .select("CASE t1.self_data->>'$.taxes' WHEN '1' THEN t1.self_data->>'$.priceWithTaxes' WHEN '0' THEN t1.self_data->>'$.price' END as unit_or_contract_price")
                .select("t1.self_data->>'$.pcsType' as pcs_type")

                .select("t1.virtual_data->>'$.channel' as channel")
                .select("t1.virtual_data->>'$.operator' as operator")
                .select("CASE t.is_old_status WHEN '1' THEN t1.virtual_data->>'$.oldSku' WHEN '0' THEN t1.virtual_sku END as destination_sku")

                .select("t1.factory_data->>'$.addressCode' as address_code")
                .select("t1.factory_data->>'$.purchaser' as purchaser")
                .select("t1.factory_data->>'$.orderTracker' as order_tracker")
                .select("t1.factory_data->>'$.currency' as currency")

                .leftJoin(ProductSnapshotDO.class, ProductSnapshotDO::getId, LclContainerDetailDO::getProductSnapshotId)
                .in(LclContainerDetailDO::getContainerInfoId, infoIds)
        );
    }

    public List<LclContainerDetailDO> getContainerDetailList(List<String> detailIdList, String containerId) {
        return baseMapper.selectList(Wrappers.<LclContainerDetailDO>lambdaQuery()
                .in(CollectionUtil.isNotEmpty(detailIdList), LclContainerDetailDO::getId, detailIdList)
                .eq(LclContainerDetailDO::getContainerInfoId, containerId)
        );
    }

    public List<LclContainerDetailDO> getContainerDetailList(String factoryFinishedId) {
        return baseMapper.selectList(Wrappers.<LclContainerDetailDO>lambdaQuery()
                .eq(LclContainerDetailDO::getConsolidationFactoryFinishedId, factoryFinishedId)
        );
    }

    public Set<String> getSnapIds(InteriorInfoQuery infoQuery) {
        List<LclContainerDetailDO> list = baseMapper.selectList(Wrappers.<LclContainerDetailDO>lambdaQuery()
                .select(LclContainerDetailDO::getProductSnapshotId)
                .between(StrUtil.isNotBlank(infoQuery.getCreateStartDate()) && StrUtil.isNotBlank(infoQuery.getCreateEndDate()),
                        LclContainerDetailDO::getCreateDate, infoQuery.getCreateStartDate(), infoQuery.getCreateEndDate()));
        return CollectionUtil.isEmpty(list) ? Collections.emptySet() :
                list.stream()
                        .map(LclContainerDetailDO::getProductSnapshotId)
                        .collect(Collectors.toSet());
    }

}
