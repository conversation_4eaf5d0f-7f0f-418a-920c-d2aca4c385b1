package com.purchase.purchase_server.service.factory;


import com.purchase.purchase_server.entity.dataObject.FactoryFinancialDO;
import com.purchase.purchase_server.entity.dto.factory.FactoryFinancialDto;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/6/6
 **/
public interface IFactoryFinancialService {

    FactoryFinancialDO getByFactoryInfoId(String factoryInfoId);
    boolean insertOrUpdateFinancialInfo(List<FactoryFinancialDto> factoryFinancialDOList);

    void deleteByFactoryInfoId(String factoryInfoId);
}
