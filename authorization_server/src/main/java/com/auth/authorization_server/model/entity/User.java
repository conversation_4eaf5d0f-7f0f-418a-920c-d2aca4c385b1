package com.auth.authorization_server.model.entity;

import lombok.Builder;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * @Description 用户
 * <AUTHOR>
 * @Date 2023/12/2 14:06
 **/
@Data
@Builder
public class User {
    private String id;
    private String name;
    private UserForLogin userForLogin;
    private Boolean enabled;
    private LocalDateTime lastLoginTime;
    private String salt;

    //public static User createUser(UserDO userDO) {
    //    if(userDO == null) {
    //        return null;
    //    }
    //    return User.builder()
    //            .id(userDO.getId())
    //            .name(userDO.getName())
    //            .salt(userDO.getSalt())
    //            .enabled(userDO.getEnabled())
    //            .lastLoginTime(userDO.getLastLoginTime())
    //            .userForLogin(new UserForLogin(userDO.getUsername(), new PasswordDP(userDO.getPassword(), userDO.getSalt())))
    //            .build();
    //}
}
