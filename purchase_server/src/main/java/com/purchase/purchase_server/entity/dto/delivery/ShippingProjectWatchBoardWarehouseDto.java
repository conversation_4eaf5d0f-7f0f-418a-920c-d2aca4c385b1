package com.purchase.purchase_server.entity.dto.delivery;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @Description 试算看板仓库数据
 * <AUTHOR>
 * @Date 2024/1/10 13:57
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ShippingProjectWatchBoardWarehouseDto implements Serializable {

    private String shippingProjectId;

    private String trialId;

    private String contractCode;

    private Integer shippingAmount;

    /**
     * 发货量统计区间
     */
    private String deliveryDateRange;

    private String warehouse;

    private String arrivalDate;

    private String shippingStartDate;

    private String remarks;

    private Integer perCaseNum;

    private Integer caseAmount;

    private Double outCaseLength;

    private Double outCaseWidth;

    private Double outCaseHeight;

    private Double volume;

    private Double weight;

    private String factoryFinishedDate;

    private String factoryFinishedId;

    private Integer deliveryType;

}
