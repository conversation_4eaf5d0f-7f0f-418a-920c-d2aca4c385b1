package com.product.product_server.repository.httpRepository;

import com.product.product_server.model.currency.dto.AllRatesResponseDto;
import com.product.product_server.model.currency.dto.ExchangeRateDto;

public interface ICurrencyExchangeRateHttpRepository {

    AllRatesResponseDto getExchangeRateFromNetworkFromUSD(String toCurrency);

    ExchangeRateDto getOfficialMiddleExchangeRate(String fromCurrency);
}
