package com.purchase.purchase_server.entity.bo;

import com.purchase.purchase_server.entity.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.io.Serial;
import java.util.Date;

/**
 * @Description 补货计划-虚拟sku
 * <AUTHOR>
 * @Date 2024/1/18 17:16
 **/
@EqualsAndHashCode(callSuper = true)
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class RepVirtualSkuDateBO extends BaseEntity {


    @Serial
    private static final long serialVersionUID = 4334860997848660438L;
    private String replenishmentProjectId;

    /**
     * 图片
     */
    private String image;
    /**
     * 自定义sku
     */
    private String selfSku;
    /**
     * 老sku
     */
    private String oldSku;
    /**
     * 子体类型
     */
    private String subType;
    /**
     * 产品状态
     */
    private String productStatus;
    /**
     * 产品类型
     */
    private String productType;
    /**
     * 产品名称
     */
    private String productName;
    /**
     * 供应商代码
     */
    private String factoryCode;
    /**
     * 虚拟sku
     */
    private String destinationSku;
    /**
     * 渠道
     */
    private String channel;
    /**
     * 运营
     */
    private String operator;
    /**
     * 海外仓库存
     */
    private String overseasInventory;
    /**
     * 海外仓在途
     */
    private String overseasShipping;
    /**
     * 本地库存
     */
    private String localInventory;
    /**
     * 断货日
     */
    private String soldOutDate;

    /**
     * 补货状态（0：正常；1：需补货）
     */
    private String replenishmentStatus;
    /**
     * 理论补货量
     */
    private Integer advicePurchaseNum;
    /**
     * 建议补货量
     */
    private Double actualReplenishmentNum;
    /**
     * 原因
     */
    private String reason;
    /**
     * 运营确认量
     */
    private Double operationConfirmedNum;
    /**
     * 运营确认量
     */
    private String operationConfirmedStatus;
    /**
     * 运营确认原因
     */
    private String confirmedReason;
    /**
     * 运营补充说明
     */
    private String operatorRemark;
    /**
     * 已采纳采购量
     */
    private Integer acceptedPurchaseNum;
    /**
     * 补货规则id
     */
    private String rulesId;
    /**
     * 目标日销
     */
    private String saleDestination;

    /**
     * 实际日销
     */
    private Double actualDailySalesNum;

    /**
     * 试算当月目标日销
     */
    private Double targetSalesNum;

    /**
     * 子体达成率
     */
    private Double subEntityRate;

    /**
     * 父体达成率
     */
    private Double parentEntityRate;

    /**
     * 全链路海外仓理论售罄时间
     */
    private Date fullLinkTheoreticalSoldOutDate;

    /**
     * 全链路售罄前断货天数
     */
    private Integer fullLinkDaysBeforeSoldOut;

    /**
     * 预计售罄时间
     */
    private String expectedSoldOutDate;
    /**
     * 采购人员
     */
    private String purchaser;
    /**
     * 产品经理
     */
    private String buyer;

}
