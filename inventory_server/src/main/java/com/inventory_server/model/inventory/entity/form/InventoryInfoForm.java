package com.inventory_server.model.inventory.entity.form;

import com.alibaba.fastjson2.annotation.JSONField;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * @Description 获取库存信息请求类
 * <AUTHOR>
 * @Date 2024/7/10 9:30
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class InventoryInfoForm implements Serializable {
    /**
     * 当前页，默认1
     */
    private Integer page;

    /**
     * 每页条数，最大100
     * 默认50
     */
    @JSONField(name = "page_size")
    private Integer pageSize;


    /**
     * 产品SKU(多个) 格式：["SKU1","SKU2"]
     */
    @JSONField(name = "product_sku")
    private List<String> productSku;

    /**
     * 产品SKU模糊查询(多个)
     */
    @JSONField(name = "product_sku_like")
    private List<String> productSkuLike;

    /**
     * 产品名称
     */
    @JSONField(name = "product_title")
    private String productTitle;
    /**
     * 产品名称模糊查询
     */
    @JSONField(name = "product_title_like")
    private String productTitleLike;
    /**
     * 产品英文名称模糊查询
     */
    @JSONField(name = "product_title_en_like")
    private String productTitleEnLike;
    /**
     * 产品销售状态Id
     */
    @JSONField(name = "sale_status")
    private Integer saleStatus;
    /**
     * 仓库Id
     */
    @JSONField(name = "warehouse_id")
    private Integer warehouseId;

    /**
     * 仓库代码
     */
    @JSONField(name = "warehouse_code")
    private String warehouseCode;

    /**
     * 默认供应商代码
     */
    @JSONField(name = "default_supplier_code")
    private String defaultSupplierCode;

    /**
     * 产品创建时间-开始时间（格式：YYYY-MM-DD）
     */
    @JSONField(name = "product_add_time_from")
    private String productAddTimeFrom;
    /**
     * 产品创建时间-截止时间（格式：YYYY-MM-DD）
     */
    @JSONField(name = "product_add_time_to")
    private String productAddTimeTo;
    /**
     * 起始更新时间（格式：YYYY-MM-DD）
     */
    @JSONField(name = "update_time_from")
    private String updateTimeFrom;
    /**
     * 结束更新时间（格式：YYYY-MM-DD）
     */
    @JSONField(name = "update_time_to")
    private String updateTimeTo;

    /**
     * 是否缺货：
     * 0：全部
     * 1：是
     * 2：否
     */
    @JSONField(name = "is_out_stock")
    private Integer isOutStock;

}
