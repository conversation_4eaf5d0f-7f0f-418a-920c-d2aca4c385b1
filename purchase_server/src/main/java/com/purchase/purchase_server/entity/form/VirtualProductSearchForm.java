package com.purchase.purchase_server.entity.form;

import lombok.*;

import java.io.Serial;
import java.util.List;

/**
 * @Description 虚拟sku列表搜索表单
 * <AUTHOR>
 * @Date 2023/12/14 16:08
 **/
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class VirtualProductSearchForm extends BasePageForm {

    @Serial
    private static final long serialVersionUID = -1644207554472702093L;

    /**
     * 虚拟sku或老sku
     */
    private String destinationSku;
    /**
     * sku
     */
    private String sku;

    private List<String> selfSkuList;
}
