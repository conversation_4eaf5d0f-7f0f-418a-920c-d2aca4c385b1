package com.purchase.purchase_server.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

@Getter
@AllArgsConstructor
public enum ReplenishmentOperationReasonEnum {
    CONFIRMED(0, "确认"),
    NEW_PRODUCT_OR_TESTING(1, "新品/测款"),
    REORDER_AFTER_TESTING(2, "测款翻单"),
    PRODUCT_OUT_OF_STOCK(3, "产品断货"),
    STOCK_CONTROL_FOR_DAILY_SALES(4, "缺货控日销"),
    PRODUCT_UPGRADE_OR_TRANSITION(5, "产品升级/新老过渡"),
    CONFIDENT_TO_ACHIEVE_GOAL(6, "有信心达成目标");

    private final Integer code;
    private final String desc;

    public static ReplenishmentOperationReasonEnum ofCode(Integer code) {
        if (code == null) {
            return null;
        }
        return Arrays.stream(ReplenishmentOperationReasonEnum.values())
                .filter(it -> it.getCode().equals(code))
                .findFirst()
                .orElse(null);
    }
}
