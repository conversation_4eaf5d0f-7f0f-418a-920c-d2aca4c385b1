package com.inventory_server.applications.dto;

import com.inventory_server.model.redundancy.entity.dto.InterventionalTimeDto;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/5/10
 **/
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class InventoryInfoDto implements Serializable {

    @Serial
    private static final long serialVersionUID = 1964499559051567441L;

    private String inventoryInfoId;
    /**
     * 产品图片
     */
    private String image;
    /**
     * 虚拟sku
     */
    private String virtualSku;
    /**
     * 自定义skuId
     */
    private String selfSkuId;
    /**
     * 自定义sku
     */
    private String selfSku;
    /**
     * 品名
     */
    private String productName;
    /**
     * 工厂库存
     */
    private Map<String, Integer> factoryInventory;
    /**
     * 海外仓在途
     */
    private Map<String, Integer> foreignInTransit;
    /**
     * 海外仓库存
     */
    private Map<String, Integer> foreignInventory;
    /**
     * 海外仓理论售罄时间
     */
    private String foreignTheoreticalSoldOutDate;

    /**
     * 缺货量
     */
    private Double lackNum;

    /**
     * 售罄前断货天数
     */
    private Integer daysBeforeSoldOut;

    /**
     * 全链路海外仓理论售罄时间
     */
    private String fullLinkForeignTheoreticalSoldOutDate;

    /**
     * 全链路缺货量
     */
    private Double fullLinkLackNum;

    /**
     * 售罄前断货天数
     */
    private Integer fullLinkDaysBeforeSoldOut;

    /**
     * 海外仓冗余库存
     */
    private Double foreignRedundantInventory;

    /**
     * 在途冗余库存
     */
    private Integer onShippingRedundantInventory;

    /**
     * 工厂冗余库存
     */
    private Double factoryRedundantInventory;
    /**
     * 可干预时间
     */
    private InterventionalTimeDto leadTime;
    /**
     * 更新时间
     */
    private String updateDate;
    /**
     * 试算结束时间
     */
    private Date calFinishedDate;
    /**
     * 渠道
     */
    private String channel;

    /**
     * 子体类型
     */
    private Integer subType;
    /**
     * 产品类型
     */
    private Integer productType;
    /**
     * 产品状态
     */
    private Integer productStatus;
    /**
     * 运营
     */
    private String operatorList;

    /**
     * 被升级款虚拟sku
     */
    private String formerSku;

    /**
     * 目标日销
     */
    private Map<String, Double> monthTargetSalesMap;

    /**
     * 7日销量
     */
    private String sevenDaySales;
    /**
     * 14日销量
     */
    private String fourteenDaySales;
    /**
     * 30日销量
     */
    private String thirtyDaySales;
    /**
     * 实际日均销量
     */
    private String actualDailySales;
    /**
     * 目标日销
     */
    private String targetSalesNum;
    /**
     * 子体达成率
     */
    private String subEntityRate;
    /**
     * 全链路可售天数
     */
    private String fullLinkSalableDays;

    /**
     * 海外仓可售天数
     */
    private String salableDays;
    /**
     * 借货策略
     */
    private Integer borrowingStrategy;
}
