package com.purchase.purchase_server.enums.lcl;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum LclStatusEnum {
    DELETE("-3","删除"),
    INVALID("-2", "作废"),
    CANCELLED("-1", "已取消"),
    DRAFT("0", "草稿"),
    DATA_PROCESSING("1", "数据整理中"),
    PENDING_CONTAINER("2", "待排柜"),
    CONTAINER_PROCESSING("3", "排柜中"),
    PRETENDING_SAVE("4", "待保存"),
    SAVED("5", "已保存");

    private final String code;
    private final String desc;
}
