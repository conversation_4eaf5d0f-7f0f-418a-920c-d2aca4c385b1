package com.demo_server.model.file.service.impl;

import com.crafts_mirror.utils.utils.RestTemplateUtils;
import com.crafts_mirror.utils.web.domain.ResultDTO;
import com.demo_server.model.file.service.FileCenterService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;

import static com.crafts_mirror.utils.constant.SystemConstant.FILE_SYSTEM_PUT_OBJECT_BY_FILE;

@Service
@Slf4j
public class FileCenterServiceImpl implements FileCenterService {


    @Override
    public MultiValueMap<String, Object> putFile(byte[] byteArrayResource, String fileName, String key) {
        try {
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.MULTIPART_FORM_DATA);
            MultiValueMap<String, Object> params = new LinkedMultiValueMap<>();
            ByteArrayResource byteFile  = new ByteArrayResource(byteArrayResource) {
                @Override
                public String getFilename() {
                    return fileName;
                }
            };
            params.clear();
            params.add("avatar", byteFile);
            params.add("key", key);
            return params;
        }catch (Exception e){
            log.error("上传文件异常", e);
            return null;
        }
    }

    @Override
    @Async(value = "fileUploadThreadPool")
    public void uploadFile(MultiValueMap<String, Object> httpEntity, RestTemplateUtils restTemplateUtil) {
        restTemplateUtil.post(httpEntity, ResultDTO.class, FILE_SYSTEM_PUT_OBJECT_BY_FILE);
    }
}
