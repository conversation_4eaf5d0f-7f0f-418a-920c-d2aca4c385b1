package com.purchase.purchase_server.entity.vo.Lcl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.purchase.purchase_server.entity.dto.SenboWarehouseDto;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.util.List;

/**
 * 装柜列表vo
 *
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
public class LclConsolidationPageVO extends Page<LclConsolidationFinishedInventoryVO> {

    @Serial
    private static final long serialVersionUID = -4980601308687529376L;

    private List<SenboWarehouseDto> dynamicHeadSet;

    private LclPageTotalVO lclPageTotalVO;

    private String shippingStartDate;

    private String shippingEndDate;

    /**
     * 是否整理过
     */
    private String isLclConsolidation;
    /**
     * 判断处理中
     */
    private Boolean isDataClean;

    /**
     * 状态
     */
    private String lclStatus;

    public LclConsolidationPageVO(String isLclConsolidation,Boolean isDataClean,long current, long size, List<SenboWarehouseDto> dynamicHeadSet,
                                  String shippingStartDate, String shippingEndDate,String lclStatus) {
        super(current, size);
        this.isLclConsolidation = isLclConsolidation;
        this.isDataClean = isDataClean;
        this.shippingStartDate = shippingStartDate;
        this.shippingEndDate = shippingEndDate;
        this.dynamicHeadSet = dynamicHeadSet;
        this.lclStatus = lclStatus;
    }

    public LclConsolidationPageVO(String isLclConsolidation,Boolean isDataClean,String lclStatus,
                                  String shippingStartDate, String shippingEndDate) {
        this.isLclConsolidation = isLclConsolidation;
        this.isDataClean = isDataClean;
        this.lclStatus = lclStatus;
        this.shippingStartDate = shippingStartDate;
        this.shippingEndDate = shippingEndDate;
    }

}