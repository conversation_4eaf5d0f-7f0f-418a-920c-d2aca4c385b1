package com.inventory_server.model.warning.unsalable.entity.dos;

import com.baomidou.mybatisplus.annotation.TableName;
import com.inventory_server.infrastructures.entity.PhysicalBaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;


/**
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/5/8 9:15
 **/
@EqualsAndHashCode(callSuper = true)
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@TableName("cm_warning_foreign_unsalable_inventory")
public class WarningForeignUnsalableInventoryDO extends PhysicalBaseEntity {

  private String foreignStoreId;
  private String unsalableInventoryId;
  private Integer unsalableNum;

}
