package com.purchase.purchase_server.entity.dto.Lcl;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.Date;
import java.util.List;

/**
 * 拼柜装柜计划清理数据表
 * @TableName cm_lcl_consolidation_finished_inventory
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class LclConsolidationFinishedInventoryDTO {

    private String lclFinishedInventoryId;
    /**
     * 合同号
     */
    private String contractCode;

    /**
     * 工厂交货时间
     */
    private Date factoryFinishedDate;

    /**
     * 拼柜计划id 
     */
    private String lclRecordId;

    /**
     * 虚拟skuId
     */
    private String virtualSkuId;

    /**
     * sku类型(0-虚拟sku 1-老sku)
     */
    private String isOldStatus;

    /**
     * 快照记录id
     */
    private String productSnapshotId;

    private List<LclConsolidationTrialShippingInventoryDTO> lclConTrialShippingInventoryDTOS;

}