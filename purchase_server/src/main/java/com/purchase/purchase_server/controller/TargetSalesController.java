package com.purchase.purchase_server.controller;

import com.crafts_mirror.common.security.annotation.RequiresPermissions;
import com.purchase.purchase_server.aop.PreventReSubmit;
import com.purchase.purchase_server.entity.LogTrackNumDto;
import com.purchase.purchase_server.entity.response.ResultDTO;
import com.purchase.purchase_server.model.targetSales.entity.form.DaySalesSynchronizeForm;
import com.purchase.purchase_server.service.targetSales.ITargetSalesService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @Description 目标销售Controller层
 * <AUTHOR>
 * @Date 2025/3/17 17:27
 **/
@RestController
@RequestMapping(value = "targetSales")
@Slf4j
public class TargetSalesController {

    @Resource
    private ITargetSalesService targetSalesService;

    @RequiresPermissions("sales:targetDaySales:edit")
    @PostMapping("/synchronize/targetSales")
    @PreventReSubmit
    public ResultDTO<Void> synchronizeTargetSales(@RequestBody DaySalesSynchronizeForm form){
        targetSalesService.updateTargetSales(form, new LogTrackNumDto());
        return ResultDTO.success();
    }
}
