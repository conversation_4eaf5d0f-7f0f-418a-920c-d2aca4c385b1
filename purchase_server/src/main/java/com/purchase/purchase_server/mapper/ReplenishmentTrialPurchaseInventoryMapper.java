package com.purchase.purchase_server.mapper;

import com.github.yulichang.base.MPJBaseMapper;
import com.purchase.purchase_server.entity.bo.AdvicePurchaseDateBO;
import com.purchase.purchase_server.entity.bo.RepExcelTrialPurchaseBO;
import com.purchase.purchase_server.entity.dataObject.ReplenishmentTrialPurchaseInventoryDO;
import com.purchase.purchase_server.entity.dto.RepProjectDto;
import com.purchase.purchase_server.entity.form.AdvicePurchaseForm;
import com.purchase.purchase_server.entity.form.ReplenishmentProjectForm;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【cm_replenishment_trial_purchase_inventory】的数据库操作Mapper
 * @createDate 2024-01-17 10:45:51
 * @Entity generator.domain.CmReplenishmentTrialPurchaseInventory
 */
public interface ReplenishmentTrialPurchaseInventoryMapper extends MPJBaseMapper<ReplenishmentTrialPurchaseInventoryDO> {

    void deleteByVirtualSkuPurchaseIds(@Param("virtualSkuPurchaseIds") List<String> virtualSkuPurchaseIds);
}




