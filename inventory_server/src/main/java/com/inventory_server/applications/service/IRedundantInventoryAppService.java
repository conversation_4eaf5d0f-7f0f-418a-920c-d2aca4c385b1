package com.inventory_server.applications.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.inventory_server.applications.cqe.InventoryIdQuery;
import com.inventory_server.applications.cqe.InventoryIdSoldOutQuery;
import com.inventory_server.applications.cqe.InventoryInfoQuery;
import com.inventory_server.applications.cqe.LeadTimeCommand;
import com.inventory_server.applications.dto.*;
import com.inventory_server.infrastructures.entity.LogTrackNumDto;
import com.inventory_server.model.product.entity.vo.OperatorSearchVo;
import com.inventory_server.model.redundancy.entity.dos.InventorySoldOutDaysDO;
import com.inventory_server.model.redundancy.entity.dto.ProductCategoryDTO;
import com.inventory_server.model.redundancy.entity.dto.RedundancySaleDestinationDto;
import com.inventory_server.model.redundancy.entity.dto.SoldOutDateBeforeTheoryDto;
import com.inventory_server.model.redundancy.entity.form.UrgentHeadShipDateForm;
import com.inventory_server.model.redundancy.entity.vo.NormalDeliveryWatchBoardVo;
import jakarta.servlet.http.HttpServletResponse;

import java.io.InputStream;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;
import java.util.Set;

public interface IRedundantInventoryAppService {

    void calRedundantInventoryApp(InputStream file, byte[] fileBytes, String fileName);

    void exportTemplate(HttpServletResponse response);

    IPage<InventoryInfoDto> pageList(InventoryInfoQuery form);

    void export(InventoryInfoQuery form, HttpServletResponse response);

    DetailInventoryRulesDto getDetailInventoryRules(String inventoryInfoId);

    DetailInventoryRulesDto getShippingCalculation(String inventoryInfoId);

    NormalDeliveryWatchBoardVo getNormalDeliveryWatchBoard(String inventoryInfoId);

    DetailInventoryRulesDto getShippingCalculationWithRequirement(String inventoryInfoId);

    NormalDeliveryWatchBoardVo getNormalShippingCalculation(String inventoryInfoId);

    List<InventorySoldOutDaysDO> getBeforeSoldOutList(InventoryIdQuery infoId);

    List<InventoryForeignRedundantInfoDto> getForeignRedundantInfo(InventoryIdQuery infoId);

    List<InventoryForeignRedundantInfoDto> getForeignRedundantInfoByWatchBoardId(String watchBoardId);

    List<InventoryShipRedundantInfoDto> getOnShippingRedundantInfo(InventoryIdQuery infoId);

    List<InventoryShipRedundantInfoDto> getOnShippingRedundantInfoByWatchBoardId(String watchBoardId);

    List<InventoryFactoryRedundantInfoDto> getFactoryRedundantInfo(InventoryIdQuery infoId);

    LeadTimeDto getLeadTime(InventoryIdQuery inventoryInfoId);

    Boolean updateUrgentProduceDays(LeadTimeCommand leadTimeCommand);

    Boolean updateUrgentHeadShipDate(UrgentHeadShipDateForm urgentForm);

    String selectFileInfo(String filePath);

    InventoryFactoryAndForeignInfoDto getFactoryRedundantInfo(InventoryInfoQuery infoId);

    InventoryFactoryAndForeignInfoDto getFactoryRedundantInfoInterior(InventoryInfoQuery infoId);


    Set<String> getSnapIds(InventoryInfoQuery infoId);

    boolean hasRedundancyByVirtualSkuId(List<String> virtualSkuIds);

    List<String> hasRedundancyByVirtualSkuIdList(List<String> virtualSkuIds);
    /**
     * 删除库存信息
     *
     * @param infoIds 需要删除的库存ID
     * @return 结果
     */
    boolean deleteFactoryByInfoIds(List<String> infoIds);

    /**
     * 获取运营名单
     *
     */
    List<OperatorSearchVo> getOperator();

    List<ProductCategoryDTO> category();

    Map<String, LocalDate> calTheoreticalSoldOutDate(InventoryIdSoldOutQuery form);

    Map<String, List<SoldOutDateBeforeTheoryDto>> calAllVirtualSkuRealSoldOutDate(InventoryIdSoldOutQuery form);

    List<RedundancySaleDestinationDto> getSaleDestination(InventoryInfoQuery form);

    void updateSaleDestination(InventoryInfoQuery form, LogTrackNumDto logDto);
}
