package com.purchase.purchase_server.repository.dataRepository.Shipment;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.crafts_mirror.common.security.utils.SecurityUtils;
import com.crafts_mirror.utils.aop.OperationLog;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.purchase.purchase_server.entity.LogTrackNumDto;
import com.purchase.purchase_server.entity.dataObject.ShipmentDetailDO;
import com.purchase.purchase_server.entity.dto.Shipment.ShipmentDetailDto;
import com.purchase.purchase_server.entity.form.ShipmentPlanForm;
import com.purchase.purchase_server.entity.vo.ShipmentPlan.ShipmentDetailVO;
import com.purchase.purchase_server.mapper.ShipmentDetailMapper;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2024/11/18 15:00
 **/
@Service
public class ShipmentDetailRepositoryImpl extends ServiceImpl<ShipmentDetailMapper, ShipmentDetailDO> {


    public List<ShipmentDetailDO> list(ShipmentDetailDto val) {
        return list(Wrappers.<ShipmentDetailDO>lambdaQuery()
                .eq(StrUtil.isNotBlank(val.getShipmentPlanId()), ShipmentDetailDO::getShipmentPlanId, val.getShipmentPlanId())
                .eq(StrUtil.isNotBlank(val.getContractNo()), ShipmentDetailDO::getContractNo, val.getContractNo())
                .eq(StrUtil.isNotBlank(val.getDestinationSku()), ShipmentDetailDO::getDestinationSku, val.getDestinationSku())
                .eq(StrUtil.isNotBlank(val.getFactoryDeliveryDate()), ShipmentDetailDO::getFactoryDeliveryDate, val.getFactoryDeliveryDate())
        );
    }

    @OperationLog(content = "编辑货件明细", operationType = "货件计划管理")
    public void saveOrUpdateDetail(List<ShipmentDetailDO> val, LogTrackNumDto logTrackNumDto) {
        baseMapper.insertOrUpdate(val);
        List<String> planIds = val.stream().map(ShipmentDetailDO::getShipmentPlanId).toList();
        Map<String, String> logMap = new HashMap<>();
        planIds.forEach(id -> logMap.put(id, "编辑货件明细"));
        logTrackNumDto.setLogMap(logMap);
        logTrackNumDto.setAuthorization(SecurityUtils.getToken());
    }

    public List<ShipmentDetailVO> getShipmentDetail(ShipmentPlanForm form) {
        return baseMapper.selectJoinList(ShipmentDetailVO.class, new MPJLambdaWrapper<ShipmentDetailDO>()
                .selectAll(ShipmentDetailDO.class)
                .eq(StrUtil.isNotBlank(form.getPlanId()), ShipmentDetailDO::getShipmentPlanId, form.getPlanId())
                .like(StrUtil.isNotBlank(form.getProductName()), ShipmentDetailDO::getSelfProductName, form.getProductName())
                .in(CollectionUtil.isNotEmpty(form.getSelfSkuList()), ShipmentDetailDO::getSelfSku, form.getSelfSkuList())
                .in(CollectionUtil.isNotEmpty(form.getVirtualSkuList()), ShipmentDetailDO::getDestinationSku, form.getVirtualSkuList())
                .like(StrUtil.isNotBlank(form.getContractNo()), ShipmentDetailDO::getContractNo, form.getContractNo())
                .orderByAsc(ShipmentDetailDO::getDestinationSku)
        );
    }

    public void deleteByPlanId(String planId) {
        baseMapper.deleteByPlanId(planId);
    }
}
