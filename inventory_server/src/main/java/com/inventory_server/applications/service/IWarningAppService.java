package com.inventory_server.applications.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.inventory_server.model.warning.unsalable.entity.form.UnsalablePageForm;
import com.inventory_server.model.warning.urgentPurchase.entity.vo.UrgentPurchasePageVo;
import jakarta.servlet.http.HttpServletResponse;

public interface IWarningAppService {

    void exportUnsalableWarningList(UnsalablePageForm form, HttpServletResponse response);

    void exportUrgentPurchase(UnsalablePageForm form, HttpServletResponse response);

    IPage<UrgentPurchasePageVo> getUrgentPurchasePage(UnsalablePageForm form);
}
