package com.inventory_server.model.inventory.repository.httpRepository.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.crafts_mirror.utils.common.entity.HttpRequestDetail;
import com.crafts_mirror.utils.constant.YiCangConstant;
import com.crafts_mirror.utils.listener.HttpResponseListener;
import com.crafts_mirror.utils.utils.DingTalkUtils;
import com.crafts_mirror.utils.utils.HttpClientPool;
import com.crafts_mirror.utils.utils.YCRequestBody;
import com.google.common.util.concurrent.RateLimiter;
import com.inventory_server.model.inventory.entity.dto.AsyncInventoryResultDto;
import com.inventory_server.model.inventory.entity.dto.YcInventoryInfoDto;
import com.inventory_server.model.inventory.entity.form.InventoryInfoForm;
import com.inventory_server.model.inventory.entity.vo.InventoryInfoBizContent;
import com.inventory_server.model.inventory.repository.databaseRepository.InventoryInfoRepositoryImpl;
import com.inventory_server.model.inventory.repository.httpRepository.FetchYcInventoryInfoRepository;
import com.inventory_server.model.warehouse.entity.dos.YCResponseEntity;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.net.URI;
import java.net.http.HttpConnectTimeoutException;
import java.net.http.HttpResponse;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.CopyOnWriteArrayList;

import static com.crafts_mirror.utils.constant.DateFormatConstant.YYYY_MM_DD_DATE_FORMAT_HYPHEN;
import static com.crafts_mirror.utils.constant.DingTalkConstant.YC_INVENTORY_FETCH_RESULT_INFO_SECRET;
import static com.crafts_mirror.utils.constant.DingTalkConstant.YC_INVENTORY_FETCH_RESULT_INFO_TOKEN;

/**
 * @Description 从易仓处获取库存信息数据交互层
 * <AUTHOR>
 * @Date 2024/7/10 9:54
 **/
@Service
@Slf4j
public class FetchYcInventoryInfoRepositoryImpl implements FetchYcInventoryInfoRepository {

    @Resource
    private InventoryInfoRepositoryImpl inventoryInfoRepository;

    @Override
    public Integer fetchYcInventoryInfoCount(HttpRequestDetail<YCResponseEntity> detail, AsyncInventoryResultDto asyncInventoryResultDto) {
        HttpClientPool<YCResponseEntity> httpClientPool = new HttpClientPool<>(Collections.singletonList(detail));
        YCResponseEntity entity = httpClientPool.sendRequest(detail);
        if (!Objects.equals(entity.getCode(), "200")) {
            String errorMessage = handleCaughtException(entity);
            asyncInventoryResultDto.addFailedCount(1);
            asyncInventoryResultDto.addFailedReason(errorMessage);
            log.error("从易仓处获取库存信息数量异常：{}；异常原始信息：{}", errorMessage, entity);
            throw new IllegalArgumentException("从易仓处获取库存信息数量异常" + errorMessage);
        }
        String bizContent = entity.getBizContent();
        InventoryInfoBizContent infoBizContent = JSON.parseObject(bizContent, InventoryInfoBizContent.class);
        return infoBizContent.getTotal();
    }

    @Override
    public void fetchYcNonFBAInventoryInfoAndSave(String startDate, String endDate, AsyncInventoryResultDto asyncInventoryResultDto) {
        if (StrUtil.isNotBlank(endDate)) {
            endDate = DateUtil.parse(endDate).offset(DateField.DAY_OF_YEAR, 1).toString(YYYY_MM_DD_DATE_FORMAT_HYPHEN);
        }
        InventoryInfoForm countForm = InventoryInfoForm.builder().pageSize(1).build();
        countForm.setUpdateTimeFrom(startDate);
        countForm.setUpdateTimeTo(endDate);
        YCRequestBody<InventoryInfoForm> ycRequestBody = new YCRequestBody<>("V1.0.0", "getProductInventory", countForm)
                .createECRequestBody();

        String body = JSON.toJSONString(ycRequestBody);
        URI uri = URI.create(YiCangConstant.GET_WAREHOUSE_SUMMARY_URL);

        HttpRequestDetail<YCResponseEntity> countDetail = new HttpRequestDetail<>(HttpRequestDetail.HttpMethod.POST, uri, body, YCResponseEntity.class);
        Integer totalCount = fetchYcInventoryInfoCount(countDetail, asyncInventoryResultDto);
        asyncInventoryResultDto.setTotal(totalCount);

        int pageSize = 100;
        int totalPage = (int) Math.ceil((double) totalCount / pageSize);
        HttpResponseListener<YCResponseEntity> responseListener = initHttpResponseListener(asyncInventoryResultDto);

        // 每秒3次请求
        double countPerSec = 0.3;
        RateLimiter rateLimiter = RateLimiter.create(countPerSec);

        // 每1次请求一组
        int countPerTimes = 1;
        for (int i = 1; i <= totalPage; i += countPerTimes) {
            rateLimiter.acquire();
            List<HttpRequestDetail<YCResponseEntity>> detailList = new ArrayList<>(totalPage);

            for (int j = 0; j < 1; j++) {
                InventoryInfoForm form = InventoryInfoForm.builder()
                        .updateTimeTo(endDate).updateTimeFrom(startDate).page(i + j).pageSize(pageSize)
                        .build();
                HttpRequestDetail<YCResponseEntity> detail = createHttpRequestDetail(form, uri);
                detailList.add(detail);
            }

            HttpClientPool<YCResponseEntity> httpClientPool = new HttpClientPool<>(detailList, responseListener, rateLimiter);
            httpClientPool.startAsyncRequests();
        }
    }

    private HttpRequestDetail<YCResponseEntity> createHttpRequestDetail(InventoryInfoForm form, URI uri) {
        YCRequestBody<InventoryInfoForm> ycRequestBody = new YCRequestBody<>("V1.0.0", "getProductInventory", form)
                .createECRequestBody();

        String body = JSON.toJSONString(ycRequestBody);
        return new HttpRequestDetail<>(HttpRequestDetail.HttpMethod.POST, uri, body, YCResponseEntity.class);
    }

    private HttpRequestDetail<YCResponseEntity> createHttpRequestDetail(String formStr, URI uri) {
        YCRequestBody<?> ycRequestBody = new YCRequestBody<>("V1.0.0", "getProductInventory", formStr)
                .createECRequestBody();

        String body = JSON.toJSONString(ycRequestBody);
        return new HttpRequestDetail<>(HttpRequestDetail.HttpMethod.POST, uri, body, YCResponseEntity.class);
    }

    private HttpResponseListener<YCResponseEntity> initHttpResponseListener(AsyncInventoryResultDto asyncInventoryResultDto) {
        return new HttpResponseListener<>() {
            final List<String> markdownList = new CopyOnWriteArrayList<>();
            @Override
            public void onResponse(HttpResponse<YCResponseEntity> response) {
                int statusCode = response.statusCode();
                if (statusCode != 200) {
                    String errorMsg = handleCaughtException(response.body());
                    log.error("从易仓处获取库存信息数量异常：{}；异常原始信息：{}", errorMsg, response);
                    throw new IllegalArgumentException("从易仓处获取库存信息数量异常" + errorMsg);
                }

                handleResponse(response.body(), asyncInventoryResultDto);
            }

            @Override
            public void onError(Throwable throwable, HttpRequestDetail<YCResponseEntity> requestDetail) {
                URI uri = URI.create(YiCangConstant.GET_WAREHOUSE_SUMMARY_URL);
                YCRequestBody ycRequestBody = JSON.parseObject(requestDetail.body(), YCRequestBody.class);
                String bizContent = ycRequestBody.getBizContent();
                HttpRequestDetail<YCResponseEntity> httpRequestDetail = createHttpRequestDetail(bizContent, uri);
                switch (throwable.getCause()) {
                    case HttpConnectTimeoutException ignored -> {
                        resendErrorRequest(throwable, bizContent, httpRequestDetail);
                    }
                    case RuntimeException ignored -> {
                        String message = throwable.getCause().getMessage();
                        if ("从易仓处获取库存信息异常同一客户每秒请求接口次数不能超过10次".equals(message)) {
                            try {
                                Thread.sleep(1000);
                            } catch (InterruptedException e) {
                                log.error("休眠被打断{}", String.valueOf(e));
                                throw new IllegalArgumentException("休眠被打断" + e);
                            }

                            resendErrorRequest(throwable, bizContent, httpRequestDetail);
                        } else {
                            markdownList.add("- **异常原因**: " + throwable.getMessage() + "\n");
                            markdownList.add("  - **请求参数**: " + bizContent + "\n\n");
                            log.error("从易仓处获取库存信息异常", throwable);
                            asyncInventoryResultDto.addFailedReason(throwable.getMessage());
                        }
                    }
                    default -> {
                        markdownList.add("- **异常原因**: " + throwable.getMessage() + "\n");
                        markdownList.add("  - **请求参数**: " + bizContent + "\n\n");
                        log.error("从易仓处获取库存信息异常", throwable);
                        asyncInventoryResultDto.addFailedReason(throwable.getMessage());
                    }
                }
            }

            private void resendErrorRequest(Throwable throwable, String bizContent, HttpRequestDetail<YCResponseEntity> httpRequestDetail) {
                HttpClientPool<YCResponseEntity> httpClientPool = new HttpClientPool<>(Collections.singletonList(httpRequestDetail));
                try {
                    YCResponseEntity response = httpClientPool.sendRequest(httpRequestDetail);
                    handleResponse(response, asyncInventoryResultDto);
                } catch (Exception e) {
                    markdownList.add("- **异常原因**: " + throwable.getMessage() + "\n");
                    markdownList.add("  - **请求参数**: " + bizContent + "\n\n");
                    log.error("从易仓处获取库存信息异常", throwable);
                    asyncInventoryResultDto.addFailedReason(throwable.getMessage());
                }
            }

            @Override
            public void afterAll() {
                if (CollectionUtil.isNotEmpty(markdownList)) {
                    StringBuilder markdown = new StringBuilder();
                    markdown.append("# 获取库存信息异常\n\n");
                    markdown.append("## 异常详情\n\n");
                    markdown.append("### 异常原因及请求参数\n\n");
                    markdownList.forEach(markdown::append);
                    DingTalkUtils.sendDingTalkMarkdownMessage(YC_INVENTORY_FETCH_RESULT_INFO_TOKEN, YC_INVENTORY_FETCH_RESULT_INFO_SECRET, markdown.toString());
                    markdownList.clear();
                }
            }
        };
    }

    private String handleCaughtException(YCResponseEntity entity) {
        JSONArray jsonArray = JSON.parseArray(entity.getBizContent());
        if (jsonArray != null) {
            JSONObject jsonObject = jsonArray.getJSONObject(0);
            return jsonObject.getString("errorMsg");
        } else {
            return "未知异常：" + entity.getMessage();
        }
    }

    private void handleResponse(YCResponseEntity body, AsyncInventoryResultDto asyncInventoryResultDto) {
        if (!Objects.equals(body.getCode(), "200")) {
            String errorMsg = handleCaughtException(body);
            asyncInventoryResultDto.addFailedReason(errorMsg);
            log.error("从易仓处获取库存信息数量异常：{}；异常原始信息：{}", errorMsg, body);
            throw new IllegalArgumentException("从易仓处获取库存信息数量异常" + errorMsg);
        }

        String bizContent = body.getBizContent();
        InventoryInfoBizContent infoBizContent = JSON.parseObject(bizContent, InventoryInfoBizContent.class);
        List<YcInventoryInfoDto> warehouseDetailList = infoBizContent.getData();

        inventoryInfoRepository.saveInventoryInfo(warehouseDetailList);
        asyncInventoryResultDto.addSucceedCount(warehouseDetailList.size());
    }
}
