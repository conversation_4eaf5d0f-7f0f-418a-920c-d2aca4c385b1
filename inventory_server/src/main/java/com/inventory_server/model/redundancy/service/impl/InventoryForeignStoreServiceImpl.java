package com.inventory_server.model.redundancy.service.impl;

import com.inventory_server.applications.dto.FactoryRemainInventoryDto;
import com.inventory_server.infrastructures.assembler.RedundantInventoryAssembler;
import com.inventory_server.model.redundancy.entity.dos.InventoryFactoryPlanInfoDO;
import com.inventory_server.model.redundancy.entity.dos.InventoryForeignStoreDO;
import com.inventory_server.model.redundancy.repository.dataRepository.InventoryForeignStoreRepository;
import com.inventory_server.model.redundancy.service.IInventoryForeignStoreService;
import com.inventory_server.model.warehouse.entity.dto.SenboWarehouseDto;
import com.inventory_server.model.warehouse.service.IWarehouseService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/5/10
 **/
@Service
@Slf4j
public class InventoryForeignStoreServiceImpl implements IInventoryForeignStoreService {

    @Resource
    private InventoryForeignStoreRepository inventoryForeignStoreRepository;

    @Resource
    private RedundantInventoryAssembler redundantInventoryAssembler;

    @Resource
    private IWarehouseService warehouseService;

    @Override
    public List<InventoryForeignStoreDO> getForeignStoreListByInfoIds(List<String> infoIds) {
        List<InventoryForeignStoreDO> foreignStoreList = inventoryForeignStoreRepository.getForeignStoreListByInfoIds(infoIds);
        List<SenboWarehouseDto> senboWarehouseList = warehouseService.getSenboWarehouseListWithoutMiddleTransit();
        Map<Integer, String> idNameMap = senboWarehouseList.stream().collect(Collectors.toMap(SenboWarehouseDto::getSenboWarehouseId, SenboWarehouseDto::getSenboWarehouse));

        for (var foreign : foreignStoreList) {
            foreign.setWarehouse(idNameMap.get(Integer.valueOf(foreign.getWarehouse())));
        }
        return foreignStoreList;
    }

    public List<InventoryForeignStoreDO> getForeignStoreListByInfoId(String infoId) {
        List<String> list = Collections.singletonList(infoId);
        return inventoryForeignStoreRepository.getForeignStoreListByInfoIds(list);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public List<InventoryForeignStoreDO> saveForeignStoreInfo(String inventoryId, List<FactoryRemainInventoryDto> list) {
        deleteForeignStoreInfoByInfoId(inventoryId);
        List<InventoryForeignStoreDO> inventoryForeignStoreDOList = redundantInventoryAssembler.remainDtoListToStoreDoList(list);
        inventoryForeignStoreDOList.forEach(i -> i.setInventoryInfoId(inventoryId));
        inventoryForeignStoreRepository.saveBatch(inventoryForeignStoreDOList);
        return inventoryForeignStoreDOList;
    }

    @Override
    public List<InventoryForeignStoreDO> getPlanInfoByIdList(List<String> idList) {
        return inventoryForeignStoreRepository.listByIds(idList);
    }

    @Override
    public void deleteForeignStoreInfoByInfoId(String inventoryId) {
        inventoryForeignStoreRepository.deleteForeignStoreInfoByInfoId(inventoryId);
    }
}
