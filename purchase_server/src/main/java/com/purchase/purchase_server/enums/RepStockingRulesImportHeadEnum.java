package com.purchase.purchase_server.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@Getter
@AllArgsConstructor
public enum RepStockingRulesImportHeadEnum {
    VIRTUAL_SKU("*虚拟SKU","virtualSku"),
    PURCHASE_DAYS("*采购下单天数","purchaseDays"),
    TRANSIT_DAYS("*国内中转天数","transitDays"),
    SAFE_DAYS("*安全天数","safeDays"),
    REP_FREQUENCY("*发货周期","repFrequency"),
    PURCHASE_CYCLE("*采购周期","purchaseCycle"),
    ;

    private final String code;
    private final String desc;
    public static RepStockingRulesImportHeadEnum ofCode(String code) {
        return Arrays.stream(RepStockingRulesImportHeadEnum.values())
                .filter(it -> it.getCode().equals(code))
                .findFirst()
                .orElse(null);
    }

    //枚举是否存在
    public static boolean isExist(String code) {
        return Arrays.stream(RepStockingRulesImportHeadEnum.values())
                .anyMatch(it -> it.getCode().equals(code));
    }

    //返回枚举的desc的list集合
    public static List<String> getDescList() {
        return Arrays.stream(RepStockingRulesImportHeadEnum.values())
                .map(RepStockingRulesImportHeadEnum::getDesc).collect(Collectors.toList());
    }
    //返回枚举的code的list集合
    public static List<String> getCodeList() {
        return Arrays.stream(RepStockingRulesImportHeadEnum.values())
                .map(RepStockingRulesImportHeadEnum::getCode).collect(Collectors.toList());
    }
}
