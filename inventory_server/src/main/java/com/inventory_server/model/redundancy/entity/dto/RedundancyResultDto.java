package com.inventory_server.model.redundancy.entity.dto;

import com.inventory_server.model.redundancy.entity.dp.RedundancyDp;
import lombok.Data;

import java.util.*;

/**
 * @Description 冗余计算结果实体类
 * <AUTHOR>
 * @Date 2024/5/17 9:20
 **/
@Data
public class RedundancyResultDto {

    private List<RedundancyDp> foreignRedundancyDpList;
    private List<RedundancyDp> factoryRedundancyDpList;
    private List<RedundancyDp> onShippingRedundancyDpList;

    public RedundancyResultDto() {
        this.foreignRedundancyDpList = new ArrayList<>();
        this.factoryRedundancyDpList = new ArrayList<>();
        this.onShippingRedundancyDpList  = new ArrayList<>();
    }

    public void addForeignRedundancyDp(RedundancyDp dp) {
        if(dp == null) {
            return;
        }
        foreignRedundancyDpList.add(dp);
    }

    public void addFactoryRedundancyDp(RedundancyDp dp) {
        if(dp == null) {
            return;
        }
        factoryRedundancyDpList.add(dp);
    }

    public void addOnShippingRedundancyDp(RedundancyDp dp) {
        if(dp == null) {
            return;
        }
        onShippingRedundancyDpList.add(dp);
    }
}
