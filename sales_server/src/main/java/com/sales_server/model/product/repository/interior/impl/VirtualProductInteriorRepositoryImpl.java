package com.sales_server.model.product.repository.interior.impl;

import com.alibaba.fastjson2.JSON;
import com.crafts_mirror.common.security.utils.SecurityUtils;
import com.crafts_mirror.utils.utils.RestTemplateUtils;
import com.crafts_mirror.utils.web.domain.ResultDTO;
import com.sales_server.model.product.entity.dp.VirtualProductInfoDp;
import com.sales_server.model.product.entity.dto.VirtualProductListDto;
import com.sales_server.model.product.entity.form.VirtualIdListForm;
import com.sales_server.model.product.entity.form.VirtualProductSearchForm;
import com.sales_server.model.product.entity.vo.VirtualProductInfoVo;
import com.sales_server.model.product.entity.vo.VirtualSkuVo;
import com.sales_server.model.product.repository.interior.IVirtualProductInteriorRepository;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.ArrayList;
import java.util.List;

import static com.crafts_mirror.utils.constant.SystemConstant.VIRTUAL_PRODUCTS_SKU_INFO_LIST_URL;
import static com.crafts_mirror.utils.constant.SystemConstant.VIRTUAL_PRODUCTS_SKU_URL_LIST;

/**
 * <AUTHOR>
 * @Date 2024/11/11 16:16
 **/
@Service
public class VirtualProductInteriorRepositoryImpl implements IVirtualProductInteriorRepository {

    @Resource
    private RestTemplate restTemplate;

    @Override
    public List<VirtualProductListDto> getAllVirtualProductList() {
        VirtualProductSearchForm form = new VirtualProductSearchForm();
        form.setNeedDatePermission(false);
        ResultDTO resultDTO = sendRequest(form, VIRTUAL_PRODUCTS_SKU_URL_LIST);
        VirtualSkuVo virtualSkuVo = JSON.to(VirtualSkuVo.class, resultDTO.getData());
        return virtualSkuVo.getVirtualSkuList();
    }

    @Override
    public List<VirtualProductListDto> getVirtualProductList(VirtualProductSearchForm form) {
        ResultDTO resultDTO = sendRequest(form, VIRTUAL_PRODUCTS_SKU_URL_LIST);
        VirtualSkuVo virtualSkuVo = JSON.to(VirtualSkuVo.class, resultDTO.getData());
        return virtualSkuVo.getVirtualSkuList();
    }

    @Override
    public List<VirtualProductInfoDp> getVirtualProductInfoListByVirtualIdList(VirtualIdListForm form) {
        ResultDTO resultDTO = sendRequest(form, VIRTUAL_PRODUCTS_SKU_INFO_LIST_URL);
        VirtualProductInfoVo virtualProductInfoDp = JSON.to(VirtualProductInfoVo.class, resultDTO.getData());
        if (virtualProductInfoDp == null) return new ArrayList<>();
        return virtualProductInfoDp.getVirtualProductInfoList();
    }

    private <T> ResultDTO sendRequest(T t, String url) {
        RestTemplateUtils restTemplateUtils = new RestTemplateUtils(restTemplate, SecurityUtils.getToken());
        return restTemplateUtils.post(t, ResultDTO.class, url);
    }
}
