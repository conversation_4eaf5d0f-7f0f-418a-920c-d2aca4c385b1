//package com.product.product_server.properties;
//
//import com.crafts_mirror.utils.utils.RsaUtils;
//import jakarta.annotation.PostConstruct;
//import lombok.Data;
//import org.springframework.boot.context.properties.ConfigurationProperties;
//
//import java.security.PrivateKey;
//import java.security.PublicKey;
//
///**
// *  该类用于配置RSA公钥和私钥的路径
// * @author: 短途游
// */
//@Data
//@ConfigurationProperties(prefix = "rsa.key", ignoreInvalidFields = true)
//public class RsaKeyProperties {
//    private String publicKeyPath;
//    private String privateKeyPath;
//
//    private PublicKey publicKey;
//    private PrivateKey privateKey;
//
//    /**
//     * 该方法用于初始化公钥和私钥的内容
//     */
//    @PostConstruct
//    public void loadRsaKey() throws Exception {
//        if (publicKeyPath != null) {
//            publicKey = RsaUtils.getPublicKey(publicKeyPath);
//        }
//        if (privateKeyPath != null) {
//            privateKey = RsaUtils.getPrivateKey(privateKeyPath);
//        }
//    }
//}
