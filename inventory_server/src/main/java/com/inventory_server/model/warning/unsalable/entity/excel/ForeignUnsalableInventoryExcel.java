package com.inventory_server.model.warning.unsalable.entity.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.alibaba.excel.enums.BooleanEnum;
import com.alibaba.excel.enums.poi.HorizontalAlignmentEnum;
import com.alibaba.excel.enums.poi.VerticalAlignmentEnum;
import com.inventory_server.model.warning.unsalable.entity.dto.ForeignUnsalableDto;
import com.inventory_server.model.warning.unsalable.entity.vo.UnsalableWaringVo;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.math.BigDecimal;

import static java.math.RoundingMode.HALF_UP;

/**
 * @Description 海外仓无计划库存
 * <AUTHOR>
 * @Date 2025/4/17 17:00
 **/
@EqualsAndHashCode(callSuper = true)
@Data
@SuperBuilder
@NoArgsConstructor
@ContentStyle(wrapped = BooleanEnum.TRUE,
        horizontalAlignment = HorizontalAlignmentEnum.CENTER,
        verticalAlignment = VerticalAlignmentEnum.CENTER)
public class ForeignUnsalableInventoryExcel extends AbstractUnsalableInventoryExcel {

    @ExcelProperty(value = "仓库", order = 10)
    private String warehouse;

    public ForeignUnsalableInventoryExcel(UnsalableWaringVo vo, ForeignUnsalableDto foreignUnsalableDto) {
        super(vo);
        this.setVirtualSku(foreignUnsalableDto.getVirtualSku());
        this.warehouse = foreignUnsalableDto.getWarehouse();
        this.setTotalUnsalableInventory(foreignUnsalableDto.getUnsalableNum());
        this.setUnsalablePrices(BigDecimal.valueOf(foreignUnsalableDto.getUnsalableNum() * vo.getPriceWithTaxes()).setScale(4, HALF_UP).doubleValue());
    }
}
