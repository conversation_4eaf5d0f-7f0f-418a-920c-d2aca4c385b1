package com.purchase.purchase_server.entity.vo;

import com.purchase.purchase_server.entity.dto.replenishment.ReplenishmentTrialWatchBoardDto;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * @Description 补货建议试算看板返回给前端的数据
 * <AUTHOR>
 * @Date 2024/1/22 15:13
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ReplenishmentDetailWatchBoardReplenishmentVo implements Serializable {
    private List<ReplenishmentTrialWatchBoardDto> watchBoardDtoList;

    private TrialMockInventoryVo trialMockInventoryVo;

    private List<String> watchBoardDateList;

    private Integer operatorSum;

    private String operationReason;

    private String operatorRemark;;

    private String replenishmentStartDate;

    private String replenishmentEndDate;
}
