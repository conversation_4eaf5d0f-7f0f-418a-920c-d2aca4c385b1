/*
 Navicat Premium Data Transfer

 Source Server         : 森帛本地环境
 Source Server Type    : MySQL
 Source Server Version : 80029 (8.0.29)
 Source Host           : localhost:3306
 Source Schema         : crafts_mirror

 Target Server Type    : MySQL
 Target Server Version : 80029 (8.0.29)
 File Encoding         : 65001

 Date: 03/02/2024 17:28:00
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for cm_sys_menu
-- ----------------------------
DROP TABLE IF EXISTS `cm_sys_menu`;
CREATE TABLE `cm_sys_menu`  (
  `id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `parent_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '父id',
  `route_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '路由名称',
  `route_path` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '访问路由',
  `sort` int NOT NULL,
  `visible` tinyint NOT NULL COMMENT '是否可见',
  `create_date` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_date` datetime NULL DEFAULT NULL,
  `create_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `update_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of cm_sys_menu
-- ----------------------------

-- ----------------------------
-- Table structure for cm_sys_permission
-- ----------------------------
DROP TABLE IF EXISTS `cm_sys_permission`;
CREATE TABLE `cm_sys_permission`  (
  `id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '名称',
  `url_perm` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '接口路径',
  `btn_perm` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '按钮权限控制',
  `method` int UNSIGNED NOT NULL COMMENT '请求方式（0-get；1-post）',
  `service_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '服务名',
  `menu_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '0' COMMENT '菜单id',
  `create_date` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_date` datetime NULL DEFAULT NULL,
  `create_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `update_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '系统权限表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of cm_sys_permission
-- ----------------------------
INSERT INTO `cm_sys_permission` VALUES ('1', '用户管理', '/user/manager', '', 0, 'manager', '0', NULL, NULL, NULL, NULL, '0');
INSERT INTO `cm_sys_permission` VALUES ('2', '用户列表查询', '/user/list', '', 1, 'list', '1', NULL, NULL, NULL, NULL, '0');
INSERT INTO `cm_sys_permission` VALUES ('3', '删除用户', '/user/delete', '', 0, 'delete', '1', NULL, NULL, NULL, NULL, '0');
INSERT INTO `cm_sys_permission` VALUES ('4', '更新用户', '/user/update', '', 1, 'update', '1', NULL, NULL, NULL, NULL, '0');
INSERT INTO `cm_sys_permission` VALUES ('5', '新增用户', '/user/save', '', 1, 'save', '1', NULL, NULL, NULL, NULL, '0');
INSERT INTO `cm_sys_permission` VALUES ('6', '用户详情', '/user/getById', '', 0, 'getById', '1', NULL, NULL, NULL, NULL, '0');
INSERT INTO `cm_sys_permission` VALUES ('7', '测试', 'ROLE_ADMIN', NULL, 1, 'test', '0', NULL, NULL, NULL, NULL, '0');
INSERT INTO `cm_sys_permission` VALUES ('8', '获取产品经理列表', 'URL_/selfProduct/buyer/set', NULL, 1, 'product', '0', NULL, NULL, NULL, NULL, '0');

-- ----------------------------
-- Table structure for cm_sys_role
-- ----------------------------
DROP TABLE IF EXISTS `cm_sys_role`;
CREATE TABLE `cm_sys_role`  (
  `id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '名称',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  `sort` int NOT NULL,
  `create_date` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_date` datetime NULL DEFAULT NULL,
  `create_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `update_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '系统角色表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of cm_sys_role
-- ----------------------------
INSERT INTO `cm_sys_role` VALUES ('1', 'SUPER_ADMIN', '超管，拥有最高权限', 0, NULL, NULL, NULL, NULL, '0');
INSERT INTO `cm_sys_role` VALUES ('2', 'ADMIN', '管理员，拥有操作权限', 0, NULL, NULL, NULL, NULL, '0');

-- ----------------------------
-- Table structure for cm_sys_role_menu
-- ----------------------------
DROP TABLE IF EXISTS `cm_sys_role_menu`;
CREATE TABLE `cm_sys_role_menu`  (
  `id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `role_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `menu_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of cm_sys_role_menu
-- ----------------------------

-- ----------------------------
-- Table structure for cm_sys_role_permission
-- ----------------------------
DROP TABLE IF EXISTS `cm_sys_role_permission`;
CREATE TABLE `cm_sys_role_permission`  (
  `id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `role_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '角色id',
  `permission_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '权限id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '角色-权限关联表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of cm_sys_role_permission
-- ----------------------------
INSERT INTO `cm_sys_role_permission` VALUES ('1', '1', '1');
INSERT INTO `cm_sys_role_permission` VALUES ('10', '2', '4');
INSERT INTO `cm_sys_role_permission` VALUES ('11', '2', '5');
INSERT INTO `cm_sys_role_permission` VALUES ('12', '2', '6');
INSERT INTO `cm_sys_role_permission` VALUES ('13', '2', '7');
INSERT INTO `cm_sys_role_permission` VALUES ('14', '2', '8');
INSERT INTO `cm_sys_role_permission` VALUES ('2', '1', '2');
INSERT INTO `cm_sys_role_permission` VALUES ('3', '1', '3');
INSERT INTO `cm_sys_role_permission` VALUES ('4', '1', '4');
INSERT INTO `cm_sys_role_permission` VALUES ('5', '1', '5');
INSERT INTO `cm_sys_role_permission` VALUES ('6', '1', '6');
INSERT INTO `cm_sys_role_permission` VALUES ('7', '2', '1');
INSERT INTO `cm_sys_role_permission` VALUES ('8', '2', '2');
INSERT INTO `cm_sys_role_permission` VALUES ('9', '2', '3');

-- ----------------------------
-- Table structure for cm_sys_user
-- ----------------------------
DROP TABLE IF EXISTS `cm_sys_user`;
CREATE TABLE `cm_sys_user`  (
  `id` int UNSIGNED NOT NULL,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '姓名',
  `username` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '用户名',
  `password` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '密码',
  `enabled` tinyint UNSIGNED NOT NULL COMMENT '是否启用（0-未启用；1-启用中）',
  `last_login_time` datetime NULL DEFAULT NULL COMMENT '上一次登录时间',
  `salt` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '盐',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '用户表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of cm_sys_user
-- ----------------------------
INSERT INTO `cm_sys_user` VALUES (1, '超级管理员', 'admin', '$2a$10$61a.RyzX6lVzBEvzj.dOzeEWUQDwgXoTjjmd9dstgfh6TknYkyq0i', 1, '2023-10-01 00:00:01', '');
INSERT INTO `cm_sys_user` VALUES (2, '系统管理员', 'normal', '$2a$10$61a.RyzX6lVzBEvzj.dOzeEWUQDwgXoTjjmd9dstgfh6TknYkyq0i', 1, '2023-10-01 00:00:01', '');

-- ----------------------------
-- Table structure for cm_sys_user_role
-- ----------------------------
DROP TABLE IF EXISTS `cm_sys_user_role`;
CREATE TABLE `cm_sys_user_role`  (
  `id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `role_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '角色id，数据来源于role表的主键',
  `user_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '用户id，数据来源于user表的主键',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '用户-角色关系表表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of cm_sys_user_role
-- ----------------------------
INSERT INTO `cm_sys_user_role` VALUES ('1', '1', '1');
INSERT INTO `cm_sys_user_role` VALUES ('2', '2', '2');

SET FOREIGN_KEY_CHECKS = 1;

ALTER TABLE `crafts_mirror`.`cm_replenishment_trial_purchase_inventory`
    ADD INDEX `virtualPurchaseId`(`replenishment_virtual_purchase_id` ASC) USING BTREE;