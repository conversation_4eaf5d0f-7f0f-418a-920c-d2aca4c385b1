package com.purchase.purchase_server.entity.vo.ShipmentPlan;

import com.baomidou.mybatisplus.annotation.TableName;
import com.purchase.purchase_server.entity.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;
import java.util.Date;

/**
 * 货件明细
 *
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class ShipmentDetailVO implements Serializable {

    private String id;
    /**
     * 货件计划表ID
     */
    private String shipmentPlanId;

    /**
     * 合同号
     */
    private String contractNo;
    /**
     * senbo虚拟skuId
     */
    private String virtualSkuId;

    /**
     * senbo虚拟sku
     */
    private String destinationSku;

    /**
     * sku类型(0-虚拟sku 1-老sku)
     */
    private String isOldStatus;

    /**
     * 运营
     */
    private String operator;

    /**
     * 渠道
     */
    private String channel;
    /**
     * 自定义skuId
     */
    private String selfProductId;
    /**
     * 自定义sku
     */
    private String selfSku;

    /**
     * 产品名称
     */
    private String selfProductName;

    /**
     * 图片链接
     */
    private String image;

    /**
     * 供应商id
     */
    private String factoryId;

    /**
     * 供应商代码
     */
    private String factoryCode;

    /**
     * 工厂交期
     */
    private String factoryDeliveryDate;

    /**
     * 发货数量
     */
    private Integer shippingQuantity;


}