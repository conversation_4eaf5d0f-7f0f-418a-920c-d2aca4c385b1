package com.inventory_server.interfaces.mqConsumer;

import com.inventory_server.model.warning.urgentPurchase.entity.form.UrgentPurchaseMQForm;
import com.inventory_server.model.warning.urgentPurchase.service.IUrgentPurchaseService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.ConsumeMode;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.stereotype.Service;

import static com.crafts_mirror.utils.constant.MQConstants.*;

/**
 * @Description 加急补货消费者
 * <AUTHOR>
 * @Date 2025/4/21 13:30
 **/
@Service
@RocketMQMessageListener(
        topic = URGENT_PURCHASE_TOPIC,
        consumerGroup = URGENT_PURCHASE_CONSUMER,
        consumeMode = ConsumeMode.CONCURRENTLY // 或者 ORDERLY
)
@Slf4j
public class UrgentPurchaseConsumer implements RocketMQListener<UrgentPurchaseMQForm> {

    @Resource
    private IUrgentPurchaseService urgentPurchaseService;

    @Override
    public void onMessage(UrgentPurchaseMQForm message) {
        log.info("{} 获取到紧急补货消息，入参：{}", URGENT_PURCHASE_CONSUMER, message);
        urgentPurchaseService.calUrgentPurchaseNum(message);
    }
}
