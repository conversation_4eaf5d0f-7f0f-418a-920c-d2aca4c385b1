package com.purchase.purchase_server.entity.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;


/**
 * @Description 备货规则
 * <AUTHOR>
 * @Date 2024/1/20 14:00
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProductRulesDto {
    private String rulesId;
    //虚拟sku
    private String virtualSku;
    //采购计划天数
    private Integer purchaseProjectDays;
    //生产周期
    private String produceDays;
    //中转天数
    private Integer transitDays;
    //安全天数
    private Integer safeDays;
    //发货频次
    private Integer shippingFrequency;
    /**
     * 补货周期
     */
    private Integer purchaseCircle;
    /**
     * 发货周期
     */
    private Integer shippingCircle;
    //头程时间
    private String headShippingDays;
    //发货比例
    private String shippingRatio;
}
