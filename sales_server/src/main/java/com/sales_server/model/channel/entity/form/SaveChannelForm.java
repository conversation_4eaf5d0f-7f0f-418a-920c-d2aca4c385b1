package com.sales_server.model.channel.entity.form;

import com.crafts_mirror.utils.aop.validator.PositiveInteger;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @Description 渠道保存表单
 * <AUTHOR>
 * @Date 2025/4/30 15:03
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SaveChannelForm implements Serializable {

    private String channelId;

    private String parentChannelId;

    private String channelName;

    @PositiveInteger(message = "排序值必须是正整数")
    private Integer sort;

    private String remarks;
}
