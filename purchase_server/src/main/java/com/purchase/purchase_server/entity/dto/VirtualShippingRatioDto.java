package com.purchase.purchase_server.entity.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @Date 2024/7/4 17:01
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class VirtualShippingRatioDto {

    private String virtualSkuId;

    private Integer warehouseId;

    private Integer headShippingDate;

    private Double shippingRatio;
}
