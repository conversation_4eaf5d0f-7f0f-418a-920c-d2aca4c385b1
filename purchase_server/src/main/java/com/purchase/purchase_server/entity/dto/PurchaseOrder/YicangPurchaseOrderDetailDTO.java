package com.purchase.purchase_server.entity.dto.PurchaseOrder;

import com.alibaba.fastjson2.annotation.JSONField;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * 易仓采购单明细
 */

@Data
@AllArgsConstructor
@NoArgsConstructor
public class YicangPurchaseOrderDetailDTO implements Serializable {
    @Serial
    private static final long serialVersionUID = -8685400150472052126L;
    /**
     * 产品Id
     */
    @JSONField(name = "product_id")
    private Integer productId;

    /**
     * 预期数量
     */
    @JSONField(name = "qty_expected")
    private Integer qtyExpected;

    /**
     * (良品上架数量)付款数量
     */
    @JSONField(name = "qty_pay")
    private Integer qtyPay;

    /**
     * 实收数量
     */
    @JSONField(name = "qty_receving")
    private Integer qtyReceving;

    /**
     * 赠送数量
     */
    @JSONField(name = "qty_free")
    private Integer qtyFree;

    /**
     * 单价（不含税）
     */
    @JSONField(name = "unit_price")
    private Float unitPrice;

    /**
     * 单价（含税）
     */
    @JSONField(name = "contract_price")
    private Float contractPrice;

    /**
     * 总价
     */
    @JSONField(name = "total_price")
    private Float totalPrice;

    /**
     * 币种
     */
    @JSONField(name = "currency_code")
    private String currencyCode;

    /**
     * 产品代码（自定义）
     */
    @JSONField(name = "product_sku")
    private String productSku;

    /**
     * 产品名称
     */
    @JSONField(name = "product_title")
    private String productTitle;

    /**
     * 供应商品号
     */
    @JSONField(name = "sp_supplier_sku")
    private String spSupplierSku;

    /**
     * 是否赠品
     * 0否，
     * 1是
     */
    @JSONField(name = "is_free")
    private String isFree;

    /**
     * 产品采购备注
     */
    @JSONField(name = "note")
    private String note;

    /**
     * 外部单号
     */
    @JSONField(name = "pop_external_number")
    private String popExternalNumber;

    /**
     * 头程占用数量
     */
    @JSONField(name = "transfer_qty")
    private Integer transferQty;

    /**
     * 税率
     */
    @JSONField(name = "po_tax_rate")
    private Integer poTaxRate;

    /**
     * 首次到货时间
     */
    @JSONField(name = "first_receive_time")
    private Date firstReceiveTime;

    /**
     * 平台产品代码：如1688产品ID beta
     */
    @JSONField(name = "pop_platform_product")
    private String popPlatformProduct;

    /**
     * 平台产品款式：如1688产品的specId
     */
    @JSONField(name = "pop_platform_sku")
    private String popPlatformSku;

    /**
     * 产品采购状态
     */
    @JSONField(name = "po_status")
    private String poStatus;

    /**
     * sb采购单id
     */
    @JSONField(name = "sb_po_id")
    private String sbPoId;

    /**
     * 自定义产品id（senbo）
     */
    private String sbSelfSkuId;
}