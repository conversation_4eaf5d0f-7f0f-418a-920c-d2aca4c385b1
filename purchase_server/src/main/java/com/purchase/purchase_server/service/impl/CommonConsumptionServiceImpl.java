package com.purchase.purchase_server.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.TypeReference;
import com.purchase.purchase_server.entity.dto.FactoryRemainInventoryDto;
import com.purchase.purchase_server.entity.dto.delivery.MockTableCalDto;
import com.purchase.purchase_server.entity.dto.delivery.ShippingProjectBaseParamDto;
import com.purchase.purchase_server.entity.dto.delivery.TrialCalReplenishmentDto;
import com.purchase.purchase_server.model.purchase.PurchaseDayNeedNumDp;
import com.purchase.purchase_server.service.ICalRemainInventoryService;
import com.purchase.purchase_server.service.ICommonConsumptionService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import java.util.stream.Collectors;

import static cn.hutool.core.date.DateField.DAY_OF_YEAR;
import static com.crafts_mirror.utils.constant.DateFormatConstant.YYYY_MM_DD_DATE_FORMAT_SLASH;
import static com.crafts_mirror.utils.constant.DateFormatConstant.YYYY_M_DATE_FORMAT_CHINESE;
import static java.math.RoundingMode.HALF_UP;

/**
 * @Description 公共的模拟消耗service层
 * <AUTHOR>
 * @Date 2024/12/25 13:47
 **/
@Service
@Slf4j
public class CommonConsumptionServiceImpl implements ICommonConsumptionService {

    @Resource
    private ICalRemainInventoryService calRemainInventoryService;

    private static final SimpleDateFormat format = new SimpleDateFormat(YYYY_MM_DD_DATE_FORMAT_SLASH);

    private final DateTimeFormatter formatter = DateTimeFormatter.ofPattern(YYYY_MM_DD_DATE_FORMAT_SLASH);

    private final DateTimeFormatter chineseFormatter = DateTimeFormatter.ofPattern(YYYY_M_DATE_FORMAT_CHINESE);


    @Override
    public DateTime calDeliveryNumStartDate(
            DateTime startDate, List<FactoryRemainInventoryDto> onShippingInventoryList, String warehouse,
            Map<String, DateTime> fastestArrivingDateMap, Map<String, Double> targetSalesMap, DateTime maxSaleDate,
            Map<String, Double> remainMap, DateTime replenishmentCreateDate, ShippingProjectBaseParamDto shippingRules,
            List<String> sortedWarehouseList, List<FactoryRemainInventoryDto> priorDeliveryList) {
        // 此处是在途数据
        List<FactoryRemainInventoryDto> newShippingInventoryList = new ArrayList<>(onShippingInventoryList);
        if (maxSaleDate.isBeforeOrEquals(startDate)) {
            return startDate;
        }

        DateTime finalStartDate = startDate;
        List<FactoryRemainInventoryDto> tempRemainList = newShippingInventoryList.stream()
                .filter(f -> finalStartDate.isBefore(f.getEnableUsingDate()))
                .collect(Collectors.toList());
        for (var entry : remainMap.entrySet()) {
            if (entry.getKey().contains("start")) {
                continue;
            }
            String tempWarehouse = entry.getKey();
            DateTime arrivingDate = fastestArrivingDateMap.get(tempWarehouse);
            if (arrivingDate != null && !tempWarehouse.equals(warehouse)) {
                // 若不是待计算的仓库，且可以到货，将剩余库存补满，模拟一直有货的情况
                double remainInventory = Integer.MAX_VALUE;
                tempRemainList.add(FactoryRemainInventoryDto.builder()
                        .storeNum(remainInventory)
                        .warehouse(tempWarehouse)
                        .enableUsingDate(arrivingDate)
                        .build());
            }
            // 各仓当天真实的剩余库存
            tempRemainList.add(FactoryRemainInventoryDto.builder()
                    .storeNum(entry.getValue())
                    .warehouse(tempWarehouse)
                    .enableUsingDate(startDate)
                    .build());
        }

        Map<String, Double> shippingRatioMap = shippingRules.getShippingRatio();
        Map<String, Integer> headShippingDays = shippingRules.getHeadShippingDays();
        int minShippingDate = headShippingDays.entrySet().stream()
                .filter(h -> shippingRatioMap.containsKey(h.getKey()) && shippingRatioMap.get(h.getKey()) > 0)
                .map(Map.Entry::getValue)
                .sorted().findFirst().orElseThrow(() -> new NullPointerException("头程时间为空"));

        long between = DateUtil.between(replenishmentCreateDate, maxSaleDate, DateUnit.DAY);
        // 重新计算一遍库存衰减情况，找出待计算仓库哪一天的目标日销 > 实际日销
        MockTableCalDto mockTableCalDto = new MockTableCalDto(new ArrayList<>(),
                (int) between, tempRemainList, headShippingDays, replenishmentCreateDate, new ArrayList<>(),
                new ArrayList<>(), priorDeliveryList, minShippingDate, targetSalesMap, shippingRatioMap, sortedWarehouseList);
        TrialCalReplenishmentDto trialCalReplenishment = calDeliveryResults(mockTableCalDto);

        var everydaySaleMap = trialCalReplenishment.getEverydaySaleProductMap();
        startDate = new DateTime(startDate);
        for (DateTime calDate = new DateTime(startDate); calDate.isBeforeOrEquals(maxSaleDate); calDate.offset(DAY_OF_YEAR, 1)) {
            String calDateStr = calDate.toString(YYYY_MM_DD_DATE_FORMAT_SLASH);
            double targetSales = targetSalesMap.getOrDefault(calDateStr, 0D);
            if (targetSales == 0) {
                startDate = calDate;
                continue;
            }
            Map<String, Double> tempRemainMap = trialCalReplenishment.getEverydayRemainInventoryMap().get(calDateStr);
            BigDecimal everydayTotalSaleNumDec = calRemainInventoryService.consumeZeroRatioWarehouseInventory(shippingRatioMap, tempRemainMap,
                    new HashMap<>(), new HashMap<>(), targetSales);
            if (everydayTotalSaleNumDec.compareTo(BigDecimal.ZERO) == 0) {
                startDate = calDate;
                continue;
            }

            double salesNum = everydaySaleMap.get(calDateStr).getOrDefault(warehouse, 0D);
            targetSales = roundHalfUpToThreeDecimal(everydayTotalSaleNumDec.doubleValue() * shippingRatioMap.get(warehouse));
            // 目标日销 > 实际日销则表明这一天的货不够卖了
            startDate = calDate;
            if (targetSales > salesNum) {
                break;
            }
        }

        return startDate;
    }

    @Override
    public DateTime calReplenishmentOrDeliveryNumStartDate(DateTime startDate, List<FactoryRemainInventoryDto> onShippingInventoryList,
                                                           String warehouse, Map<String, DateTime> fastestArrivingDateMap,
                                                           Map<String, Double> targetSalesMap, DateTime maxSaleDate,
                                                           Map<String, Double> remainMap, DateTime replenishmentCreateDate,
                                                           ShippingProjectBaseParamDto shippingRules,
                                                           List<String> sortedWarehouseList, List<FactoryRemainInventoryDto> priorDeliveryList) {
        List<FactoryRemainInventoryDto> newShippingInventoryList = new ArrayList<>(onShippingInventoryList.stream().map(ObjectUtil::clone).toList());

        if (maxSaleDate.isBeforeOrEquals(startDate)) {
            return startDate;
        }

        DateTime finalStartDate = startDate;
        List<FactoryRemainInventoryDto> tempRemainList = newShippingInventoryList.stream()
                .filter(f -> finalStartDate.isBefore(f.getEnableUsingDate()))
                .collect(Collectors.toList());
        for (var entry : remainMap.entrySet()) {
            if (entry.getKey().contains("start")) {
                continue;
            }
            String tempWarehouse = entry.getKey();
            DateTime arrivingDate = fastestArrivingDateMap.get(tempWarehouse);
            if (arrivingDate != null && !tempWarehouse.equals(warehouse)) {
                // 若不是待计算的仓库，且可以到货，将剩余库存补满，模拟一直有货的情况
                double remainInventory = Integer.MAX_VALUE;
                tempRemainList.add(FactoryRemainInventoryDto.builder()
                        .storeNum(remainInventory)
                        .warehouse(tempWarehouse)
                        .enableUsingDate(arrivingDate)
                        .build());
            }
            // 各仓当天真实的剩余库存
            tempRemainList.add(FactoryRemainInventoryDto.builder()
                    .storeNum(entry.getValue())
                    .warehouse(tempWarehouse)
                    .enableUsingDate(startDate)
                    .build());
        }

        Map<String, Double> shippingRatioMap = shippingRules.getShippingRatio();
        Map<String, Integer> headShippingDays = shippingRules.getHeadShippingDays();
        int minShippingDate = headShippingDays.entrySet().stream()
                .filter(h -> shippingRatioMap.containsKey(h.getKey()) && shippingRatioMap.get(h.getKey()) > 0)
                .map(Map.Entry::getValue)
                .sorted().findFirst().orElseThrow(() -> new NullPointerException("头程时间为空"));

        long between = DateUtil.between(replenishmentCreateDate, maxSaleDate, DateUnit.DAY);
        // 重新计算一遍库存衰减情况，找出待计算仓库哪一天的目标日销 > 实际日销
        MockTableCalDto mockTableCalDto = new MockTableCalDto(new ArrayList<>(), (int) between, tempRemainList, headShippingDays,
                replenishmentCreateDate, new ArrayList<>(), new ArrayList<>(), priorDeliveryList, minShippingDate, targetSalesMap,
                shippingRatioMap, sortedWarehouseList);
        TrialCalReplenishmentDto trialCalReplenishment = calDeliveryResults(mockTableCalDto);

        var everydaySaleMap = trialCalReplenishment.getEverydaySaleProductMap();
        for (DateTime calDate = new DateTime(startDate); calDate.isBeforeOrEquals(maxSaleDate); calDate.offset(DAY_OF_YEAR, 1)) {
            String calDateStr = calDate.toString(YYYY_MM_DD_DATE_FORMAT_SLASH);
            double targetSales = targetSalesMap.getOrDefault(DateUtil.beginOfMonth(calDate).toString(YYYY_MM_DD_DATE_FORMAT_SLASH), 0D);
            double salesNum = everydaySaleMap.get(calDateStr).getOrDefault(warehouse, 0D);
            targetSales = roundHalfUpToThreeDecimal(targetSales * shippingRatioMap.get(warehouse));
            // 目标日销 > 实际日销则表明这一天的货不够卖了
            if (targetSales > salesNum) {
                startDate = calDate;
                break;
            }
        }

        return startDate;
    }

    @Override
    public TrialCalReplenishmentDto calDeliveryResults(MockTableCalDto mockTableCalDto) {
        List<FactoryRemainInventoryDto> inventoryDtos = mockTableCalDto.getInventoryDtos();
        List<FactoryRemainInventoryDto> factoryRemainInventoryList = mockTableCalDto.getFactoryRemainInventoryList();
        List<FactoryRemainInventoryDto> onShippingRemainInventoryList = new ArrayList<>(inventoryDtos) {{
            addAll(factoryRemainInventoryList);
            addAll(mockTableCalDto.getPriorDeliveryList());
        }};

        Map<String, Integer> headShippingDate = mockTableCalDto.getHeadShippingDateMap();
        DateTime projectCreateDate = mockTableCalDto.getProjectCreateDate();
        int calCircle = mockTableCalDto.getCalCircle();
        List<FactoryRemainInventoryDto> importFactoryRemainInventoryList = mockTableCalDto.getImportFactoryRemainInventoryList();
        Map<String, Map<String, Double>> mockShippingMap = calRemainInventoryService.getMockShippingMap(
                onShippingRemainInventoryList, headShippingDate, projectCreateDate, calCircle, importFactoryRemainInventoryList
        );

        Map<String, Map<String, Double>> everyDayWarehouseSaleMap = new TreeMap<>();

        // 将所有试算结果重新计算一遍，算出整个表格
        List<FactoryRemainInventoryDto> remainInventoryListForCal = mockTableCalDto.getRemainInventoryListForCal();
        remainInventoryListForCal.addAll(inventoryDtos);
        remainInventoryListForCal.addAll(factoryRemainInventoryList);
        remainInventoryListForCal.addAll(mockTableCalDto.getPriorDeliveryList());

        int minShippingDate = mockTableCalDto.getMinShippingDate();
        Map<String, Double> targetSalesMap = mockTableCalDto.getTargetSalesMap();
        Map<String, Double> inventorySaleDefRatio = mockTableCalDto.getInventorySaleDefRatio();
        List<String> sortedWarehouseList = mockTableCalDto.getSortedWarehouseList();
        var everyDayRemainInventoryMap = calRemainInventoryService.calRemainInventoryMap(projectCreateDate,
                remainInventoryListForCal, calCircle, minShippingDate, targetSalesMap, inventorySaleDefRatio,
                everyDayWarehouseSaleMap, sortedWarehouseList);

        calRemainInventoryService.implementShippingStartDateAndArrivingDate(inventoryDtos, headShippingDate,
                mockShippingMap, everyDayRemainInventoryMap);
        return TrialCalReplenishmentDto.builder()
                .everydayOnShippingInventoryMap(mockShippingMap)
                .everydayRemainInventoryMap(everyDayRemainInventoryMap)
                .everydaySaleProductMap(everyDayWarehouseSaleMap)
                .shippingInventoryList(inventoryDtos)
                .priorDeliveryList(mockTableCalDto.getPriorDeliveryList())
                .build();
    }

    public PurchaseDayNeedNumDp calNeedReplenishmentNum(Map<String, Map<String, Double>> everyRetainInventoryMap,
                                                        Map<String, Double> targetSalesMap, int rangeDays, Map<String, Double> shippingRatioMap,
                                                        String warehouse, DateTime calStartDate,
                                                        Map<String, DateTime> fastestArrivingDateMap,
                                                        List<FactoryRemainInventoryDto> onShippingInventoryList,
                                                        List<FactoryRemainInventoryDto> priorDeliveryList
    ) {
        Map<String, Map<String, Double>> tempEverydayRetainMap = new TreeMap<>() {{
            putAll(everyRetainInventoryMap);
        }};

        // 此处是在途数据
        List<FactoryRemainInventoryDto> newShippingInventoryList = onShippingInventoryList.stream().map(ObjectUtil::clone).collect(Collectors.toList());
        newShippingInventoryList.addAll(priorDeliveryList);
        DateTime endDate = calStartDate.offsetNew(DAY_OF_YEAR, rangeDays);

        // 计算出在这次补货区间内有多少能在什么时间到货
        Map<Date, Double> dateStoreNumMap = newShippingInventoryList.stream()
                .filter(f -> f.getWarehouse().equals(warehouse))
                .filter(f -> calStartDate.isBeforeOrEquals(f.getEnableUsingDate()) && endDate.isAfter(f.getEnableUsingDate()))
                .collect(Collectors.toMap(FactoryRemainInventoryDto::getEnableUsingDate, FactoryRemainInventoryDto::getStoreNum, Double::sum));

        double totalTargetSales = 0;
        double shippingNum = 0;
        for (DateTime calDate = DateUtil.dateNew(calStartDate); calDate.isBefore(endDate); calDate.offset(DAY_OF_YEAR, 1)) {
            double targetSales = targetSalesMap.getOrDefault(calDate.toString(YYYY_MM_DD_DATE_FORMAT_SLASH), 0D);
            if (targetSales == 0) {
                continue;
            }
            Map<String, Double> tempRemainInventoryMap = tempEverydayRetainMap.getOrDefault(calDate.toString(YYYY_MM_DD_DATE_FORMAT_SLASH), new HashMap<>());
            // 先计算能到货或者有剩余库存的仓库的实际日销比例
            double totalRatio = shippingRatioMap.entrySet().stream()
                    .filter(f -> f.getValue() > 0)
                    .filter(s -> {
                        String sWarehouse = s.getKey();
                        DateTime arrivingDate = fastestArrivingDateMap.get(sWarehouse);
                        return tempRemainInventoryMap.getOrDefault(sWarehouse, 0D) > 0 || arrivingDate.isBeforeOrEquals(calDate);
                    })
                    .mapToDouble(Map.Entry::getValue)
                    .sum();
            if (totalRatio == 0) {
                continue;
            }

            String nextDay = calDate.offsetNew(DAY_OF_YEAR, 1).toString(YYYY_MM_DD_DATE_FORMAT_SLASH);
            Map<String, Double> tempNextdayRetainMap = new HashMap<>();
            // 先计算比例为0的仓库消耗情况
            BigDecimal everydayTotalSaleNumDec = calRemainInventoryService.consumeZeroRatioWarehouseInventory(shippingRatioMap, tempRemainInventoryMap,
                    new HashMap<>(), tempNextdayRetainMap, targetSales);
            if (everydayTotalSaleNumDec.compareTo(BigDecimal.ZERO) == 0) {
                shippingRatioMap.entrySet().stream()
                        .filter(entry -> entry.getValue() > 0)
                        .forEach(s -> {
                            String sWarehouse = s.getKey();
                            double sum = calShippingInventorySum(newShippingInventoryList, sWarehouse, nextDay);
                            tempNextdayRetainMap.compute(sWarehouse, (k, v) -> sum + tempRemainInventoryMap.getOrDefault(sWarehouse, 0D));
                        });
                tempEverydayRetainMap.put(nextDay, tempNextdayRetainMap);
                continue;
            }
            targetSales = everydayTotalSaleNumDec.setScale(3, HALF_UP).doubleValue();

            totalRatio = shippingRatioMap.entrySet().stream()
                    .filter(entry -> entry.getValue() > 0)
                    .filter(s -> {
                        String sWarehouse = s.getKey();
                        DateTime arrivingDate = fastestArrivingDateMap.get(sWarehouse);
                        return arrivingDate.isBeforeOrEquals(calDate);
                    })
                    .mapToDouble(Map.Entry::getValue)
                    .sum();
            // 若有比例的仓全部都能到货，则待计算仓的日销直接等于目标日销，否则需要按照实际日销计算
            if (roundHalfUpToThreeDecimal(totalRatio) == 1) {
                targetSales = roundHalfUpToThreeDecimal(shippingRatioMap.get(warehouse) * targetSales);
            } else {
                boolean needRepeat;
                do {
                    Map<String, Double> warehouseSalesMap = new HashMap<>();
                    needRepeat = false;
                    totalRatio = shippingRatioMap.entrySet().stream()
                            .filter(f -> f.getValue() > 0)
                            .filter(s -> {
                                String sWarehouse = s.getKey();
                                DateTime arrivingDate = fastestArrivingDateMap.get(sWarehouse);
                                return tempRemainInventoryMap.getOrDefault(sWarehouse, 0D) > 0 || arrivingDate.isBeforeOrEquals(calDate);
                            })
                            .mapToDouble(Map.Entry::getValue)
                            .sum();
                    for (Map.Entry<String, Double> entry : tempRemainInventoryMap.entrySet()) {
                        DateTime arrivingDate = fastestArrivingDateMap.get(entry.getKey());
                        if (arrivingDate != null && arrivingDate.isBeforeOrEquals(calDate)) {
                            continue;
                        }

                        double sum = onShippingInventoryList.stream()
                                .filter(f -> f.getWarehouse().equals(entry.getKey()) && format.format(f.getEnableUsingDate()).equals(nextDay))
                                .mapToDouble(FactoryRemainInventoryDto::getStoreNum)
                                .sum();
                        if (entry.getValue() <= 0) {
                            tempNextdayRetainMap.put(entry.getKey(), sum);
                            continue;
                        }

                        // 计算未到货仓库每天的消耗量
                        double retainValue = entry.getValue();
                        double warehouseRealRatio = shippingRatioMap.getOrDefault(entry.getKey(), 0D) / totalRatio;
                        double todaySales = roundHalfUpToThreeDecimal(targetSales * warehouseRealRatio);
                        if (retainValue < todaySales) {
                            entry.setValue(0D);
                            targetSales -= retainValue;
                            totalRatio -= warehouseRealRatio;
                            needRepeat = true;

                            tempNextdayRetainMap.put(entry.getKey(), sum);
                        } else {
                            warehouseSalesMap.compute(entry.getKey(), (k, v) -> Optional.ofNullable(v).orElse(0D) + todaySales);
                        }
                    }
                    if (!needRepeat) {
                        for (var entry : warehouseSalesMap.entrySet()) {
                            double sum = onShippingInventoryList.stream()
                                    .filter(f -> f.getWarehouse().equals(entry.getKey()) && format.format(f.getEnableUsingDate()).equals(nextDay))
                                    .mapToDouble(FactoryRemainInventoryDto::getStoreNum)
                                    .sum();
                            targetSales -= entry.getValue();
                            double todayRemain = tempRemainInventoryMap.get(entry.getKey()) - entry.getValue();
                            tempNextdayRetainMap.put(entry.getKey(), roundHalfUpToThreeDecimal(sum + todayRemain));
                        }
                    }

                    // 计算到货仓的实际日销比例
                    double totalArrivingRatio = shippingRatioMap.entrySet().stream()
                            .filter(f -> f.getValue() > 0)
                            .filter(s -> {
                                String sWarehouse = s.getKey();
                                DateTime arrivingDate = fastestArrivingDateMap.get(sWarehouse);
                                return tempRemainInventoryMap.getOrDefault(sWarehouse, 0D) > 0 || arrivingDate.isBeforeOrEquals(calDate);
                            })
                            .mapToDouble(Map.Entry::getValue)
                            .sum();
                    BigDecimal realRatio = BigDecimal.ZERO;
                    if (totalArrivingRatio != 0) {
                        realRatio = BigDecimal.valueOf(shippingRatioMap.get(warehouse)).divide(BigDecimal.valueOf(totalArrivingRatio), 3, HALF_UP);
                    }
                    targetSales = roundHalfUpToThreeDecimal(targetSales * realRatio.doubleValue());
                } while (needRepeat);
            }

            // 判断是否有在途到货，如果有的话，需要剔除掉
            for (var entry : dateStoreNumMap.entrySet()) {
                Date deliveryArrivingDate = entry.getKey();
                if (calDate.isBefore(deliveryArrivingDate)) {
                    continue;
                }

                double storeNum = entry.getValue();
                if (storeNum >= targetSales) {
                    entry.setValue(roundHalfUpToThreeDecimal(storeNum - targetSales));
                    shippingNum += targetSales;
                    targetSales = 0;
                    break;
                }
                entry.setValue(0D);
                targetSales -= storeNum;
                shippingNum += storeNum;
            }
            totalTargetSales += targetSales;
        }

        if (totalTargetSales <= 0) {
            return new PurchaseDayNeedNumDp();
        }
        Map<String, Double> retainMap = everyRetainInventoryMap.get(calStartDate.toString(YYYY_MM_DD_DATE_FORMAT_SLASH));
        double remainNum = retainMap.get(warehouse);
        if (remainNum >= totalTargetSales) {
            return new PurchaseDayNeedNumDp(0D, shippingNum + totalTargetSales);
        } else {
            return new PurchaseDayNeedNumDp(totalTargetSales - remainNum, shippingNum + remainNum);
        }
    }

    @Override
    public PurchaseDayNeedNumDp calNeedDeliveryNum(
            Map<String, Map<String, Double>> everyRetainInventoryMap, Map<String, Double> targetSalesMap, int rangeDays,
            Map<String, Double> shippingRatioMap, String warehouse, DateTime calStartDate, double factorySum,
            Map<String, DateTime> fastestArrivingDateMap, List<FactoryRemainInventoryDto> onShippingInventoryList,
            List<FactoryRemainInventoryDto> priorDeliveryList) {
        Map<String, Map<String, Double>> tempEverydayRetainMap = new TreeMap<>() {{
            putAll(everyRetainInventoryMap);
        }};

        // 此处是在途数据
        List<FactoryRemainInventoryDto> newShippingInventoryList = new ArrayList<>(onShippingInventoryList);
        newShippingInventoryList.addAll(priorDeliveryList);
        DateTime endDate = calStartDate.offsetNew(DAY_OF_YEAR, rangeDays);

        // 计算出在这次补货区间内有多少能在什么时间到货
        Map<Date, Double> dateStoreNumMap = newShippingInventoryList.stream()
                .filter(f -> f.getWarehouse().equals(warehouse))
                .filter(f -> calStartDate.isBefore(f.getEnableUsingDate()) && endDate.isAfterOrEquals(f.getEnableUsingDate()))
                .collect(Collectors.toMap(FactoryRemainInventoryDto::getEnableUsingDate, FactoryRemainInventoryDto::getStoreNum, Double::sum));

        double totalTargetSales = 0;
        double shippingNum = 0;
        DateTime deliveryEndDate = null;
        for (DateTime calDate = DateUtil.dateNew(calStartDate); calDate.isBefore(endDate); calDate.offset(DAY_OF_YEAR, 1)) {
            double targetSales = targetSalesMap.getOrDefault(calDate.toString(YYYY_MM_DD_DATE_FORMAT_SLASH), 0D);
            if (targetSales == 0) {
                continue;
            }
            Map<String, Double> tempRemainInventoryMap = tempEverydayRetainMap.getOrDefault(calDate.toString(YYYY_MM_DD_DATE_FORMAT_SLASH), new HashMap<>());
            // 先计算能到货或者有剩余库存的仓库的实际日销比例
            double totalRatio = shippingRatioMap.entrySet().stream()
                    .filter(f -> f.getValue() > 0)
                    .filter(s -> {
                        String sWarehouse = s.getKey();
                        DateTime arrivingDate = fastestArrivingDateMap.get(sWarehouse);
                        return tempRemainInventoryMap.getOrDefault(sWarehouse, 0D) > 0 || arrivingDate.isBeforeOrEquals(calDate);
                    })
                    .mapToDouble(Map.Entry::getValue)
                    .sum();
            if (totalRatio == 0) {
                continue;
            }

            String nextDay = calDate.offsetNew(DAY_OF_YEAR, 1).toString(YYYY_MM_DD_DATE_FORMAT_SLASH);
            Map<String, Double> tempNextdayRetainMap = new HashMap<>();
            // 先计算比例为0的仓库消耗情况
            BigDecimal everydayTotalSaleNumDec = calRemainInventoryService.consumeZeroRatioWarehouseInventory(shippingRatioMap, tempRemainInventoryMap,
                    new HashMap<>(), tempNextdayRetainMap, targetSales);
            if (everydayTotalSaleNumDec.compareTo(BigDecimal.ZERO) == 0) {
                shippingRatioMap.entrySet().stream()
                        .filter(entry -> entry.getValue() > 0)
                        .forEach(s -> {
                            String sWarehouse = s.getKey();
                            double sum = calShippingInventorySum(newShippingInventoryList, sWarehouse, nextDay);
                            tempNextdayRetainMap.compute(sWarehouse, (k, v) -> sum + tempRemainInventoryMap.getOrDefault(sWarehouse, 0D));
                        });
                tempEverydayRetainMap.put(nextDay, tempNextdayRetainMap);
                continue;
            }
            targetSales = everydayTotalSaleNumDec.setScale(3, HALF_UP).doubleValue();

            totalRatio = shippingRatioMap.entrySet().stream()
                    .filter(entry -> entry.getValue() > 0)
                    .filter(s -> {
                        String sWarehouse = s.getKey();
                        DateTime arrivingDate = fastestArrivingDateMap.get(sWarehouse);
                        return arrivingDate.isBeforeOrEquals(calDate);
                    })
                    .mapToDouble(Map.Entry::getValue)
                    .sum();
            // 若有比例的仓全部都能到货，则待计算仓的日销直接等于目标日销，否则需要按照实际日销计算
            if (roundHalfUpToThreeDecimal(totalRatio) == 1) {
                targetSales = roundHalfUpToThreeDecimal(shippingRatioMap.get(warehouse) * targetSales);
            } else {
                boolean needRepeat;
                do {
                    Map<String, Double> warehouseSalesMap = new HashMap<>();
                    needRepeat = false;
                    totalRatio = shippingRatioMap.entrySet().stream()
                            .filter(f -> f.getValue() > 0)
                            .filter(s -> {
                                String sWarehouse = s.getKey();
                                DateTime arrivingDate = fastestArrivingDateMap.get(sWarehouse);
                                return tempRemainInventoryMap.getOrDefault(sWarehouse, 0D) > 0 || arrivingDate.isBeforeOrEquals(calDate);
                            })
                            .mapToDouble(Map.Entry::getValue)
                            .sum();
                    for (Map.Entry<String, Double> entry : tempRemainInventoryMap.entrySet()) {
                        DateTime arrivingDate = fastestArrivingDateMap.get(entry.getKey());
                        if (arrivingDate != null && arrivingDate.isBeforeOrEquals(calDate)) {
                            continue;
                        }

                        double sum = onShippingInventoryList.stream()
                                .filter(f -> f.getWarehouse().equals(entry.getKey()) && format.format(f.getEnableUsingDate()).equals(nextDay))
                                .mapToDouble(FactoryRemainInventoryDto::getStoreNum)
                                .sum();
                        if (entry.getValue() <= 0) {
                            tempNextdayRetainMap.put(entry.getKey(), sum);
                            continue;
                        }

                        // 计算未到货仓库每天的消耗量
                        double retainValue = entry.getValue();
                        double warehouseRealRatio = shippingRatioMap.getOrDefault(entry.getKey(), 0D) / totalRatio;
                        double todaySales = roundHalfUpToThreeDecimal(targetSales * warehouseRealRatio);
                        if (retainValue < todaySales) {
                            entry.setValue(0D);
                            targetSales -= retainValue;
                            totalRatio -= warehouseRealRatio;
                            needRepeat = true;

                            tempNextdayRetainMap.put(entry.getKey(), sum);
                        } else {
                            warehouseSalesMap.compute(entry.getKey(), (k, v) -> Optional.ofNullable(v).orElse(0D) + todaySales);
                        }
                    }
                    if (!needRepeat) {
                        for (var entry : warehouseSalesMap.entrySet()) {
                            double sum = onShippingInventoryList.stream()
                                    .filter(f -> f.getWarehouse().equals(entry.getKey()) && format.format(f.getEnableUsingDate()).equals(nextDay))
                                    .mapToDouble(FactoryRemainInventoryDto::getStoreNum)
                                    .sum();
                            double todayRemain = tempRemainInventoryMap.get(entry.getKey()) - entry.getValue();
                            tempNextdayRetainMap.put(entry.getKey(), roundHalfUpToThreeDecimal(sum + todayRemain));
                        }
                    }

                    // 计算到货仓的实际日销比例
                    double totalArrivingRatio = shippingRatioMap.entrySet().stream()
                            .filter(f -> f.getValue() > 0)
                            .filter(s -> {
                                String sWarehouse = s.getKey();
                                DateTime arrivingDate = fastestArrivingDateMap.get(sWarehouse);
                                return tempRemainInventoryMap.getOrDefault(sWarehouse, 0D) > 0 || arrivingDate.isBeforeOrEquals(calDate);
                            })
                            .mapToDouble(Map.Entry::getValue)
                            .sum();
                    BigDecimal realRatio = BigDecimal.ZERO;
                    if (totalArrivingRatio != 0) {
                        realRatio = BigDecimal.valueOf(shippingRatioMap.get(warehouse)).divide(BigDecimal.valueOf(totalArrivingRatio), 3, HALF_UP);
                    }
                    targetSales = roundHalfUpToThreeDecimal(targetSales * realRatio.doubleValue());
                } while (needRepeat);
            }

            // 判断是否有在途到货，如果有的话，需要剔除掉
            for (var entry : dateStoreNumMap.entrySet()) {
                Date deliveryArrivingDate = entry.getKey();
                if (calDate.isBefore(deliveryArrivingDate)) {
                    continue;
                }

                double storeNum = entry.getValue();
                if (storeNum >= targetSales) {
                    entry.setValue(roundHalfUpToThreeDecimal(storeNum - targetSales));
                    shippingNum += targetSales;
                    targetSales = 0;
                    break;
                }
                entry.setValue(0D);
                targetSales -= storeNum;
                shippingNum += storeNum;
            }
            totalTargetSales += targetSales;

            // 判断需发货量以及能发货的工厂交货数量的大小，若需发货量已经比能发货的量还多了，则需要跳出循环，并确定发货量区间的结束日期
            if (totalTargetSales > factorySum) {
                deliveryEndDate = new DateTime(calDate);
                break;
            }
        }

        if (totalTargetSales <= 0) {
            return new PurchaseDayNeedNumDp(endDate);
        }
        Map<String, Double> retainMap = everyRetainInventoryMap.get(calStartDate.toString(YYYY_MM_DD_DATE_FORMAT_SLASH));
        double remainNum = retainMap.get(warehouse);
        deliveryEndDate = deliveryEndDate == null ? endDate : deliveryEndDate;
        if (remainNum >= totalTargetSales) {
            return new PurchaseDayNeedNumDp(0D, shippingNum + totalTargetSales, deliveryEndDate);
        } else {
            return new PurchaseDayNeedNumDp(roundHalfUpToThreeDecimal(totalTargetSales - remainNum), shippingNum + remainNum, deliveryEndDate);
        }
    }

    @Override
    public TreeMap<String, Double> calTargetSalesMap(String saleDestination, LocalDate startDate) {
        var targetSalesMap = JSON.parseObject(saleDestination, new TypeReference<TreeMap<String, Double>>() {
        });
        TreeMap<String, Double> monthTargetSalesMap = new TreeMap<>(Comparator.comparing(c -> YearMonth.parse(c, chineseFormatter)));

        LocalDate endDate;
        LocalDate startDateMonthLast = startDate.with(TemporalAdjusters.lastDayOfMonth());
        if (CollectionUtil.isEmpty(targetSalesMap)) {
            endDate = startDateMonthLast;
        } else {
            LocalDate parse = LocalDate.parse(targetSalesMap.lastKey(), formatter);
            endDate = parse.isAfter(startDateMonthLast) ? parse : startDateMonthLast;
        }
        for (var calDate = startDate; !calDate.isAfter(endDate); calDate = calDate.plusDays(1)) {
            String date = calDate.format(formatter);
            targetSalesMap.putIfAbsent(date, 0D);
            YearMonth yearMonth = YearMonth.from(calDate);
            Double daySales = targetSalesMap.getOrDefault(date, 0D);
            monthTargetSalesMap.compute(yearMonth.format(chineseFormatter),
                    (k, v) -> roundHalfUpToThreeDecimal(Optional.ofNullable(v).orElse(0D) + daySales));
        }
        return monthTargetSalesMap;
    }

    private double calShippingInventorySum(List<FactoryRemainInventoryDto> newShippingInventoryList, String warehouse, String nextDay) {
        return newShippingInventoryList.stream()
                .filter(f -> f.getWarehouse().equals(warehouse) && format.format(f.getEnableUsingDate()).equals(nextDay))
                .mapToDouble(FactoryRemainInventoryDto::getStoreNum)
                .sum();
    }

    private double roundHalfUpToThreeDecimal(double num) {
        return BigDecimal.valueOf(num).setScale(3, HALF_UP).doubleValue();
    }
}
