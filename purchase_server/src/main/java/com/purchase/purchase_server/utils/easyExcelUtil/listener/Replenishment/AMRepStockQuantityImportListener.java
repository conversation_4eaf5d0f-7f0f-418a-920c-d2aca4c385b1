package com.purchase.purchase_server.utils.easyExcelUtil.listener.Replenishment;

import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.context.AnalysisContext;
import com.purchase.purchase_server.entity.dataObject.VirtualProductDO;
import com.purchase.purchase_server.entity.dto.FactoryRemainInventoryDto;
import com.purchase.purchase_server.entity.dto.SenboWarehouseDto;
import com.purchase.purchase_server.entity.vo.UpgradeInfoInteriorVo;
import com.purchase.purchase_server.enums.RepImportSheetNameEnum;
import com.purchase.purchase_server.model.purchase.RepStockQuantityInfoOtherDp;
import com.purchase.purchase_server.utils.easyExcelUtil.manager.ReplenishmentManager;
import com.purchase.purchase_server.utils.easyExcelUtil.manager.ReplenishmentMapManager;
import lombok.extern.slf4j.Slf4j;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description 导入发货计划-AM库存
 * <AUTHOR>
 * @Date 2024/8/6 15:49
 **/
@Slf4j
public class AMRepStockQuantityImportListener extends AbstractRepImportListener<HashMap<Integer, String>> {

    private final Map<String, String> warehouseNameIdMap;

    private final Map<String, String> channelMapping;

    private final Map<String, String> channelIdNameMap;

    private final List<String> amChannelList = List.of("AM", "AC", "AMEU", "AK");

    private final List<String> lampChannelList1 = List.of("AM", "AC");

    private final Set<String> lampChannelSet2 = Set.of("AMEU", "AMEUSS", "AMEU2", "AMEU3");

    public AMRepStockQuantityImportListener(List<SenboWarehouseDto> senboWarehouseList, Map<String, String> channelMapping,
                                            Map<String, String> channelIdNameMap) {
        super(senboWarehouseList);
        this.warehouseNameIdMap = senboWarehouseList.stream().collect(Collectors.toMap(SenboWarehouseDto::getSenboWarehouse, SenboWarehouseDto::getSenboWarehouseId));
        this.channelMapping = channelMapping;
        this.channelIdNameMap = channelIdNameMap;
    }

    @Override
    public void invoke(HashMap<Integer, String> data, AnalysisContext analysisContext) {
//        log.warn("导入发货计划-------------导入excel进行中，成功解析excel");
        Integer approximateRowNumber = analysisContext.readSheetHolder().getApproximateTotalRowNumber();
        if (approximateRowNumber != null && approximateRowNumber > TOTAL_ROW_NUMBER) {
            throw new RuntimeException(String.format("导入数据超过%d行，请缩小导入数据量", TOTAL_ROW_NUMBER));
        }
        ReplenishmentMapManager replenishmentMapManager = ReplenishmentMapManager.getInstance();
        //动态读取 excel逻辑删除造成数据null问题
        data.entrySet().removeIf(entry -> entry.getKey() > replenishmentMapManager.getStockQuantityHeadMap().size() - 1);

        new RepStockQuantityInfoOtherDp(data);

        String AMEU_WAREHOUSE = "FBA(AMEU)";
        String AMEU_K_WAREHOUSE = "FBA(AMEU)-K";

        // 替换数据格式
        HashMap<String, String> keyReplacementMap = new HashMap<>();
        HashMap<Integer, String> stockQuantityHeadMap = replenishmentMapManager.getStockQuantityHeadMap();
        String virtualSku = data.get(0);
        VirtualProductDO virtualProductDO = Optional.ofNullable(replenishmentMapManager.getSkuVirtualProductMap().get(virtualSku))
                .orElseGet(() -> Optional.ofNullable(replenishmentMapManager.getOldSkuVirtualProductMap().get(virtualSku))
                        .orElseThrow(() -> new IllegalArgumentException("虚拟sku不存在")));
        final double ZERO = 0.0;
        double ameuNum;
        double ameuKNum;
        for (Map.Entry<Integer, String> entry : new HashMap<>(data).entrySet()) {
            Integer key = entry.getKey();
            String value = entry.getValue();
            // 根据第二个HashMap中的映射关系，将key替换为对应的value
            if (stockQuantityHeadMap.containsKey(key)) {
                String channel = channelIdNameMap.getOrDefault(virtualProductDO.getChannel(), virtualProductDO.getChannel());
                String virtualChannel = channelMapping.getOrDefault(channel, channel);
                String head = stockQuantityHeadMap.get(key);
                double storeNum = NumberUtil.parseDouble(keyReplacementMap.getOrDefault(head, "0"));

                if (analysisContext.readSheetHolder().getSheetName().equals(RepImportSheetNameEnum.AM_STOCK_QUANTITY.getCode())) {
                    if (key > 0 && key <= amChannelList.size()) {
                        String amChannel = amChannelList.get(key - 1);
                        double channelValue = Double.parseDouble(data.get(key));
                        if (channelValue > ZERO && !virtualChannel.equals(amChannel)) {
                            throw new IllegalArgumentException("产品渠道与仓库渠道不一致");
                        }
                        // 计算总值
                        double totalValue = storeNum;
                        if (virtualChannel.equals(amChannel)) {
                            double additionalValue = NumberUtil.parseDouble(data.get(5)) + NumberUtil.parseDouble(data.get(6));
                            totalValue += NumberUtil.parseDouble(value) + additionalValue;
                        }
                        keyReplacementMap.put(head, String.valueOf(totalValue));

                    } else if (key > amChannelList.size() + 2) {
                        keyReplacementMap.put(head, String.valueOf(NumberUtil.parseDouble(value) + storeNum));
                    }
                } else if (analysisContext.readSheetHolder().getSheetName().equals(RepImportSheetNameEnum.LAMP_STOCK_QUANTITY.getCode())) {
                    if (key > 0 && key <= lampChannelList1.size()) {
                        String lampChannel = lampChannelList1.get(key - 1);
                        double channelValue = Double.parseDouble(data.get(key));
                        if (channelValue > ZERO && !virtualChannel.equals(lampChannel)) {
                            throw new IllegalArgumentException("产品渠道与仓库渠道不一致");
                        }
                        double totalValue = storeNum;
                        if (virtualChannel.equals(lampChannel)) {
                            totalValue += NumberUtil.parseDouble(value) + NumberUtil.parseDouble(data.get(5));
                        }
                        keyReplacementMap.put(head, String.valueOf(totalValue));
                    } else if (key > lampChannelList1.size() && key < lampChannelList1.size() + lampChannelSet2.size()) {
                        double channelValue = Double.parseDouble(data.get(key));
                        if (channelValue > ZERO && !lampChannelSet2.contains(virtualChannel)) {
                            throw new IllegalArgumentException("产品渠道与仓库渠道不一致");
                        }
                        if (!lampChannelSet2.contains(virtualChannel)) {
                            continue;
                        }
                        if (warehouseNameIdMap.get(AMEU_WAREHOUSE).equals(head)) {
                            ameuNum = NumberUtil.parseDouble(value);
                            double finalAmeuNum = ameuNum;
                            keyReplacementMap.compute(head, (k, v) -> String.valueOf(finalAmeuNum > 0 ? finalAmeuNum + NumberUtil.parseDouble(data.get(5)) : 0));
                        } else if (warehouseNameIdMap.get(AMEU_K_WAREHOUSE).equals(head)){
                            ameuKNum = NumberUtil.parseDouble(value);
                            if (Double.parseDouble(keyReplacementMap.get(warehouseNameIdMap.get(AMEU_WAREHOUSE))) > 0) {
                                keyReplacementMap.put(head, String.valueOf(ameuKNum));
                            } else if (ameuKNum > 0){
                                keyReplacementMap.put(head, String.valueOf(ameuKNum + NumberUtil.parseDouble(data.get(5))));
                            } else {
                                keyReplacementMap.compute(warehouseNameIdMap.get(AMEU_WAREHOUSE), (k, v) -> String.valueOf(NumberUtil.parseDouble(data.get(5))));
                            }
                        }
                    } else if (key > lampChannelList1.size() + lampChannelSet2.size() + 1) {
                        Double totalValue = NumberUtil.parseDouble(value) + storeNum;
                        keyReplacementMap.put(head, String.valueOf(totalValue));
                    }
                }
            }
        }
        String resultSku = virtualProductDO.getVirtualSku();
        if (StrUtil.isNotBlank(virtualProductDO.getUpgradeId())) {
            UpgradeInfoInteriorVo upgradeInfoInteriorVo = selectUpgradeInfo(virtualProductDO.getUpgradeId());

            if (ObjectUtil.isNotEmpty(upgradeInfoInteriorVo)) {
                if (upgradeInfoInteriorVo.getOriginalSkuId().equals(virtualProductDO.getId())) {
                    resultSku = upgradeInfoInteriorVo.getUpgradeSku();
                }
            }
        }

        ReplenishmentManager manager = ReplenishmentManager.getInstance();
        for (Map.Entry<String, String> entry : keyReplacementMap.entrySet()) {
            if (!entry.getKey().equals("虚拟SKU")) {
                String warehouse = entry.getKey();
                String storeNum = entry.getValue();
                if (Double.parseDouble(storeNum) != 0) {
                    FactoryRemainInventoryDto factoryRemainInventoryDto = FactoryRemainInventoryDto.builder()
                            .virtualSku(virtualSku)
                            .warehouse(warehouse)
                            .enableUsingDate(manager.getCreateTime())
                            .storeNum(Double.valueOf(storeNum)).build();


                    String finalResultSku = resultSku;
                    Optional.ofNullable(replenishmentMapManager.getStockQuantityMap().get(finalResultSku))
                            .ifPresentOrElse(
                                    list -> list.stream()
                                            .filter(item -> item.getWarehouse().equals(factoryRemainInventoryDto.getWarehouse())
                                                    && item.getEnableUsingDate().equals(factoryRemainInventoryDto.getEnableUsingDate())
                                                    && item.getVirtualSku().equals(factoryRemainInventoryDto.getVirtualSku())
                                            )
                                            .findFirst()
                                            .ifPresentOrElse(
                                                    item -> item.setStoreNum(item.getStoreNum() + factoryRemainInventoryDto.getStoreNum()),
                                                    () -> list.add(factoryRemainInventoryDto)
                                            ),
                                    () -> {
                                        ArrayList<FactoryRemainInventoryDto> newList = new ArrayList<>();
                                        newList.add(factoryRemainInventoryDto);
                                        replenishmentMapManager.putStockQuantityMap(finalResultSku, newList);
                                    }
                            );
                }
            }
        }

        manager.incrementSuccessfulTimes();
    }

    /* 这里会一行行的返回头
     *
     * @param headMap
     * @param context
     */
    @Override
    public void invokeHeadMap(Map<Integer, String> headMap, AnalysisContext context) {
        ReplenishmentMapManager replenishmentMapManager = ReplenishmentMapManager.getInstance();
        replenishmentMapManager.resetStockQuantityHeadMap();
        //删除headMap中的空值
        headMap.entrySet().removeIf(entry -> entry.getValue() == null || entry.getValue().isEmpty());
        List<String> headMapList = new ArrayList<>(headMap.values());
        String sheetName = context.readSheetHolder().getSheetName();
        for (int i = 0; i < headMapList.size(); i++) {
            String headValue = headMapList.get(i);
            if (i == 0 && !headValue.equals("虚拟SKU")) {
                throw new RuntimeException(sheetName + "表头错误，请检查表头是否正确");
            }

            boolean isAMInventorySheet = sheetName.equals(RepImportSheetNameEnum.AM_STOCK_QUANTITY.getCode());

            if (isAMInventorySheet && (i == 5 || i == 6)) {
                String expectedHeader = switch (i) {
                    case 5 -> "正在接收中";
                    case 6 -> "预留库存（转运+处理中）";
                    default -> throw new IllegalStateException("Unexpected value: " + i);
                };
                if (!headValue.equals(expectedHeader)) {
                    throw new IllegalArgumentException(sheetName + "表头错误，请检查表头是否正确");
                }
            }

            //灯具
            boolean isLampInventorySheet = sheetName.equals(RepImportSheetNameEnum.LAMP_STOCK_QUANTITY.getCode());
            if (isLampInventorySheet && i == 5) {
                if (!headValue.equals("预留库存（转运+处理中）")) {
                    throw new IllegalArgumentException(sheetName + "表头错误，请检查表头是否正确");
                }
            }

            boolean useNameIdMap = ((isAMInventorySheet && ((i > 0 && i <= amChannelList.size()) || i > amChannelList.size() + 2))) ||
                    (isLampInventorySheet && ((i >= 1 && i <= 4) || i > 5));

            if (useNameIdMap) {
                if (!warehouseNameIdMap.containsKey(headValue)) {
                    throw new RuntimeException(sheetName + "表头错误，" + headValue + "不存在，请检查表头是否正确");
                }
                replenishmentMapManager.putStockQuantityHeadMap(i, warehouseNameIdMap.get(headValue));
            } else {
                replenishmentMapManager.putStockQuantityHeadMap(i, headValue);
            }

        }
    }
}
