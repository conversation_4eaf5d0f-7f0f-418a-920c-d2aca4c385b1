package com.inventory_server.model.visitLog.runnable;

import com.crafts_mirror.common.security.runnable.AbstractVisitLogRunnable;
import com.crafts_mirror.common.security.utils.SecurityUtils;
import com.crafts_mirror.utils.common.entity.LoginVo;
import com.crafts_mirror.utils.context.SecurityContextHolder;
import com.crafts_mirror.utils.dp.VisitLogForm;
import com.inventory_server.infrastructures.utils.InventoryUrlInfoUtils;
import lombok.extern.slf4j.Slf4j;

/**
 * @Description 访问记录线程
 * <AUTHOR>
 * @Date 2024/8/10 16:53
 **/
@Slf4j
public class VisitLogRunnable extends AbstractVisitLogRunnable {
    private final VisitLogForm visitLogForm;

    private final LoginVo loginVo;

    public VisitLogRunnable(VisitLogForm visitLogForm, Object handler) {
        super(handler);
        this.visitLogForm = visitLogForm;
        this.loginVo = SecurityUtils.getLoginUser();
    }

    @Override
    public void run() {
        try {
            refreshSecurityContextHolder(loginVo);
            // 根据url获取用户操作的日志类型以及服务类型
            InventoryUrlInfoUtils purchaseUrlInfo = new InventoryUrlInfoUtils(visitLogForm.getRequestUrl());
            visitLogForm.setVisitType(purchaseUrlInfo.getLogType().getDesc());
            visitLogForm.setServiceType(purchaseUrlInfo.getServiceLogType().getDesc());

            // 处理日志标题
            visitLogForm.setVisitInfoTitle(getVisitLogTitle(visitLogForm.getRequestUrl()));

            sendVisitLogRequest(visitLogForm);
        } finally {
            SecurityContextHolder.remove();
        }
    }
}
