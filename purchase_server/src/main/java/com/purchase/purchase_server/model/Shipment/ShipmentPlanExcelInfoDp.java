package com.purchase.purchase_server.model.Shipment;

import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.purchase.purchase_server.entity.excelObject.PoDeliveryScheduleExcel;
import com.purchase.purchase_server.entity.excelObject.ShipmentPlanExcel;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.stream.Stream;

import static com.crafts_mirror.utils.constant.DateFormatConstant.YYYY_MM_DD_DATE_FORMAT_SLASH;
import static com.crafts_mirror.utils.constant.DateFormatConstant.YYYY_M_D_DATE_FORMAT_SLASH;

/**
 * @Description 采购单交货计划导入-计划数据校验
 * <AUTHOR>
 * @Date 2024/11/11 10:56
 **/
public record ShipmentPlanExcelInfoDp(ShipmentPlanExcel data) {
    public ShipmentPlanExcelInfoDp(ShipmentPlanExcel data) {
        this.data = data;
    }


    public void checkNum(String qtyEta, String deliveryQuantity) {
        if (StrUtil.isNotBlank(qtyEta)) {
            if (!(NumberUtil.isInteger(qtyEta) && Integer.parseInt(qtyEta) > 0)) {
                throw new IllegalArgumentException("采购数量-请正确填写数字");
            }
        }
        if (StrUtil.isNotBlank(deliveryQuantity)) {
            if (!(NumberUtil.isInteger(deliveryQuantity) && Integer.parseInt(deliveryQuantity) > 0)) {
                throw new IllegalArgumentException("未安排数量-请正确填写数字");
            }
        }
    }
}
