package com.auth.authorization_server.repository;

import com.auth.authorization_server.mapper.RoleDataPermissionMapper;
import com.auth.authorization_server.model.dataObject.RoleDataPermissionDO;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import java.util.List;


/**
 * <AUTHOR>
 */
@Service
public class RoleDataPermissionRepositoryImpl extends ServiceImpl<RoleDataPermissionMapper, RoleDataPermissionDO> {

    /**
     * 通过角色ID删除角色和数据权限关联
     *
     * @param roleId 角色ID
     */
    public void deleteRoleDataByRoleId(String roleId){
        baseMapper.delete(Wrappers.<RoleDataPermissionDO>lambdaQuery()
                .eq(RoleDataPermissionDO::getRoleId, roleId));
    };

    /**
     * 通过多个角色ID查询数据权限信息
     *
     * @param roleIds 角色ID
     */
    public List<RoleDataPermissionDO> getRoleDataByRoleIds(List<String> roleIds){
        return baseMapper.selectList(Wrappers.<RoleDataPermissionDO>lambdaQuery()
                .in(RoleDataPermissionDO::getRoleId, roleIds));
    };
}
