package com.purchase.purchase_server.entity.dataObject;

import com.baomidou.mybatisplus.annotation.TableName;
import com.purchase.purchase_server.entity.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * @TableName cm_replenishment_rules
 */
@EqualsAndHashCode(callSuper = true)
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@Data
@TableName(value = "cm_replenishment_rules")
public class ReplenishmentRulesDO extends BaseEntity {
    /**
     * 虚拟sku
     */
    private String virtualSkuId;

    /**
     * 发货比例
     */
    private String shippingRatio;

    /**
     * 头程时间
     */
    private String headShippingDays;

    /**
     * 采购周期
     */
    private Integer purchaseCircle;

    /**
     * 发货周期
     */
    private Integer shippingCircle;

    /**
     * 安全天数
     */
    private Integer safeDays;

    /**
     * 可调配安全天数
     */
    private Integer changeableSafeDays;

    /**
     * 采购下单天数
     */
    private Integer purchaseDays;

    /**
     * 中转天数
     */
    private Integer transitDays;
}