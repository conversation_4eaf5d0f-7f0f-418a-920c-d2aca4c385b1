package com.purchase.purchase_server.model.targetSales.entity.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDate;

import static com.crafts_mirror.utils.constant.DateFormatConstant.YYYY_MM_DD_DATE_FORMAT_SLASH;

/**
 * <AUTHOR>
 * @Date 2024/11/14 17:17
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TargetDaySalesDto implements Serializable {
    private String id;

    @JsonFormat(pattern = YYYY_MM_DD_DATE_FORMAT_SLASH, timezone = "GMT+8")
    private LocalDate targetDate;

    private Double targetSales;
}
