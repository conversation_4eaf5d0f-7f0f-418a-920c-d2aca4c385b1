package com.product.product_server.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.crafts_mirror.utils.enums.ResponseCodeEnum;
import com.product.product_server.entity.dataObject.VirtualProductDO;
import com.product.product_server.entity.dataObject.VirtualUpgradeRelationDO;
import com.product.product_server.entity.dto.UpgradeInfoDto;
import com.product.product_server.exception.BusinessException;
import com.product.product_server.repository.VirtualProductRepositoryImpl;
import com.product.product_server.repository.VirtualUpgradeRelationRepositoryImpl;
import com.product.product_server.service.IProductSnapshotService;
import com.product.product_server.service.IUpgradeProductService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * @Description 虚拟sku商品业务编排层
 * <AUTHOR>
 * @Date 2023/12/12 19:46
 **/
@Service
@Slf4j
public class UpgradeProductServiceImpl implements IUpgradeProductService {

    @Resource
    private VirtualUpgradeRelationRepositoryImpl upgradeRelationRepository;

    @Resource
    private VirtualProductRepositoryImpl virtualProductRepository;

    @Resource
    private IProductSnapshotService productSnapshotService;

    @Override
    public UpgradeInfoDto getUpgradeInfo(String skuId, String upgradeId) {

        VirtualUpgradeRelationDO upgradeRelationDO = upgradeRelationRepository.getById(upgradeId);
        if (ObjectUtil.isEmpty(upgradeRelationDO)) {
            throw new BusinessException(ResponseCodeEnum.BAD_REQUEST, "升级款不存在");
        }

        boolean equals = upgradeRelationDO.getOriginalId().equals(skuId);
        String upgradeSkuId = equals ? upgradeRelationDO.getUpgradeId() : upgradeRelationDO.getOriginalId();

        VirtualProductDO upgradeDO = virtualProductRepository.getById(upgradeSkuId);
        if (ObjectUtil.isEmpty(upgradeDO)) {
            throw new BusinessException(ResponseCodeEnum.BAD_REQUEST, "升级款不存在");
        }
        return UpgradeInfoDto.builder().upgradeSkuId(upgradeDO.getId())
                .upgradeSku(upgradeDO.getVirtualSku()).build();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteUpgradeRelation(String upgradeId) {
        VirtualUpgradeRelationDO upgradeRelationDO = upgradeRelationRepository.getById(upgradeId);
        if (ObjectUtil.isEmpty(upgradeRelationDO)){
            return;
        }
        String originalId = upgradeRelationDO.getOriginalId();
        String upgradeSkuId = upgradeRelationDO.getUpgradeId();
        virtualProductRepository.updateVirtualProductUpgrade(originalId, null);
        virtualProductRepository.updateVirtualProductUpgrade(upgradeSkuId, null);

        productSnapshotService.saveProductSnapshot(originalId);
        productSnapshotService.saveProductSnapshot(upgradeSkuId);

        upgradeRelationRepository.deleteUpgradeRelation(CollectionUtil.toList(upgradeId));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteUpgradeRelationList(List<String> upgradeIds) {
        List<VirtualUpgradeRelationDO> upgradeRelationDOList = upgradeRelationRepository.listByIds(upgradeIds);
        if (CollectionUtil.isEmpty(upgradeRelationDOList)){
            return;
        }

        List<String> originalIds = upgradeRelationDOList.stream().map(VirtualUpgradeRelationDO::getOriginalId).toList();
        List<String> upgradeSkuIds = upgradeRelationDOList.stream().map(VirtualUpgradeRelationDO::getUpgradeId).toList();
        virtualProductRepository.updateVirtualProductUpgradeToNull(originalIds);
        virtualProductRepository.updateVirtualProductUpgradeToNull(upgradeSkuIds);

        productSnapshotService.saveProductSnapshotList(originalIds);
        productSnapshotService.saveProductSnapshotList(upgradeSkuIds);

        upgradeRelationRepository.deleteUpgradeRelation(upgradeRelationDOList.stream().map(VirtualUpgradeRelationDO::getId).toList());
    }
}
