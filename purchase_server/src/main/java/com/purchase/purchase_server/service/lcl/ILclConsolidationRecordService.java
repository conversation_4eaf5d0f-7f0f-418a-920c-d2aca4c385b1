package com.purchase.purchase_server.service.lcl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.purchase.purchase_server.entity.dataObject.Lcl.LclConsolidationRecordDO;
import com.purchase.purchase_server.entity.form.LclConsolidationForm;
import com.purchase.purchase_server.entity.form.LclConsolidationRecordForm;
import com.purchase.purchase_server.entity.vo.DeliveryRecordPurchaseListVo;
import com.purchase.purchase_server.entity.vo.Lcl.LclConsolidationRecordVO;
import com.purchase.purchase_server.entity.vo.UserInteriorVO;
import com.purchase.purchase_server.enums.lcl.LclStatusEnum;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【cm_lcl_consolidation_record(拼柜装柜记录表)】的数据库操作Service
* @createDate 2024-12-04 16:47:06
*/
public interface ILclConsolidationRecordService {

    LclConsolidationRecordVO createLclConsolidation(LclConsolidationForm form);

    /**
     * 数据整理
     */
    void dataClean(LclConsolidationForm form, LclConsolidationRecordDO lclRecordDO);

    /**
     * 装柜记录查询
     */
    IPage<LclConsolidationRecordVO> pageList(LclConsolidationRecordForm form);

    List<UserInteriorVO> getUserList();

    void exportDeliveryPlan(LclConsolidationForm form);

    void updateRecordStatus(String lclRecordId, LclStatusEnum lclStatus);

    DeliveryRecordPurchaseListVo downloadExport(LclConsolidationForm form);

}
