package com.inventory_server.applications.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.time.LocalDate;

@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class InventoryShipRedundantInfoDto {
    private String inventoryInfoId;
    private String shipmentCode;
    private String warehouse;
    private LocalDate enableUsingDate;
    private Integer redundantNum;
    private String onShippingWatchBoardId;
    private LocalDate startShippingDate;
}
