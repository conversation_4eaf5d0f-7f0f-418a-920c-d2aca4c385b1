package com.inventory_server.model.redundancy.entity.dto;

import com.inventory_server.applications.dto.FactoryRemainInventoryDto;
import com.inventory_server.model.redundancy.entity.dos.InventoryForeignStoreDO;
import com.inventory_server.model.redundancy.entity.dos.InventorySaleRulesDO;
import lombok.Builder;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2024/10/23 14:35
 **/
@Data
@Builder
public class NormalDeliveryInventorySaveDto {
    private String inventoryId;
    private Map<String, Integer> headShippingDays;

    private List<FactoryRemainInventoryDto> shippingInventoryList;

    private String virtualSku;

    private List<FactoryFinishedInventoryDto> factoryFinishedInventoryList;

    private List<InventoryForeignStoreDO> inventoryForeignStoreList;

    private InventorySaleRulesDO rulesDO;

    /**
     * 优先发货数据
     */
    private List<FactoryRemainInventoryDto> priorDeliveryList;
}
