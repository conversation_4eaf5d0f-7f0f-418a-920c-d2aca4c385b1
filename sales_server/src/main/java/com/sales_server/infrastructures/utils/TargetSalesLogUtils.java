package com.sales_server.infrastructures.utils;

import com.sales_server.model.targetSales.entity.dos.TargetSalesInfoDO;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2025/3/12 9:52
 **/
public class TargetSalesLogUtils {

    public static String targetSalesLog(List<TargetSalesInfoDO> list, String prefix, String suffix) {
        // 按年份分组，并收集去重排序后的月份
        Map<Integer, List<Integer>> yearToMonths = list.stream()
                .map(TargetSalesInfoDO::getTargetDate)
                .collect(Collectors.groupingBy(
                        LocalDate::getYear,
                        Collectors.mapping(
                                LocalDate::getMonthValue,
                                Collectors.collectingAndThen(
                                        Collectors.toSet(),
                                        set -> set.stream().sorted().collect(Collectors.toList()))
                        )
                ));

        // 按年份升序排序
        List<Integer> sortedYears = yearToMonths.keySet().stream().sorted().toList();

        // 生成各年份部分字符串
        List<String> yearParts = new ArrayList<>();
        for (int year : sortedYears) {
            String monthsStr = yearToMonths.get(year).stream()
                    .map(String::valueOf)
                    .collect(Collectors.joining("、"));
            yearParts.add(year + "年" + monthsStr);
        }

        // 拼接最终结果
        return prefix + String.join("、", yearParts) + "月" + suffix;
    }
}
