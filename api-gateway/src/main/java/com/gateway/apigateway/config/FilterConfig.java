package com.gateway.apigateway.config;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.gateway.apigateway.operator.RequestBodyRewrite;
import org.springframework.cloud.gateway.route.RouteLocator;
import org.springframework.cloud.gateway.route.builder.RouteLocatorBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * @author: 雪竹
 * @description: 路由过滤器
 * @dateTime: 2023/11/24 14:59
 **/
@Configuration
public class FilterConfig {

    @Bean
    public RouteLocator routes(RouteLocatorBuilder builder, ObjectMapper objectMapper) {
        return builder
                .routes()
                .route("path_route_change",
                        r -> r.path("/productServer/demo/test")
                                .filters(f -> f
                                        .modifyRequestBody(String.class, String.class, new RequestBodyRewrite(objectMapper))
//                                        .modifyResponseBody(String.class, String.class, new ResponseBodyRewrite(objectMapper))
                                        .stripPrefix(1)
                                )
                                .uri("lb://product-server"))
                .route("file-system",
                        r -> r.path("/file/**")
                                .filters(f -> f.stripPrefix(1))
                                .uri("lb://file-system"))
                .build();
    }
}
