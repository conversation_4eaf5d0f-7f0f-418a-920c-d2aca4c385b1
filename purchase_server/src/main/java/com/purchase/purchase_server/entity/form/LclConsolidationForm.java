package com.purchase.purchase_server.entity.form;

import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @Description 发货装柜
 * @date 2024/1/18
 **/
@EqualsAndHashCode(callSuper = false)
@Data
@Builder
public class LclConsolidationForm implements Serializable {

    @Serial
    private static final long serialVersionUID = 8443927341525881111L;

    private String id;
    /**
     * 发货计划记录id
     */
    private String shippingRecordId;
    /**
     * 装柜记录id
     */
    private String lclRecordId;
    /**
     * 采购日开始时间
     */
    private String shippingStartDate;
    /**
     * 采购日结束时间
     */
    private String shippingEndDate;
    /**
     * 最小体积阈值(立方米)
     */
    private String minVolume;

    private boolean isLimit;

    /**
     * 是否整理
     * @see com.purchase.purchase_server.enums.lcl.LclConsolidationEnum
     */
    private String isConsolidation;
    /**
     * 当前采购日时间
     */
    private String shippingDate;
}
