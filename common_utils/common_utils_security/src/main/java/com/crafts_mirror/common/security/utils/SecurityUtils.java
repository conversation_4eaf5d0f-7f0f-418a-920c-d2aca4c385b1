package com.crafts_mirror.common.security.utils;


import cn.hutool.core.util.ObjectUtil;
import com.crafts_mirror.utils.common.entity.LoginVo;
import com.crafts_mirror.utils.constant.SecurityConstants;
import com.crafts_mirror.utils.context.SecurityContextHolder;


/**
 * 权限获取工具类
 *
 * <AUTHOR>
 */
public class SecurityUtils {
    /**
     * 获取用户ID
     */
    public static String getUserId() {
        return SecurityContextHolder.getUserId();
    }

    /**
     * 获取用户名称
     */
    public static String getUsername() {
        return SecurityContextHolder.getUserName();
    }

    /**
     * 获取登录用户信息
     */
    public static LoginVo getLoginUser() {
        return SecurityContextHolder.get(SecurityConstants.LOGIN_USER, LoginVo.class);
    }


    /**
     * 获取请求token
     */
    public static String getToken() {
        LoginVo loginUser = getLoginUser();
        if (ObjectUtil.isNotEmpty(loginUser)) {
            return loginUser.getToken();
        }
        return null;
    }


    /**
     * 是否为管理员
     *
     * @param userId 用户ID
     * @return 结果
     */
    public static boolean isAdmin(String userId) {
        return userId != null && userId.equals("1");
    }
}
