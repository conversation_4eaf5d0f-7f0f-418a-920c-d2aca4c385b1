package com.purchase.purchase_server.service.shipping.impl;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.purchase.purchase_server.config.WarehouseConstants;
import com.purchase.purchase_server.entity.dataObject.VirtualProductDO;
import com.purchase.purchase_server.entity.dto.FactoryFinishedInventoryDto;
import com.purchase.purchase_server.entity.dto.FactoryRemainInventoryDto;
import com.purchase.purchase_server.entity.dto.SenboWarehouseDto;
import com.purchase.purchase_server.entity.dto.delivery.MockTableCalDto;
import com.purchase.purchase_server.entity.dto.delivery.ShippingProjectBaseParamDto;
import com.purchase.purchase_server.entity.dto.delivery.TrialCalReplenishmentDto;
import com.purchase.purchase_server.enums.DeliveryTypeEnum;
import com.purchase.purchase_server.model.purchase.PurchaseDayNeedNumDp;
import com.purchase.purchase_server.model.purchase.ReplenishDeliveryRangeAndNumDp;
import com.purchase.purchase_server.service.AbstractShippingCalculationService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static cn.hutool.core.date.DateField.DAY_OF_YEAR;
import static com.crafts_mirror.utils.constant.DateFormatConstant.YYYY_MM_DD_DATE_FORMAT_SLASH;
import static java.math.RoundingMode.*;

/**
 * @Description 不重复发货的发货试算算法
 * <AUTHOR>
 * @Date 2024/12/23 13:32
 **/
@Service("noRepeatableCalculationServiceImpl")
@Slf4j
public class NoRepeatableCalculationServiceImpl extends AbstractShippingCalculationService {

    @Resource
    private WarehouseConstants warehouseConstants;

    @Override
    public TrialCalReplenishmentDto trialShippingCalculation(
            DateTime shippingStartDate, int redundantDate, List<FactoryRemainInventoryDto> factoryRemainInventoryList,
            List<FactoryRemainInventoryDto> importFactoryRemainInventoryList, VirtualProductDO virtualProduct, int maxDeliveryDayGap,
            Map<String, Double> targetSalesMap, DateTime projectCreateDate, List<FactoryFinishedInventoryDto> finishedInventoryList,
            ShippingProjectBaseParamDto shippingRules, List<String> sortedWarehouseList, String productName, List<SenboWarehouseDto> warehouseList) {
        finishedInventoryList.sort(Comparator.comparing(FactoryFinishedInventoryDto::getFactoryFinishedDate));
        int mockTableRange = (int) (DateUtil.betweenDay(shippingStartDate, projectCreateDate, false) + maxDeliveryDayGap);
        // 获取发货试算规则
        Map<String, Double> saleRatioMap = shippingRules.getShippingRatio();
        Map<String, Integer> headShippingDateMap = shippingRules.getHeadShippingDays();
        int transitDays = shippingRules.getTransitDays();
        int safeDate = shippingRules.getSafeDays();
        int shippingFrequency = shippingRules.getShippingFrequency();
        int containLoader = shippingRules.getContainLoader();

        // 获取加急仓库
        SenboWarehouseDto urgentWarehouseInfo = getMinHeadShipDateWarehouseInfo(saleRatioMap, warehouseList);
        // 获取洛杉矶仓库信息
        SenboWarehouseDto laWarehouse = warehouseList.stream().filter(f -> f.getSenboWarehouseId().equals("1")).findFirst().get();

        // 获取加急、未装满、配件等发货数据
        List<FactoryRemainInventoryDto> priorDeliveryFactoryList = getPriorDeliveryFactoryList(finishedInventoryList,
                productName, shippingStartDate, urgentWarehouseInfo, laWarehouse, transitDays);
        var maybeShippingfactoryList = finishedInventoryList.stream().map(ObjectUtil::clone).toList();

        int minShippingDate = headShippingDateMap.entrySet().stream()
                .filter(h -> saleRatioMap.getOrDefault(h.getKey(), 0D) > 0)
                .min(Map.Entry.comparingByValue())
                .orElseThrow(() -> new NullPointerException("头程时间为空"))
                .getValue();

        // 发货试算开始，先计算各仓剩余库存以及每日消耗情况表格
        List<FactoryRemainInventoryDto> firstInvenotryList = new ArrayList<>(factoryRemainInventoryList);
        firstInvenotryList.addAll(importFactoryRemainInventoryList);
        firstInvenotryList.addAll(priorDeliveryFactoryList);

        Map<String, Map<String, Double>> everyDayWarehouseSaleMap = new TreeMap<>();
        var everydayRemainMap = calRemainInventoryMap(projectCreateDate, firstInvenotryList, mockTableRange,
                minShippingDate, targetSalesMap, saleRatioMap, everyDayWarehouseSaleMap, sortedWarehouseList);

        DateTime fastShipDate = maybeShippingfactoryList.stream()
                .filter(f -> Boolean.FALSE.equals(f.getNeedToPriorDelivery()))
                .min(Comparator.comparing(FactoryFinishedInventoryDto::getFactoryFinishedDate))
                .map(f -> {
                    DateTime offset = DateUtil.offset(f.getFactoryFinishedDate(), DAY_OF_YEAR, transitDays);
                    return offset.compareTo(shippingStartDate) >= 0 ? offset : shippingStartDate;
                })
                .orElse(Objects.requireNonNull(shippingStartDate, "请填写发货开始日期"));
        DateTime earliestArrivingDate = fastShipDate.offsetNew(DAY_OF_YEAR, minShippingDate);

        Map<String, DateTime> fastestArrivingDateMap = new TreeMap<>();
        for (var entry : saleRatioMap.entrySet()) {
            if (entry.getValue() <= 0) {
                continue;
            }

            Integer shippingDay = headShippingDateMap.get(entry.getKey());
            fastestArrivingDateMap.put(entry.getKey(), fastShipDate.offsetNew(DAY_OF_YEAR, shippingDay));
        }

        DateTime maxSaleDate = targetSalesMap.keySet().stream()
                .map(DateUtil::parse)
                .max(Comparator.comparing(k -> k))
                .orElse(DateUtil.endOfMonth(shippingStartDate));

        Map<String, DateTime> lastTimeArrivalMap = new HashMap<>();
        List<FactoryRemainInventoryDto> deliveryList = new ArrayList<>();
        for (DateTime calDate = new DateTime(earliestArrivingDate);
             calDate.compareTo(shippingStartDate.offsetNew(DAY_OF_YEAR, maxDeliveryDayGap)) <= 0;
             calDate.offset(DAY_OF_YEAR, 1)) {

            // 如果发货当天没有货能发出去，则直接到下一天
            double sum = maybeShippingfactoryList.stream()
                    .filter(dto -> {
                        DateTime enableShipDate = DateUtil.offset(dto.getFactoryFinishedDate(), DAY_OF_YEAR, transitDays);
                        DateTime startDate = calDate.offsetNew(DAY_OF_YEAR, -minShippingDate);
                        return enableShipDate.compareTo(startDate) <= 0 && dto.getShippingNum() > 0;
                    })
                    .filter(f -> Boolean.FALSE.equals(f.getNeedToPriorDelivery()))
                    .mapToDouble(FactoryFinishedInventoryDto::getShippingNum)
                    .sum();
            if (roundUpToThreeDecimal(sum) <= 0) {
                continue;
            }

            String calDateStr = calDate.toString(YYYY_MM_DD_DATE_FORMAT_SLASH);
            // 计算当天总仓的安全库存
            BigDecimal totalSafeInventory = calTotalSafeInventory(safeDate, calDate, targetSalesMap, redundantDate);
            // 当天剩余库存
            Map<String, Double> warehouseRemainMap = everydayRemainMap.getOrDefault(calDateStr, new HashMap<>());
            BigDecimal totalInventoryNum = BigDecimal.valueOf(warehouseRemainMap.values().stream().mapToDouble(v -> v).sum());

            if (totalSafeInventory.compareTo(BigDecimal.ZERO) <= 0 || totalInventoryNum.compareTo(totalSafeInventory) > 0) {
                continue;
            }

            Set<String> everArrivedWarehouseSet = sortedWarehouseList.stream()
                    .filter(warehouse -> saleRatioMap.get(warehouse) > 0 &&
                            calDate.offsetNew(DAY_OF_YEAR, -headShippingDateMap.get(warehouse)).compareTo(earliestArrivingDate) >= 0)
                    .collect(Collectors.toSet());

            for (String warehouse : sortedWarehouseList) {
                int headShippingDate = headShippingDateMap.get(warehouse);
                // 如果发货当天没有货能发出去，则直接到下一天
                List<FactoryFinishedInventoryDto> canDeliveryFactoryList = maybeShippingfactoryList.stream()
                        .filter(dto -> {
                            DateTime enableShipDate = DateUtil.offset(dto.getFactoryFinishedDate(), DAY_OF_YEAR, transitDays);
                            DateTime startDate = calDate.offsetNew(DAY_OF_YEAR, -headShippingDate);
                            return enableShipDate.compareTo(startDate) <= 0 && dto.getShippingNum() > 0;
                        })
                        .filter(f -> Boolean.FALSE.equals(f.getNeedToPriorDelivery()))
                        .toList();
                sum = canDeliveryFactoryList.stream()
                        .mapToDouble(FactoryFinishedInventoryDto::getShippingNum)
                        .sum();
                // 每天的工厂能发货数量是限定好的，某各仓消耗一部分后，剩余量会下降，当天若已经没有了可发货数量，则直接跳过
                if (roundUpToThreeDecimal(sum) <= 0) {
                    continue;
                }

                BigDecimal factorySum = BigDecimal.valueOf(sum).setScale(3, HALF_UP);
                DateTime shippingDate = calDate.offsetNew(DAY_OF_YEAR, -headShippingDate);
                DateTime shippingEndDate = shippingStartDate.offsetNew(DAY_OF_YEAR, maxDeliveryDayGap);
                if (shippingDate.compareTo(shippingStartDate) < 0 || shippingDate.compareTo(shippingEndDate) > 0) {
                    // 如果发货时间发货都不能在可发货时间区间内的话，则直接跳过该仓
                    continue;
                }

                // 获取各仓库的到仓时间
                DateTime lastArrivingDate = new DateTime(calDate);
                // 计算最早到货日期当天各仓的安全库存
                BigDecimal warehouseSafe = calWarehouseSafeInventory(safeDate, lastArrivingDate, targetSalesMap,
                        saleRatioMap, warehouse, minShippingDate, importFactoryRemainInventoryList, headShippingDateMap,
                        earliestArrivingDate, everydayRemainMap, everArrivedWarehouseSet, redundantDate, virtualProduct.getVirtualSku());
                if (warehouseSafe.compareTo(BigDecimal.ZERO) <= 0) {
                    continue;
                }

                List<FactoryRemainInventoryDto> tempList = new ArrayList<>(firstInvenotryList);
                tempList = tempList.stream()
                        .filter(f -> !f.getWarehouse().equals(warehouse) || f.getEnableUsingDate().after(calDate))
                        .collect(Collectors.toList());
                warehouseRemainMap = everydayRemainMap.getOrDefault(calDateStr, new HashMap<>());
                for (var entry : warehouseRemainMap.entrySet()) {
                    if (entry.getKey().contains("start")) {
                        continue;
                    }
                    String tempWarehouse = entry.getKey();
                    DateTime arrivingDate = fastestArrivingDateMap.get(tempWarehouse);
                    if (arrivingDate != null && !tempWarehouse.equals(warehouse)) {
                        // 若不是待计算的仓库，且可以到货，将剩余库存补满，模拟一直有货的情况
                        double remainInventory = Integer.MAX_VALUE;
                        tempList.add(FactoryRemainInventoryDto.builder()
                                .storeNum(remainInventory)
                                .warehouse(tempWarehouse)
                                .enableUsingDate(arrivingDate)
                                .build());
                    }

                    if (tempWarehouse.equals(warehouse)) {
                        tempList.add(FactoryRemainInventoryDto.builder()
                                .storeNum(warehouseRemainMap.getOrDefault(warehouse, 0D))
                                .warehouse(tempWarehouse)
                                .enableUsingDate(calDate)
                                .build());
                    }
                }

                everydayRemainMap = calRemainInventoryMap(projectCreateDate, tempList, mockTableRange, minShippingDate,
                        targetSalesMap, saleRatioMap, new HashMap<>(), sortedWarehouseList);
                warehouseRemainMap = everydayRemainMap.getOrDefault(calDate.toString(YYYY_MM_DD_DATE_FORMAT_SLASH), new HashMap<>());
                // 获取剩余库存、在途库存并计算总库存
                BigDecimal warehouseRemain = BigDecimal.valueOf(warehouseRemainMap.getOrDefault(warehouse, 0D)).setScale(3, HALF_UP);

                StringBuilder logInfoBuilder = new StringBuilder();
                logInfoBuilder.append("仓库：").append(warehouse).append(" 安全库存为：").append(warehouseSafe);
                logInfoBuilder.append("；剩余库存为：").append(warehouseRemain).append("；");

                int times = 0;
                String deliveryRange = "";

                PurchaseDayNeedNumDp dayNeedNumDp = new PurchaseDayNeedNumDp();
                int tempShippingFrequency = shippingFrequency;
                while (warehouseRemain.compareTo(warehouseSafe) <= 0) {
                    sum = canDeliveryFactoryList.stream()
                            .mapToDouble(FactoryFinishedInventoryDto::getShippingNum)
                            .sum();
                    if (roundUpToThreeDecimal(sum) <= 0) {
                        break;
                    }
                    factorySum = BigDecimal.valueOf(sum);
                    // 计算发货量区间起点
                    DateTime lastTimeArrivedDate = lastTimeArrivalMap.getOrDefault(warehouse, new DateTime(calDate)).offsetNew(DAY_OF_YEAR, -shippingFrequency);
                    lastTimeArrivedDate = lastTimeArrivedDate.isBefore(calDate) ? new DateTime(calDate) : lastTimeArrivedDate;
                    warehouseRemainMap = everydayRemainMap.getOrDefault(lastTimeArrivedDate.toString(YYYY_MM_DD_DATE_FORMAT_SLASH), new HashMap<>());
                    DateTime deliveryNumStartDate = commonConsumptionService.calDeliveryNumStartDate(
                            lastTimeArrivedDate, importFactoryRemainInventoryList, warehouse, fastestArrivingDateMap,
                            targetSalesMap, maxSaleDate, warehouseRemainMap, shippingStartDate, shippingRules,
                            sortedWarehouseList, priorDeliveryFactoryList);
                    lastTimeArrivalMap.put(warehouse, deliveryNumStartDate.offsetNew(DAY_OF_YEAR, tempShippingFrequency));
                    if (deliveryNumStartDate.isAfter(maxSaleDate)) {
                        break;
                    }

                    // 计算出当次发货需要多少量
                    PurchaseDayNeedNumDp tempNeedNumDp = commonConsumptionService.calNeedDeliveryNum(everydayRemainMap,
                            targetSalesMap, tempShippingFrequency, saleRatioMap, warehouse, deliveryNumStartDate,
                            factorySum.doubleValue(), fastestArrivingDateMap, importFactoryRemainInventoryList, priorDeliveryFactoryList);
                    if (tempNeedNumDp.getTotalNeedNum() > factorySum.doubleValue()) {
                        tempNeedNumDp.setTotalNeedNum(factorySum.doubleValue());
                    }
                    factorySum = minusFactoryFinishedList(canDeliveryFactoryList, BigDecimal.valueOf(tempNeedNumDp.getTotalNeedNum()));

                    logInfoBuilder.append("第").append(++times).append("次发货；").append("发货周期是").append(tempShippingFrequency);
                    logInfoBuilder.append("；").append("发货前剩余库存是：").append(tempNeedNumDp.getShippingNum())
                            .append("；发货量是：").append(tempNeedNumDp.getTotalNeedNum());
                    tempNeedNumDp.setTotalNeedNum(tempNeedNumDp.getTotalNeedNum());

                    // 计算发货量区间
                    DateTime endDate = tempNeedNumDp.getEndDate();
                    if (endDate != null) {
                        if (endDate.isAfter(maxSaleDate)) {
                            endDate = maxSaleDate.offsetNew(DAY_OF_YEAR, 1);
                        } else if (endDate.isBeforeOrEquals(deliveryNumStartDate)) {
                            endDate = new DateTime(deliveryNumStartDate).offsetNew(DAY_OF_YEAR, 1);
                        }
                        String range = deliveryNumStartDate.toString(YYYY_MM_DD_DATE_FORMAT_SLASH) + "~" +
                                endDate.offsetNew(DAY_OF_YEAR, -1).toString(YYYY_MM_DD_DATE_FORMAT_SLASH);

                        deliveryRange = ReplenishDeliveryRangeAndNumDp.addRange(deliveryRange, range);
                        logInfoBuilder.append("；发货量区间是").append(deliveryRange).append("；\n");
                        lastTimeArrivalMap.put(warehouse, endDate);
                    }

                    dayNeedNumDp.addPurchaseDayNeedNumDp(tempNeedNumDp);
                    warehouseRemain = warehouseRemain.add(BigDecimal.valueOf(tempNeedNumDp.getTotalNeedNum()));

                    tempShippingFrequency = 1;

                    if (tempNeedNumDp.getTotalNeedNum() > 0) {
                        tempList.add(FactoryRemainInventoryDto.builder()
                                .enableUsingDate(new DateTime(calDate))
                                .storeNum(tempNeedNumDp.getTotalNeedNum())
                                .deliveryDateRange(deliveryRange)
                                .warehouse(warehouse)
                                .build());

                        everydayRemainMap = calRemainInventoryMap(projectCreateDate, tempList, mockTableRange,
                                minShippingDate, targetSalesMap, saleRatioMap, new HashMap<>(), sortedWarehouseList);
                    }
                    if (endDate != null && endDate.isAfter(maxSaleDate)) {
                        break;
                    }
                }

                // 发货量凑整数
                BigDecimal ceilinged = ceilingOrFloor(BigDecimal.valueOf(dayNeedNumDp.getTotalNeedNum()), canDeliveryFactoryList);
                dayNeedNumDp.setTotalNeedNum(ceilinged.doubleValue());

                // 发货量凑整箱
                BigDecimal whole = assembleWholePackage(warehouseRemain, warehouseSafe, containLoader, canDeliveryFactoryList,
                        factorySum, BigDecimal.valueOf(dayNeedNumDp.getTotalNeedNum()));
                dayNeedNumDp.setTotalNeedNum(dayNeedNumDp.getTotalNeedNum() + whole.doubleValue());

                logInfoBuilder.append("；凑完整箱后发货量是：").append(dayNeedNumDp.getTotalNeedNum());

                // 计算出当天工厂还有多少剩余量可以发出去
                BigDecimal totalNeedNum = BigDecimal.valueOf(dayNeedNumDp.getTotalNeedNum());
                if (totalNeedNum.compareTo(BigDecimal.ZERO) > 0) {
                    FactoryRemainInventoryDto tempRemainDto = FactoryRemainInventoryDto.builder()
                            .enableUsingDate(new DateTime(calDate))
                            .storeNum(totalNeedNum.doubleValue())
                            .deliveryDateRange(deliveryRange)
                            .warehouse(warehouse)
                            .build();

                    deliveryList.add(tempRemainDto);
                    firstInvenotryList.add(tempRemainDto);

                    logInfoBuilder.append("；该仓当天发货总量为：").append(totalNeedNum);
                    if(!environment.equals("prod")) {
                        deliveryPurchaseRepository.insertLogInfo(logInfoBuilder.toString(), virtualProduct.getVirtualSku(), warehouse, calDateStr, projectCreateDate);
                    }

                }
                everydayRemainMap = calRemainInventoryMap(projectCreateDate, firstInvenotryList, mockTableRange,
                        minShippingDate, targetSalesMap, saleRatioMap, new HashMap<>(), sortedWarehouseList);
            }
        }

        List<FactoryRemainInventoryDto> remainInventoryListForCal = new ArrayList<>(importFactoryRemainInventoryList);
        MockTableCalDto mockTableCalDto = new MockTableCalDto(deliveryList, mockTableRange, factoryRemainInventoryList,
                headShippingDateMap, projectCreateDate, remainInventoryListForCal, importFactoryRemainInventoryList,
                priorDeliveryFactoryList, minShippingDate, targetSalesMap, saleRatioMap, sortedWarehouseList);
        TrialCalReplenishmentDto trialCalReplenishmentDto = commonConsumptionService.calDeliveryResults(mockTableCalDto);
        trialCalReplenishmentDto.setPriorDeliveryList(priorDeliveryFactoryList);
        return trialCalReplenishmentDto;
    }

    private SenboWarehouseDto getMinHeadShipDateWarehouseInfo(Map<String, Double> saleRatioMap, List<SenboWarehouseDto> warehouseList) {
        Set<String> keySet = saleRatioMap.entrySet().stream()
                .filter(f -> f.getValue() > 0)
                .map(Map.Entry::getKey)
                .collect(Collectors.toSet());

        Set<String> existedWarehouse = warehouseList.stream()
                .filter(f -> keySet.contains(f.getSenboWarehouseId()))
                .map(SenboWarehouseDto::getSenboWarehouse)
                .collect(Collectors.toSet());
        List<String> name = warehouseConstants.getName();
        Set<String> nameSet = new HashSet<>(name);

        if (existedWarehouse.stream().anyMatch(nameSet::contains)) {
            // 当有比例的仓库中有一个仓库是美国海外仓时，加急头程仓库选择洛杉矶
            return warehouseList.stream().filter(f -> f.getSenboWarehouseId().equals("1")).findFirst().get();
        } else {
            // 当有比例的仓库中所有仓库都是非美国海外仓时，加急头程时间选择最小加急头程仓库
            return warehouseList.stream()
                    .filter(f -> keySet.contains(f.getSenboWarehouseId()))
                    .min(Comparator.comparing(SenboWarehouseDto::getUrgentHeadShipDate))
                    .get();
        }
    }

    /**
     * 计算需要优先发货的计划，这些计划发出去以后当在途库存，代入到发货的计算中去
     * @param factoryFinishedInventoryList 发货计划
     * @param productName 产品名称
     * @param startCalDate 发货区间开始时间
     * @param urgentSenboWarehouse 加急仓库
     * @param laWarehouse fba官方仓库
     * @return 优先发货的库存
     */
    private List<FactoryRemainInventoryDto> getPriorDeliveryFactoryList(
            List<FactoryFinishedInventoryDto> factoryFinishedInventoryList, String productName, Date startCalDate,
            SenboWarehouseDto urgentSenboWarehouse, SenboWarehouseDto laWarehouse, int transitDays) {
        List<FactoryRemainInventoryDto> factoryRemainInventoryList = new ArrayList<>();
        for (var factory : factoryFinishedInventoryList) {
            String remark = factory.getRemark();
            var builder = FactoryRemainInventoryDto.builder()
                    .storeNum(factory.getShippingNum())
                    .factoryHashCode(factory.hashCode());
            if (StrUtil.isNotBlank(remark)) {
                if (remark.contains("停发")) {
                    factory.setDeliveryType(DeliveryTypeEnum.STOP_DELIVERY);
                    factory.setNeedToPriorDelivery(true);
                } else if (remark.contains("加急")) {
                    Date factoryFinishedDate = factory.getFactoryFinishedDate();
                    Date deliveryDate = factoryFinishedDate.compareTo(startCalDate) < 0 ? startCalDate : factoryFinishedDate;
                    builder.warehouse(urgentSenboWarehouse.getSenboWarehouseId())
                            .enableUsingDate(DateUtil.offset(deliveryDate, DAY_OF_YEAR, urgentSenboWarehouse.getUrgentHeadShipDate()))
                            .startShippingTime(deliveryDate);
                    factory.setNeedToPriorDelivery(true);
                    factory.setDeliveryType(DeliveryTypeEnum.URGENT);
                    factoryRemainInventoryList.add(builder.build());
                } else if (productName.contains("配件")) {
                    Date factoryFinishedDate = DateUtil.offset(factory.getFactoryFinishedDate(), DAY_OF_YEAR, transitDays);
                    Date deliveryDate = factoryFinishedDate.compareTo(startCalDate) < 0 ? startCalDate : factoryFinishedDate;
                    // 配件全发往洛杉矶
                    builder.warehouse("1")
                            .startShippingTime(deliveryDate)
                            .enableUsingDate(DateUtil.offset(deliveryDate, DAY_OF_YEAR, laWarehouse.getHeadShippingDate()));
                    factory.setNeedToPriorDelivery(true);
                    factory.setDeliveryType(DeliveryTypeEnum.ACCESSORY);
                    factoryRemainInventoryList.add(builder.build());
                } else {
                    factory.setNeedToPriorDelivery(false);
                    factory.setDeliveryType(DeliveryTypeEnum.NORMAL);
                }
            } else {
                if (productName.contains("配件")) {
                    Date factoryFinishedDate = DateUtil.offset(factory.getFactoryFinishedDate(), DAY_OF_YEAR, transitDays);
                    Date deliveryDate = factoryFinishedDate.compareTo(startCalDate) < 0 ? startCalDate : factoryFinishedDate;
                    // 配件全发往洛杉矶
                    builder.warehouse("1")
                            .startShippingTime(deliveryDate)
                            .enableUsingDate(DateUtil.offset(deliveryDate, DAY_OF_YEAR, laWarehouse.getHeadShippingDate()));
                    factory.setNeedToPriorDelivery(true);
                    factory.setDeliveryType(DeliveryTypeEnum.ACCESSORY);
                    factoryRemainInventoryList.add(builder.build());
                } else {
                    factory.setNeedToPriorDelivery(false);
                    factory.setDeliveryType(DeliveryTypeEnum.NORMAL);
                }
            }
        }
        return factoryRemainInventoryList;
    }

    private BigDecimal assembleWholePackage(BigDecimal remain, BigDecimal warehouseSafe, int containerLoad,
                                            List<FactoryFinishedInventoryDto> canDeliveryFactoryList,
                                            BigDecimal factorySum, BigDecimal needNum) {
        BigDecimal[] bigDecimals = needNum.divideAndRemainder(BigDecimal.valueOf(containerLoad));
        BigDecimal reminder = bigDecimals[1];
        BigDecimal packageRemain = remain.subtract(reminder);

        BigDecimal lackNum = BigDecimal.valueOf(containerLoad).subtract(reminder);
        if (reminder.compareTo(BigDecimal.ZERO) == 0 || lackNum.compareTo(factorySum) > 0 || packageRemain.compareTo(warehouseSafe) == 0) {
            return BigDecimal.ZERO;
        } else if (packageRemain.compareTo(warehouseSafe) <= 0) {
            BigDecimal tempSub = BigDecimal.valueOf(lackNum.doubleValue());
            for (var dto : canDeliveryFactoryList) {
                BigDecimal shippingNum = BigDecimal.valueOf(dto.getShippingNum());
                if (tempSub.compareTo(shippingNum) <= 0) {
                    dto.setShippingNum(shippingNum.subtract(tempSub).setScale(3, HALF_UP).doubleValue());
                    break;
                } else {
                    dto.setShippingNum(0D);
                    tempSub = tempSub.subtract(shippingNum);
                }
            }

            return lackNum;
        } else {
            FactoryFinishedInventoryDto first = canDeliveryFactoryList.getFirst();
            first.setShippingNum(first.getShippingNum() + reminder.doubleValue());
            return BigDecimal.ZERO.subtract(reminder);
        }
    }

    private BigDecimal minusFactoryFinishedList(List<FactoryFinishedInventoryDto> factoryFinishedInventoryList, BigDecimal needNum) {
        BigDecimal temp = BigDecimal.valueOf(needNum.doubleValue());
        for (var dto : factoryFinishedInventoryList) {
            BigDecimal shippingNum = BigDecimal.valueOf(dto.getShippingNum());
            if (temp.compareTo(shippingNum) <= 0) {
                dto.setShippingNum(shippingNum.subtract(temp).setScale(3, HALF_UP).doubleValue());
                break;
            } else {
                temp = temp.subtract(shippingNum);
                dto.setShippingNum(0D);
            }
        }
        return BigDecimal.valueOf(factoryFinishedInventoryList.stream().mapToDouble(FactoryFinishedInventoryDto::getShippingNum).sum()).setScale(3, HALF_UP);
    }

    private BigDecimal ceilingOrFloor(BigDecimal inventory, List<FactoryFinishedInventoryDto> factoryFinishedInventoryList) {
        BigDecimal ceilingInventory = inventory.setScale(0, CEILING);
        if (ceilingInventory.compareTo(inventory) <= 0) {
            return inventory;
        }

        BigDecimal subtract = ceilingInventory.subtract(inventory);
        BigDecimal ceiledInventory = diverseFactoryFinishedInventory(factoryFinishedInventoryList, subtract, inventory);

        return ceiledInventory.compareTo(ceilingInventory) >= 0 ? ceiledInventory : inventory.setScale(0, FLOOR);
    }

    private BigDecimal diverseFactoryFinishedInventory(List<FactoryFinishedInventoryDto> factoryFinishedInventoryList,
                                                       BigDecimal warehouseSale, BigDecimal totalDeliveryNum) {
        if (warehouseSale.compareTo(BigDecimal.ZERO) <= 0) {
            return totalDeliveryNum;
        }
        for (var dto : factoryFinishedInventoryList) {
            BigDecimal shippingNum = BigDecimal.valueOf(dto.getShippingNum());
            if (warehouseSale.compareTo(shippingNum) <= 0) {
                dto.setShippingNum(shippingNum.subtract(warehouseSale).setScale(3, HALF_UP).doubleValue());
                totalDeliveryNum = totalDeliveryNum.add(warehouseSale);
                break;
            } else {
                dto.setShippingNum(0D);
                totalDeliveryNum = totalDeliveryNum.add(shippingNum);
                warehouseSale = warehouseSale.subtract(shippingNum);
            }
        }
        return totalDeliveryNum.setScale(3, HALF_UP);
    }
}
