package com.purchase.purchase_server.mapper.Lcl;

import com.github.yulichang.base.MPJBaseMapper;
import com.purchase.purchase_server.entity.dataObject.Lcl.LclConsolidationRecordDO;
import org.apache.ibatis.annotations.Param;

/**
* <AUTHOR>
* @description 针对表【cm_lcl_consolidation_record(拼柜装柜记录表)】的数据库操作Mapper
* @createDate 2024-12-04 16:47:06
* @Entity generator.domain.CmLclConsolidationRecord
*/
public interface LclConsolidationRecordMapper extends MPJBaseMapper<LclConsolidationRecordDO> {
    int delete(@Param("id") String id);

}




