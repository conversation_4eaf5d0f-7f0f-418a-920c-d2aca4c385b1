package com.inventory_server.model.inventory.entity.dos;

import com.baomidou.mybatisplus.annotation.TableName;
import com.inventory_server.infrastructures.entity.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.Date;

/**
 * @Description 库存信息实体类
 * <AUTHOR>
 * @Date 2024/5/8 9:15
 **/
@EqualsAndHashCode(callSuper = true)
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@TableName("cm_real_time_non_fba_inventory_info")
public class InventoryInventoryInfoDO extends BaseEntity {
  private String selfSku;
  private String virtualSkuId;
  private Integer ycWarehouseId;
  private Integer saleStatus;
  private Integer piPurchaseOnwayQty;
  private Integer piReturnOnwayQty;
  private Integer piPendingQty;
  private Integer piInUsedQty;
  private Integer piWarningQty;
  private Integer piSellableQty;
  private Integer piSharedQty;
  private Double piCanSaleDays;
  private Integer piReservedQty;
  private Integer piNoStockQty;
  private Integer piNoStockDays;
  private Integer piUnsellableQty;
  private Integer piOutboundQty;
  private Double inventoryCost;
  private String currencyCode;
  private Date piUpdateTime;
  private Integer actualUsableInventoryQty;
  private Integer piPlannedQty;
  private Integer purchaseQuantity;
}
