package com.operation.logs.operation_logs.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.crafts_mirror.utils.dp.LogTrackNumDto;
import com.crafts_mirror.utils.dp.LogTrackNumPageDto;
import com.crafts_mirror.utils.dp.OperationLogListForm;
import com.operation.logs.operation_logs.entity.OperationLogDO;
import com.operation.logs.operation_logs.entity.dto.LogListByTypeDto;
import com.operation.logs.operation_logs.entity.dto.LogListDto;

import java.util.List;

public interface IOperationLogService {

    boolean saveOperationLog(OperationLogDO operationLogDO);

    boolean saveOperationLogBatch(List<OperationLogDO> operationLogDOList);

    List<LogListDto> getLogList(LogTrackNumDto dto);

    IPage<LogListDto> getLogPageList(LogTrackNumPageDto dto);

    List<LogListDto> getLogListByType(LogListByTypeDto dto);

    IPage<LogListDto> getLogPageByType(LogListByTypeDto dto);

    void deleteByFormList(OperationLogListForm form);
}
