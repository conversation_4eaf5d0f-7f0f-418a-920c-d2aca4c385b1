package com.purchase.purchase_server.entity.vo.purchaseOrder;

import com.purchase.purchase_server.entity.dto.PurchaseOrder.YicangPurchaseOrderScheduleDto;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * 易仓采购单虚拟sku
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class YicangPurchaseOrderVirtualVO implements Serializable {
    @Serial
    private static final long serialVersionUID = -635872207232015571L;
    private String id;
    /**
     * senbo虚拟skuId
     */
    private String virtualSkuId;

    /**
     * senbo虚拟sku
     */
    private String destinationSku;

    /**
     * 图片
     */
    private String image;
    /**
     * 自定义sku
     */
    private String productSku;
    /**
     * 自定义sku名称
     */
    private String sbSelfSkuName;

    /**
     * 产品经理
     */
    private String buyer;

    /**
     * 运营
     */
    private String operator;

    /**
     * 采购量
     */
    private Integer qtyEta;

    /**
     * 到货量
     */
    private Integer qtyReceving;

    /**
     * 未安排数量
     */
    private Integer expectedDeliveryQuantity;

    /**
     * 采购单明细表id
     */
    private String sbPoDetailId;
    /**
     * 备注
     */
    private String note;

    /**
     * sku类型(0-虚拟sku 1-老sku)
     */
    private String isOldStatus;

    /**
     * 渠道
     */
    private String channel;

    /**
     * 交期计划
     */
    private List<YicangPurchaseOrderScheduleDto> scheduleList;

    /**
     * 最近交期计划
     */
    private YicangPurchaseOrderScheduleDto latestDeliverySchedule;

    /**
     * 采购单id
     */
    private String sbPoId;
}