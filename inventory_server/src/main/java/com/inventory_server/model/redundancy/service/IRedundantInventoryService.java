package com.inventory_server.model.redundancy.service;

import cn.hutool.core.date.DateTime;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.inventory_server.applications.cqe.InventoryInfoQuery;
import com.inventory_server.applications.cqe.LeadTimeCommand;
import com.inventory_server.applications.dto.DetailInventoryRulesDto;
import com.inventory_server.applications.dto.InventoryFactoryAndForeignInfoDto;
import com.inventory_server.applications.dto.InventoryInfoDto;
import com.inventory_server.applications.dto.LeadTimeDto;
import com.inventory_server.model.product.entity.vo.OperatorSearchVo;
import com.inventory_server.model.redundancy.entity.dos.InventorySaleRulesDO;
import com.inventory_server.model.redundancy.entity.dos.InventorySoldOutDaysDO;
import com.inventory_server.model.redundancy.entity.dos.RedundantInventoryDO;
import com.inventory_server.model.redundancy.entity.dp.InventoryCalDp;
import com.inventory_server.model.redundancy.entity.dto.*;
import com.inventory_server.model.redundancy.entity.form.DeliveryCalculationForm;
import com.inventory_server.model.redundancy.entity.form.UrgentHeadShipDateForm;
import com.inventory_server.model.redundancy.entity.vo.DeliveryCalculationVo;
import com.inventory_server.model.redundancy.entity.vo.NormalDeliveryWatchBoardVo;
import com.inventory_server.model.warehouse.entity.dto.SenboWarehouseDto;
import jakarta.servlet.http.HttpServletResponse;

import java.io.InputStream;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.TreeMap;

public interface IRedundantInventoryService {

    /**
     * 导入冗余库存信息
     *
     * @param file          excel文件
     * @param fileName      文件名
     */
    ImportResultDto importRedundantInventoryExcel(InputStream file, byte[] fileBytes, String fileName);

    void exportTemplate(HttpServletResponse response);

    DeliveryCalculationVo getNormalShippingCalculationResult(List<InventoryCalDp> inventoryCalDps, int solidDate);

    DeliveryCalculationVo getShippingCalculationResultWithRequirement(List<InventoryCalDp> inventoryCalDps, boolean needFactoryPlan,
                                                                      boolean needOnShippingInventory, int solidDate,
                                                                      DateTime dateTime);

    /**
     * 补货导出
     *
     * @param inventoryCalDps
     * @param needFactoryPlan
     * @param needOnShippingInventory
     */
    DeliveryCalculationVo getShippingCalculation(LocalDate calFinishedDate, List<InventoryCalDp> inventoryCalDps,
                                                 boolean needFactoryPlan, boolean needOnShippingInventory, int solidDate);

    void saveRedundancyInfo(String importFileId, List<InventoryCalDp> inventoryCalDps, DeliveryCalculationVo shippingResult,
                            RedundancyResultDto redundancyResultDto, Map<String, LocalDate> soldOutDateMap,
                            Map<String, List<SoldOutDateBeforeTheoryDto>> soldOutDetailMap, Map<String, LocalDate> fullLinkSoldOutDateMap,
                            Map<String, List<SoldOutDateBeforeTheoryDto>> fullLinkSoldOutDetailMap, DeliveryCalculationVo normalDeliveryResult,
                            Map<String, Integer> headShippingDateMap, int solidDate, Map<String, SalableDateDto> salableDateMap,
                            Map<String, SalableDateDto> fullLinkSalableDateMap, Map<String, AchievementRateDto> achievementRate);

    DeliveryCalculationForm convertToRequestForm(List<InventoryCalDp> inventoryCalDps, DateTime dateTime,
                                                 boolean needFactoryPlan, boolean needOnShippingInventory, boolean needToTargetEndDate,
                                                 Map<String, Integer> headShippingDaysMap, int solidDate);

    /**
     * 查询冗余库存信息
     *
     * @param form 查询条件
     */
    IPage<InventoryInfoDto> pageList(InventoryInfoQuery form);

    /**
     * 查询冗余库存详细信息
     *
     * @param inventoryInfoId 冗余库存信息id
     */
    DetailInventoryRulesDto getDetailInventoryRules(String inventoryInfoId);

    DetailInventoryRulesDto getShippingCalculation(String inventoryInfoId);

    NormalDeliveryWatchBoardVo getNormalDeliveryWatchBoard(String inventoryInfoId);

    DetailInventoryRulesDto getShippingCalculationWithoutFactoryPlan(String inventoryInfoId);

    void supplementMockTable(DeliveryCalResultDto deliveryCalResultDto, RedundantInventoryDO inventoryInfoDO,
                             TreeMap<String, BigDecimal> targetSalesMap, List<SenboWarehouseDto> senboWarehouseList,
                             Map<String, Double> saleRatio);

    NormalDeliveryWatchBoardVo getNormalShippingCalculation(String inventoryInfoId, List<SenboWarehouseDto> senboWarehouseList);

    Map<String, List<InventorySoldOutDaysDO>> getBeforeSoldOutList(List<String> inventoryInfoIds, String type);

    void export(InventoryInfoQuery form, HttpServletResponse response);

    LeadTimeDto getLeadTime(String inventoryInfoId);

    boolean updateUrgentProduceDays(LeadTimeCommand leadTimeCommand);

    boolean updateUrgentHeadShipDate(UrgentHeadShipDateForm urgentForm);

    String selectFileInfo(String filePath);

    InventoryFactoryAndForeignInfoDto getFactoryRedundantInfo(List<String> virtualSkuList);

    InventoryFactoryAndForeignInfoDto getFactoryRedundantInfoInterior(List<String> virtualSkuList);


    boolean hasRedundancyByVirtualSkuId(List<String> virtualSkuIds);

    List<String> hasRedundancyByVirtualSkuIdList(List<String> virtualSkuIds);

    /**
     * 删除库存信息
     *
     * @param infoIds 需要删除的库存ID
     */
    boolean deleteFactoryByInfoId(List<String> infoIds);

    /**
     * 获取运营名单
     */
    List<OperatorSearchVo> getOperator();

    Set<String> getSnapIds(InventoryInfoQuery infoQuery);

    InventorySaleRulesDO getSaleDestination(String inventoryInfoId);

    boolean updateSaleDestination(InventorySaleRulesDO saleRulesDO);

}
