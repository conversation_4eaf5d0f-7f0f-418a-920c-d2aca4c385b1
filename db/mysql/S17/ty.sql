## 发货海外仓库存
ALTER TABLE `crafts_mirror`.`cm_delivery_foreign_inventory`
    ADD COLUMN `start_shipping_date` date DEFAULT NULL COMMENT '出货时间' AFTER `enable_using_date`;
## 补货海外仓库存
ALTER TABLE `crafts_mirror`.`cm_replenishment_foreign_inventory`
    ADD COLUMN `start_shipping_date` date DEFAULT NULL AFTER `enable_using_date`;
## 冗余库存海外仓库存
ALTER TABLE `crafts_mirror`.`cm_inventory_foreign_store`
    ADD COLUMN `start_shipping_date` date DEFAULT NULL COMMENT '出货时间' AFTER `enable_using_date`;
## 冗余库存在途的虚拟sku
ALTER TABLE `crafts_mirror`.`cm_inventory_foreign_store`
    ADD COLUMN `virtual_sku` varchar(50) NULL AFTER `inventory_info_id`;
## 发货导入在途虚拟sku
ALTER TABLE `crafts_mirror`.`cm_delivery_foreign_inventory`
    ADD COLUMN `virtual_sku` varchar(255) NULL COMMENT '导入时的虚拟sku' AFTER `warehouse_id`;
## 补货导入在途虚拟sku
ALTER TABLE `crafts_mirror`.`cm_replenishment_foreign_inventory`
    ADD COLUMN `virtual_sku` varchar(50) NULL AFTER `warehouse_id`;

## 是否修改过到货日期
ALTER TABLE `crafts_mirror`.`cm_replenishment_trial_purchase_inventory`
    ADD COLUMN `is_changed_arriving_date` char(1) NULL COMMENT '是否修改过到货日期' AFTER `trial_status`;

## 重命名非FBA实时库存表名
RENAME TABLE `crafts_mirror`.`cm_inventory_inventory_info` TO `crafts_mirror`.`cm_real_time_non_fba_inventory_info`;

## 易仓实时库存增加冗余字段虚拟商品id
ALTER TABLE `crafts_mirror`.`cm_real_time_non_fba_inventory_info`
    ADD COLUMN `virtual_sku_id` varchar(50) NULL COMMENT '虚拟商品id' AFTER `self_sku`;