package com.auth.authorization_server.model.entity;

import cn.hutool.core.util.StrUtil;
import com.auth.authorization_server.model.dp.PasswordDP;

/**
 * @Description 基于领域逻辑的实体类，它的字段和数据库储存不需要有必然的联系。Entity包含数据，同时也应该包含行为。
 * <AUTHOR>
 * @Date 2023/12/2 13:39
 **/
public record UserForLogin(String username, PasswordDP passwordDp) {

    public UserForLogin {
        if (!checkUserName(username)) {
            throw new RuntimeException("用户名不能为空");
        }
    }

    private boolean checkUserName(String username) {
        return StrUtil.isNotBlank(username);
    }
}
