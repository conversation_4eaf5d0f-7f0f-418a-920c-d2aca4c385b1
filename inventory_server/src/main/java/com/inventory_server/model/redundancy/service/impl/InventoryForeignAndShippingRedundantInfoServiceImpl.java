package com.inventory_server.model.redundancy.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.inventory_server.applications.dto.InventoryShipRedundantInfoDto;
import com.inventory_server.infrastructures.entity.PhysicalBaseEntity;
import com.inventory_server.model.redundancy.entity.bo.InventoryForeignRedundantBO;
import com.inventory_server.model.redundancy.entity.dos.InventoryForeignRedundantInfoDO;
import com.inventory_server.model.redundancy.entity.dos.InventoryForeignShipWatchBoardDO;
import com.inventory_server.model.redundancy.entity.dos.InventoryShippingRedundantInfoDO;
import com.inventory_server.model.redundancy.entity.dp.ForeignWarehouseRedundancy;
import com.inventory_server.model.redundancy.entity.dp.OnShippingWarehouseRedundancy;
import com.inventory_server.model.redundancy.repository.dataRepository.InventoryForeignRedundantInfoRepository;
import com.inventory_server.model.redundancy.repository.dataRepository.InventoryShippingRedundantInfoRepository;
import com.inventory_server.model.redundancy.service.IInventoryForeignAndShippingRedundantInfoService;
import com.inventory_server.model.redundancy.service.IInventoryWatchBoardService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Description 冗余库存信息获取、初步处理、数据储存、展示的service层
 * <AUTHOR>
 * @Date 2024/5/7 16:38
 **/
@Service
@Slf4j
public class InventoryForeignAndShippingRedundantInfoServiceImpl implements IInventoryForeignAndShippingRedundantInfoService {
    @Resource
    private InventoryForeignRedundantInfoRepository inventoryForeignRedundantInfoRepository;

    @Resource
    private InventoryShippingRedundantInfoRepository shippingRedundantInfoRepository;

    @Resource
    @Lazy
    private IInventoryWatchBoardService inventoryWatchBoardService;

    @Override
    public List<InventoryForeignRedundantBO> getForeignRedundantList(List<String> infoIdList) {
        List<InventoryForeignRedundantBO> foreignRedundantList = inventoryForeignRedundantInfoRepository.getForeignRedundantList(infoIdList);
        return foreignRedundantList.stream().filter(f -> f.getRedundantNum() > 0).collect(Collectors.toList());
    }

    public List<InventoryShipRedundantInfoDto> getShippingRedundantList(List<String> infoIdList) {
        List<InventoryForeignShipWatchBoardDO> watchBoardList = inventoryWatchBoardService.getForeignAndShipInventoryWatchBoardByInfoId(infoIdList.getFirst());
        if (CollectionUtil.isEmpty(watchBoardList)) {
            return new ArrayList<>();
        }
        List<String> watchBoardIdList = watchBoardList.stream().map(PhysicalBaseEntity::getId).toList();
        return shippingRedundantInfoRepository.getShipRedundantList(watchBoardIdList);
    }

    public List<InventoryShipRedundantInfoDto> getShippingRedundantListByInfoIds(List<String> infoIdList) {
        List<InventoryForeignShipWatchBoardDO> watchBoardList = inventoryWatchBoardService.getForeignAndShipInventoryWatchBoardByInfoIds(infoIdList);
        if (CollectionUtil.isEmpty(watchBoardList)) {
            return new ArrayList<>();
        }
        List<String> watchBoardIdList = watchBoardList.stream().map(PhysicalBaseEntity::getId).toList();
        List<InventoryShipRedundantInfoDto> shipRedundantList = shippingRedundantInfoRepository.getShipRedundantList(watchBoardIdList);
        Map<String, String> watchBoardMap = watchBoardList.stream().collect(Collectors.toMap(PhysicalBaseEntity::getId, InventoryForeignShipWatchBoardDO::getInventoryInfoId));
        for (InventoryShipRedundantInfoDto info : shipRedundantList) {
            info.setInventoryInfoId(watchBoardMap.get(info.getOnShippingWatchBoardId()));
        }
        return shipRedundantList;
    }

    @Override
    public List<InventoryForeignRedundantBO> getForeignRedundantListByWatchBoardId(String watchBoardId) {
        return inventoryForeignRedundantInfoRepository.getForeignRedundantListByWatchBoardId(watchBoardId);
    }

    @Override
    public List<InventoryShipRedundantInfoDto> getOnShippingRedundantInfoByWatchBoardId(String watchBoardId) {
        return shippingRedundantInfoRepository.getShipRedundantListByWatchBoardId(watchBoardId);
    }

    @Override
    public void batchSaveForeignRedundantInfo(String watchBoardId, List<ForeignWarehouseRedundancy> dtoList) {
        if (CollectionUtil.isEmpty(dtoList)) {
            return;
        }

        inventoryForeignRedundantInfoRepository.deleteByWatchBoardIds(Collections.singletonList(watchBoardId));
        List<InventoryForeignRedundantInfoDO> list = dtoList.stream()
                .map(dto -> (InventoryForeignRedundantInfoDO) InventoryForeignRedundantInfoDO.builder()
                        .warehouse(dto.getWarehouse())
                        .redundantNum(dto.getRedundantInventory().doubleValue())
                        .watchBoardId(watchBoardId)
                        .build())
                .toList();
        inventoryForeignRedundantInfoRepository.saveBatch(list);
    }

    @Override
    public void batchSaveOnShippingRedundantInfo(String watchBoardId, List<OnShippingWarehouseRedundancy> dtoList) {
        if (CollectionUtil.isEmpty(dtoList)) {
            return;
        }

        List<InventoryShippingRedundantInfoDO> list = dtoList.stream()
                .filter(f -> f.getRedundantInventory() > 0)
                .map(dto -> (InventoryShippingRedundantInfoDO) InventoryShippingRedundantInfoDO.builder()
                        .enableUsingDate(dto.getEnableUsingDate())
                        .redundantNum(dto.getRedundantInventory())
                        .shipmentCode(dto.getShipmentCode())
                        .warehouse(dto.getWarehouse())
                        .onShippingWatchBoardId(watchBoardId)
                        .startShippingDate(dto.getStartShippingDate())
                        .build()
                ).toList();

        shippingRedundantInfoRepository.saveBatch(list);
    }

    @Override
    public void deleteByWatchBoardIds(List<String> watchBoardIds) {
        inventoryForeignRedundantInfoRepository.deleteByWatchBoardIds(watchBoardIds);
    }
}
