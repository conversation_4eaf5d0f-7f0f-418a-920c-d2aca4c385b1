package com.purchase.purchase_server.controller;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.crafts_mirror.common.security.annotation.RequiresPermissions;
import com.crafts_mirror.utils.enums.ResponseCodeEnum;
import com.purchase.purchase_server.aop.PreventReSubmit;
import com.purchase.purchase_server.entity.bo.Lcl.LclContainerLoadingBO;
import com.purchase.purchase_server.entity.dataObject.Lcl.LclConsolidationRecordDO;
import com.purchase.purchase_server.entity.dto.Lcl.LclConsolidationRecordDTO;
import com.purchase.purchase_server.entity.dto.Lcl.LclContainerInfoDto;
import com.purchase.purchase_server.entity.dto.Lcl.LclEditRemainNumDTO;
import com.purchase.purchase_server.entity.dto.Lcl.LclTrialShippingInventoryDTO;
import com.purchase.purchase_server.entity.dto.SenboWarehouseDto;
import com.purchase.purchase_server.entity.form.*;
import com.purchase.purchase_server.entity.response.ResultDTO;
import com.purchase.purchase_server.entity.vo.DeliveryRecordPurchaseListVo;
import com.purchase.purchase_server.entity.vo.Lcl.*;
import com.purchase.purchase_server.entity.vo.UserInteriorVO;
import com.purchase.purchase_server.enums.lcl.LclConsolidationEnum;
import com.purchase.purchase_server.enums.lcl.LclStatusEnum;
import com.purchase.purchase_server.exception.BusinessException;
import com.purchase.purchase_server.repository.dataRepository.Lcl.LclConsolidationFinishedInventoryRepositoryImpl;
import com.purchase.purchase_server.repository.dataRepository.Lcl.LclConsolidationRecordRepositoryImpl;
import com.purchase.purchase_server.service.lcl.*;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.List;
import java.util.concurrent.TimeUnit;

import static com.crafts_mirror.utils.constant.RedisKeyConstant.*;
import static com.purchase.purchase_server.enums.lcl.LclStatusEnum.SAVED;

/**
 * @Description 拼柜
 * <AUTHOR>
 * @Date 2023/12/6 10:48
 **/
@RestController
@RequestMapping(value = "lclConsolidation")
@Slf4j
public class LclConsolidationController {
    @Resource
    private ILclConsolidationRecordService lclConsolidationRecordService;

    @Resource
    private ILclFinishedInventoryService lclFinishedInventoryService;

    @Resource
    private ILclConsolidationFinishedInventoryService lclConsolidationFinishedInventoryService;

    @Resource
    private ILclContainerInfoService lclContainerInfoService;

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Resource
    private LclConsolidationRecordRepositoryImpl lclConsolidationRecordRepository;

    @Resource
    private ILclCalContainerLoadingService lclCalContainerLoadingService;

    @Resource
    private ILclContainerLoadingDetailService lclContainerLoadingDetailService;

    @Resource
    private LclConsolidationFinishedInventoryRepositoryImpl lclConFinishedInventoryRepository;

    /**
     * 创建装柜
     */
    @RequiresPermissions("purchase:lclConsolidation:list")
    @PostMapping("/insert/fromDelivery")
    @PreventReSubmit
    @ResponseBody
    public ResultDTO<LclConsolidationRecordVO> createLclConsolidation(@RequestBody LclConsolidationForm form) {
        if (Boolean.FALSE.equals(stringRedisTemplate.opsForValue().setIfAbsent(CREATE_LCL_LOCK + form.getShippingRecordId(), "1", 7, TimeUnit.HOURS))) {
            return ResultDTO.error("装柜数据创建中，请稍后再试");
        }
        LclConsolidationRecordVO lclConsolidation;
        try {
            lclConsolidation = lclConsolidationRecordService.createLclConsolidation(form);
        } catch (Exception e) {
            throw new BusinessException(ResponseCodeEnum.BAD_REQUEST, e.getMessage());
        } finally {
            stringRedisTemplate.delete(CREATE_LCL_LOCK + form.getShippingRecordId());
        }
        return ResultDTO.success(lclConsolidation);
    }

    /**
     * 数据整理
     */
    @RequiresPermissions("purchase:lclConsolidation:list")
    @PostMapping("/insert/lclDataClean")
    @PreventReSubmit
    @ResponseBody
    public ResultDTO dataClean(@RequestBody LclConsolidationForm form) {
        if (Boolean.FALSE.equals(stringRedisTemplate.opsForValue().setIfAbsent(EDIT_LCL_DATA_CLEAN_LOCK + form.getLclRecordId(), "1", 7, TimeUnit.HOURS))) {
            return ResultDTO.error("装柜处理中，请稍后再试");
        }
        try {
            LclConsolidationRecordDO lclConsolidationRecordDO = lclConsolidationRecordRepository.list(LclConsolidationRecordDTO.builder().id(form.getLclRecordId()).build())
                    .stream()
                    .findFirst()
                    .orElseThrow(() -> new BusinessException(ResponseCodeEnum.BAD_REQUEST.getCode(), "装柜计划不存在"));

            if (lclConsolidationRecordDO.getLclStatus().equals(LclStatusEnum.CANCELLED.getCode())) {
                throw new BusinessException(ResponseCodeEnum.BAD_REQUEST.getCode(), "装柜计划已取消，无法整理");
            }
            LclConsolidationRecordDO lclRecordDO = LclConsolidationRecordDO.builder()
                    .id(form.getLclRecordId())
                    .lclStartDate(DateUtil.parse(form.getShippingStartDate()))
                    .lclEndDate(DateUtil.parse(form.getShippingEndDate()))
                    .lclStatus(LclStatusEnum.DATA_PROCESSING.getCode())
                    .isLclConsolidation(LclConsolidationEnum.CONSOLIDATION.getCode())
                    .minVolume(new BigDecimal(form.getMinVolume()))
                    .build();
            lclConsolidationRecordRepository.updateById(lclRecordDO);
            lclConsolidationRecordService.dataClean(form, lclRecordDO);
        } catch (Exception e) {
            throw new BusinessException(ResponseCodeEnum.BAD_REQUEST, e.getMessage());
        } finally {
            stringRedisTemplate.delete(EDIT_LCL_DATA_CLEAN_LOCK + form.getLclRecordId());
        }
        return ResultDTO.success();
    }

    /**
     * 拼柜装柜
     */
    @RequiresPermissions("purchase:lclConsolidation:list")
    @PostMapping("/insert/lclContainerLoading")
    @PreventReSubmit
    @ResponseBody
    public ResultDTO lclContainerLoading(@RequestBody LclConsolidationForm form) {
        if (Boolean.FALSE.equals(stringRedisTemplate.opsForValue().setIfAbsent(EDIT_LCL_DATA_CLEAN_LOCK + form.getLclRecordId(), "1", 7, TimeUnit.HOURS))) {
            return ResultDTO.error("装柜处理中，请稍后再试");
        }
        try {
            LclConsolidationRecordDO lclConsolidationRecordDO = lclConsolidationRecordRepository.list(LclConsolidationRecordDTO.builder().id(form.getLclRecordId()).build())
                    .stream()
                    .findFirst()
                    .orElseThrow(() -> new BusinessException(ResponseCodeEnum.BAD_REQUEST.getCode(), "装柜计划不存在"));

            if (lclConsolidationRecordDO.getLclStatus().equals(LclStatusEnum.CANCELLED.getCode())) {
                throw new BusinessException(ResponseCodeEnum.BAD_REQUEST.getCode(), "装柜计划已取消，无法拼柜");
            }

            LclConsolidationRecordDO lclRecordDO = LclConsolidationRecordDO.builder()
                    .id(form.getLclRecordId())
                    .lclStatus(LclStatusEnum.CONTAINER_PROCESSING.getCode())
                    .isLclConsolidation(LclConsolidationEnum.CONTAINING.getCode())
                    .lclContainerStartDate(DateUtil.parse(form.getShippingStartDate()))
                    .lclContainerEndDate(DateUtil.parse(form.getShippingEndDate()))
                    .build();
            lclConsolidationRecordRepository.updateById(lclRecordDO);
            List<LclContainerLoadingBO> lclContainerLoadingBOS = lclConFinishedInventoryRepository.listWithTrial(form);

            lclContainerLoadingBOS.forEach(i -> {
                if (StrUtil.isBlank(i.getAddressCode())) {
                    throw new BusinessException(ResponseCodeEnum.BAD_REQUEST, "供应商地址代号为空，供应商代码：" + i.getFactoryCode());
                }
            });
            lclCalContainerLoadingService.lclContainerLoading(form, lclRecordDO);
        } catch (Exception e) {
            LclConsolidationRecordDO lclRecordDO = LclConsolidationRecordDO.builder()
                    .id(form.getLclRecordId())
                    .lclStatus(LclStatusEnum.PENDING_CONTAINER.getCode())
                    .isLclConsolidation(LclConsolidationEnum.CONSOLIDATION.getCode())
                    .build();
            lclConsolidationRecordRepository.updateById(lclRecordDO);
            throw new BusinessException(ResponseCodeEnum.BAD_REQUEST, e.getMessage());
        } finally {
            stringRedisTemplate.delete(EDIT_LCL_DATA_CLEAN_LOCK + form.getLclRecordId());
        }
        return ResultDTO.success();
    }

    /**
     * 装柜记录查询
     */
    @RequiresPermissions("purchase:lclConsolidation:list")
    @PostMapping("/pageList/lclRecord")
    @ResponseBody
    public ResultDTO<IPage<LclConsolidationRecordVO>> pageList(@RequestBody LclConsolidationRecordForm form) {
        return ResultDTO.success(lclConsolidationRecordService.pageList(form));
    }

    /**
     * 装柜数据查询
     */
    @RequiresPermissions("purchase:lclConsolidation:list")
    @PostMapping("/pageList/lclFinishedInventory")
    @ResponseBody
    public ResultDTO<LclConsolidationPageVO> pageListLclFinishedInventory(@RequestBody LclConsolidationRecordForm form) {
        return ResultDTO.success(lclConsolidationFinishedInventoryService.pageListLclFinishedInventory(form));
    }

    @RequiresPermissions("purchase:lclConsolidation:list")
    @GetMapping("/list/user")
    @ResponseBody
    public ResultDTO<List<UserInteriorVO>> getBuyerSet() {
        return ResultDTO.success(lclConsolidationRecordService.getUserList());
    }

    /**
     * 发货数据编辑
     */
    @RequiresPermissions("purchase:lclConsolidation:list")
    @PostMapping("/update/lclShippingNum")
    @ResponseBody
    @PreventReSubmit
    public ResultDTO<Boolean> updateLclShippingNum(@RequestBody LclShippingNumEditListNewForm form) {
        if (Boolean.FALSE.equals(stringRedisTemplate.opsForValue().setIfAbsent(EDIT_LCL_SHIPPING_NUM_LOCK + form.getId(), "1", 7, TimeUnit.HOURS))) {
            return ResultDTO.error("装柜数据编辑中，请稍后再试");
        }
        return ResultDTO.success(lclFinishedInventoryService.updateLclShippingNum(form));
    }

    /**
     * 装柜数据变动记录
     */
    @RequiresPermissions("purchase:lclConsolidation:list")
    @PostMapping("/info/lclNumHistory")
    @ResponseBody
    public ResultDTO<List<LclShippingNumHistoryVO>> lclNumHistory(@RequestBody LclShippingNumEditForm form) {
        return ResultDTO.success(lclConsolidationFinishedInventoryService.getLclNumHistory(form));
    }

    /**
     * 删除装柜数据
     */
    @RequiresPermissions("purchase:lclConsolidation:list")
    @PostMapping("/delete/lclFinished")
    @ResponseBody
    @PreventReSubmit
    public ResultDTO<Boolean> deleteLclFinished(@RequestBody @Valid LclFinishedDeleteForm form) {
        return ResultDTO.success(lclFinishedInventoryService.deleteLclFinished(form));
    }

    /**
     * 取消排柜
     */
    @RequiresPermissions("purchase:lclConsolidation:list")
    @PostMapping("/update/cancelRecord")
    @ResponseBody
    public ResultDTO cancelRecord(@RequestBody LclConsolidationRecordForm form) {
        lclConsolidationRecordService.updateRecordStatus(form.getId(), LclStatusEnum.CANCELLED);
        return ResultDTO.success();
    }

    /**
     * 删除排柜
     */
    @RequiresPermissions("purchase:lclConsolidation:list")
    @PostMapping("/update/deleteRecord")
    @ResponseBody
    @PreventReSubmit
    public ResultDTO deleteRecord(@RequestBody LclConsolidationRecordForm form) {
        lclConsolidationRecordService.updateRecordStatus(form.getId(), LclStatusEnum.DELETE);
        return ResultDTO.success();
    }

    /**
     * 导出整理后数据
     */
    @RequiresPermissions("purchase:lclConsolidation:list")
    @PostMapping("/export")
    @PreventReSubmit
    public void exportLclInfo(@RequestBody LclConsolidationRecordForm form, HttpServletResponse response) {
        lclConsolidationFinishedInventoryService.exportLclInfo(form, response);
    }

    /**
     * 导出拼柜后数据
     */
    @RequiresPermissions("purchase:lclConsolidation:list")
    @PostMapping("/export/lclContainerLoading")
    @PreventReSubmit
    public void exportLclContainerLoadingInfo(@RequestBody LclSearchPageForm form, HttpServletResponse response) {
        lclContainerInfoService.exportLclContainerLoadingInfo(form, response);
    }

    /**
     * 导出原发货计划
     */
    @RequiresPermissions("purchase:lclConsolidation:list")
    @PostMapping("/export/deliveryPlan")
    @PreventReSubmit
    public ResultDTO exportDeliveryPlan(@RequestBody LclConsolidationForm form) {
        if (Boolean.FALSE.equals(stringRedisTemplate.opsForValue().setIfAbsent(EXPORT_DELIVERY_LOCK + form.getShippingRecordId(), "1", 7, TimeUnit.HOURS))) {
            return ResultDTO.error(ResponseCodeEnum.BAD_REQUEST, "当前该计划文件生成中，请稍后重试");
        }
        lclConsolidationRecordService.exportDeliveryPlan(form);
        return ResultDTO.success();
    }

    @PostMapping("/download/export")
    @PreventReSubmit
    public ResultDTO<DeliveryRecordPurchaseListVo> downloadExport(@RequestBody LclConsolidationForm form) {
        return ResultDTO.success(lclConsolidationRecordService.downloadExport(form));
    }

    /**
     * 未安排数量列表
     *
     * @param form
     */
    @PostMapping("/detail/remainNum")
    public ResultDTO<List<LclTrialShippingInventoryDTO>> detailRemainNum(@RequestBody LclConsolidationForm form) {
        return ResultDTO.success(lclFinishedInventoryService.detailRemainNum(form));
    }

    /**
     * 编辑-未安排数量列表
     *
     * @param form
     */
    @PostMapping("/detail/remainNumForEdit")
    public ResultDTO<LclEditRemainNumDTO> detailRemainNumForEdit(@RequestBody LclConsolidationForm form) {
        return ResultDTO.success(lclFinishedInventoryService.detailRemainNumForEditList(form));
    }

    /********************************************************************
     ************************* 排柜拼柜页面相关需求 ************************
     ********************************************************************/

    @RequiresPermissions("purchase:lclConsolidation:list")
    @PostMapping("/pageList/info/container")
    public ResultDTO<LclContainerInfoPage<LclContainerInfoVo>> lclContainerInfoPage(@RequestBody LclSearchPageForm form) {
        return ResultDTO.success(lclCalContainerLoadingService.getLclContainerInfoPage(form));
    }

    @RequiresPermissions("purchase:lclConsolidation:list")
    @PostMapping("/list/detail/container")
    public ResultDTO<LclContainerDetailVo> lclContainerDetail(@RequestBody LclSearchPageForm form) {
        LclContainerDetailVo containerDetailVo = lclCalContainerLoadingService.getContainerDetailList(form);
        return ResultDTO.success(containerDetailVo);
    }

    @GetMapping("/count/shipmentCategory")
    public ResultDTO<LclShipmentCategoryAggVo> getShipmentCategoryAndCount(@RequestParam("recordId") String recordId) {
        return ResultDTO.success(lclCalContainerLoadingService.aggContainerTypeByRecordId(recordId));
    }

    @RequiresPermissions("purchase:lclConsolidation:list")
    @PostMapping("/create/shipmentCode")
    @PreventReSubmit
    public ResultDTO<Boolean> createShipmentCode(@RequestBody LclShipmentCodeCreationForm form) {
        lclCalContainerLoadingService.createShipmentCode(form);
        return ResultDTO.success();
    }

    @RequiresPermissions("purchase:lclConsolidation:list")
    @PostMapping("/update/shippingStartDate")
    @PreventReSubmit
    public ResultDTO<Boolean> updateLclShippingStartDate(@RequestBody LclUpdateShippingStartDateForm form) {
        lclCalContainerLoadingService.updateStartShippingDate(form);
        return ResultDTO.success(true);
    }

    @RequiresPermissions("purchase:lclConsolidation:list")
    @PostMapping("/update/shippingNum")
    @PreventReSubmit
    public ResultDTO<Boolean> updateLclShippingNum(@RequestBody LclUpdateShippingNumForm form) {
        if (form.getLclShippingNum() == null) {
            throw new BusinessException(ResponseCodeEnum.BAD_REQUEST, "发货数量不能为空");
        }
        lclContainerLoadingDetailService.updateShippingNum(form);
        return ResultDTO.success(true);
    }

    @RequiresPermissions("purchase:lclConsolidation:list")
    @GetMapping("/confirm/containerInfo")
    @PreventReSubmit
    public ResultDTO<Boolean> confirmLclContainerInfo(@RequestParam("recordId") String recordId) {
        lclConsolidationRecordService.updateRecordStatus(recordId, SAVED);
        return ResultDTO.success(true);
    }

    /**
     * 移动或拆分柜子数据
     */
    @RequiresPermissions("purchase:lclConsolidation:list")
    @PostMapping("/container/moveOrSplit")
    @ResponseBody
    @PreventReSubmit
    public ResultDTO<Boolean> moveOrSplitContainerData(@RequestBody LclContainerMoveForm form) {
        if (Boolean.FALSE.equals(stringRedisTemplate.opsForValue().setIfAbsent(
                MOVE_SPLIT_CONTAINER_LOCK_ + form.getRecordId(), "1", 1, TimeUnit.HOURS))) {
            return ResultDTO.error("柜子数据操作中，请稍后再试");
        }
        try {
            return ResultDTO.success(lclContainerInfoService.moveOrSplitContainerData(form));
        } finally {
            stringRedisTemplate.delete(MOVE_SPLIT_CONTAINER_LOCK_ + form.getRecordId());
        }
    }

    /**
     * 移动或拆分柜子数据-检查
     */
    @RequiresPermissions("purchase:lclConsolidation:list")
    @PostMapping("/container/moveOrSplit/check")
    @ResponseBody
    public ResultDTO<String> checkMoveOrSplitContainerData(@RequestBody LclContainerMoveForm form) {

        return ResultDTO.success(lclContainerInfoService.checkMoveOrSplitContainerData(form));

    }

    /**
     * 移动或拆分柜子数据-获取货件号
     */
    @RequiresPermissions("purchase:lclConsolidation:list")
    @PostMapping("/container/moveOrSplit/getShipmentCode")
    @ResponseBody
    public ResultDTO<String> getShipmentCode(@RequestBody LclContainerMoveForm form) {
        return ResultDTO.success(lclContainerInfoService.getShipmentCode(form));
    }

    /**
     * 移动或拆分柜子数据-当前货件列表
     */
    @RequiresPermissions("purchase:lclConsolidation:list")
    @PostMapping("/container/moveOrSplit/shipmentCodeList")
    @ResponseBody
    public ResultDTO<List<LclContainerInfoDto>> shipmentCodeList(@RequestBody LclContainerMoveForm form) {
        return ResultDTO.success(lclContainerInfoService.shipmentCodeList(form));
    }

    /**
     * 删除拼柜装柜数据
     */
    @RequiresPermissions("purchase:lclConsolidation:list")
    @PostMapping("/delete/lclContainerDetail")
    @ResponseBody
    @PreventReSubmit
    public ResultDTO<Boolean> deleteLclContainerDetail(@RequestBody LclContainerDetailDeleteForm form) {
        return ResultDTO.success(lclContainerInfoService.deleteLclContainerDetail(form));
    }

    /**
     * 更新拼柜信息状态
     */
    @RequiresPermissions("purchase:lclConsolidation:list")
    @PostMapping("/update/containerStatus")
    @ResponseBody
    @PreventReSubmit
    public ResultDTO<Boolean> updateContainerStatus(@RequestBody @Valid LclContainerStatusUpdateForm form) {
        return ResultDTO.success(lclContainerInfoService.updateContainerStatus(form));
    }

    @GetMapping("/list/warehouse")
    @ResponseBody
    public ResultDTO<List<SenboWarehouseDto>> getSenboWarehouse() {
        return ResultDTO.success(lclContainerInfoService.getSenboWarehouseVo());
    }
}
