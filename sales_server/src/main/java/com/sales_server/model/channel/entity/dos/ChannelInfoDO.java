package com.sales_server.model.channel.entity.dos;

import com.baomidou.mybatisplus.annotation.TableName;
import com.sales_server.infrastructures.entity.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;


/**
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/5/8 9:15
 **/
@EqualsAndHashCode(callSuper = true)
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@TableName("cm_channel_info")
public class ChannelInfoDO extends BaseEntity {

  private Integer isLeaf;
  private String parentId;
  private String ancestors;
  private Integer sort;
  private String channelName;
  private String remarks;

}
