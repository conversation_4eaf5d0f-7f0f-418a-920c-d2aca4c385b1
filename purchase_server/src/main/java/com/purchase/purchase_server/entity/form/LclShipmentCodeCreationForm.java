package com.purchase.purchase_server.entity.form;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @Description 生成货件号表单
 * <AUTHOR>
 * @Date 2025/5/26 19:35
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
public class LclShipmentCodeCreationForm implements Serializable {

    /**
     * 家具货件号后缀
     */
    private Integer furnitureCodeSuffix;

    /**
     * 灯具货件号后缀
     */
    private Integer lampCodeSuffix;

    /**
     * 家具+灯具货件号后缀
     */
    private Integer mixCodeSuffix;

    /**
     * 排柜id
     */
    private String lclRecordId;

}
