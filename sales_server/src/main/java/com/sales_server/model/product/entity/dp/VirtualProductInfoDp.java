package com.sales_server.model.product.entity.dp;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @Date 2024/11/12 15:44
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class VirtualProductInfoDp {

    private String virtualSkuId;
    private String spu;
    private String spuName;
    private String image;
    private String productName;
    private String selfSku;
    private String virtualSku;
    private String channel;
    private Integer productStatus;
    private Integer subType;
    private Integer productType;
    private String operator;
}
