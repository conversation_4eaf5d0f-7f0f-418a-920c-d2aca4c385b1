package com.auth.authorization_server.service.impl;


import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.auth.authorization_server.assembler.MenuAssembler;
import com.auth.authorization_server.enums.MenuFrameEnum;
import com.auth.authorization_server.enums.MenuTypeEnum;
import com.auth.authorization_server.model.dataObject.MenuDO;
import com.auth.authorization_server.model.dataObject.RoleDO;
import com.auth.authorization_server.model.dataObject.UserDO;
import com.auth.authorization_server.model.vo.MetaVo;
import com.auth.authorization_server.model.vo.RouterVo;
import com.auth.authorization_server.model.vo.TreeSelect;
import com.auth.authorization_server.repository.MenuRepositoryImpl;
import com.auth.authorization_server.repository.RoleMenuRepositoryImpl;
import com.auth.authorization_server.req.MenuParams;
import com.auth.authorization_server.domain.dto.MenuDTO;
import com.auth.authorization_server.service.ISysMenuService;
import com.auth.authorization_server.service.ISysRoleService;
import com.auth.authorization_server.service.ISysUserService;
import com.auth.authorization_server.utils.StringUtils;
import com.crafts_mirror.common.security.utils.SecurityUtils;
import com.crafts_mirror.utils.common.entity.LoginVo;
import com.crafts_mirror.utils.constant.RedisKeyConstant;
import com.crafts_mirror.utils.constant.UserConstants;
import com.crafts_mirror.utils.dp.MenuTitleForm;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 菜单 业务层处理
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class SysMenuServiceImpl implements ISysMenuService {

    @Resource
    private MenuRepositoryImpl menuRepositoryImpl;

    @Resource
    private RoleMenuRepositoryImpl roleMenuRepositoryImpl;

    @Resource
    private ISysRoleService roleService;

    @Lazy
    @Resource
    private ISysUserService userService;

    @Resource
    private MenuAssembler menuAssembler;

    @Resource
    private RedisTemplate<String, LoginVo> redisTemplate;


    @Override
    public List<MenuDTO> selectMenuList(String userId) {
        return selectMenuList(new MenuParams(), userId);
    }

    /**
     * 查询系统菜单列表
     *
     * @param menu 菜单信息
     * @return 菜单列表
     */
    @Override
    public List<MenuDTO> selectMenuList(MenuParams menu, String userId) {
        List<MenuDO> menuList = null;
        // 管理员显示所有菜单信息
        if (UserDO.isAdmin(userId)) {
            menuList = menuRepositoryImpl.selectMenuList(menu);
        } else {
            menuList = menuRepositoryImpl.selectMenuListByUserId(menu, userId);
        }
        return menuAssembler.menuDoListToDto(menuList);
    }

    @Override
    public MenuDTO selectMenuById(String menuId) {
        MenuDO menuDO = menuRepositoryImpl.getById(menuId);
        return menuAssembler.menuDoToDto(menuDO);
    }

    @Override
    public List<MenuDTO> selectMenuTreeByUserId(String userId) {
        List<MenuDO> menus = null;
        if (SecurityUtils.isAdmin(userId)) {
            menus = menuRepositoryImpl.selectMenuTreeAll();
        } else {
            menus = menuRepositoryImpl.selectMenuTreeByUserId(userId);
        }
        List<MenuDTO> menuDTOS = menuAssembler.menuDoListToDto(menus);
        return getChildPerms(menuDTOS, "0");
    }

    /**
     * 根据角色ID查询菜单树信息
     *
     * @param roleId 角色ID
     * @return 选中菜单列表
     */
    @Override
    public List<String> selectMenuListByRoleId(String roleId) {
        return menuRepositoryImpl.selectMenuListByRoleId(roleId);
    }

    /**
     * 构建前端路由所需要的菜单
     *
     * @param menus 菜单列表
     * @return 路由列表
     */
    @Override
    public List<RouterVo> buildMenus(List<MenuDTO> menus) {
        List<RouterVo> routers = new LinkedList<>();
        for (MenuDTO menu : menus) {
            RouterVo router = new RouterVo();
            router.setName(menu.getPathName());
            router.setPath(getRouterPath(menu));
            router.setComponent(getComponent(menu));
            if (MenuFrameEnum.YES_FRAME.getCode().equals(menu.getIsFrame())) {
                router.setMeta(new MetaVo(menu.getMenuName(), menu.getIcon(), true));
            } else {
                if (StrUtil.isNotBlank(menu.getPerms())){
                    router.setMeta(new MetaVo(menu.getMenuName(), menu.getIcon(), menu.getMenuType(), Collections.singletonList(menu.getPerms())));
                }else{
                    router.setMeta(new MetaVo(menu.getMenuName(), menu.getIcon(), menu.getMenuType()));
                }
            }
            List<MenuDTO> cMenus = menu.getChildren();
            if (CollectionUtil.isNotEmpty(cMenus) && MenuTypeEnum.TYPE_DIR.getCode().equals(menu.getMenuType())) {
                //router.setAlwaysShow(true);
                //router.setRedirect("noRedirect");
                router.setChildren(buildMenus(cMenus));
            } else if (isMenuFrame(menu)) {
                router.setMeta(null);
                List<RouterVo> childrenList = new ArrayList<>();
                RouterVo children = new RouterVo();
                children.setPath(menu.getPath());
                children.setComponent(menu.getComponent());
                children.setName(menu.getPathName());
                if (StrUtil.isNotBlank(menu.getPerms())){
                    children.setMeta(new MetaVo(menu.getMenuName(), menu.getIcon(), menu.getMenuType(), Collections.singletonList(menu.getPerms())));
                }else{
                    children.setMeta(new MetaVo(menu.getMenuName(), menu.getIcon(), menu.getMenuType()));
                }

                childrenList.add(children);
                router.setChildren(childrenList);
            } else if (menu.getParentId().equals("0") && isInnerLink(menu)) {
                router.setMeta(new MetaVo(menu.getMenuName(), menu.getIcon()));
                router.setPath("/");
                List<RouterVo> childrenList = new ArrayList<>();
                RouterVo children = new RouterVo();
                String routerPath = innerLinkReplaceEach(menu.getPath());
                children.setPath(routerPath);
                children.setComponent(UserConstants.INNER_LINK);
                children.setName(menu.getPathName());
                if (StrUtil.isNotBlank(menu.getPerms())){
                    children.setMeta(new MetaVo(menu.getMenuName(), menu.getIcon(), menu.getMenuType(), Collections.singletonList(menu.getPerms())));
                }else{
                    children.setMeta(new MetaVo(menu.getMenuName(), menu.getIcon(), menu.getMenuType()));
                }

                childrenList.add(children);
                router.setChildren(childrenList);
            }
            routers.add(router);
        }
        return routers;
    }

    /**
     * 构建前端所需要下拉树结构
     *
     * @param menus 菜单列表
     * @return 下拉树结构列表
     */
    @Override
    public List<TreeSelect> buildMenuTreeSelect(List<MenuDTO> menus) {
        List<MenuDTO> menuTrees = buildMenuTree(menus);
        return menuTrees.stream().map(TreeSelect::new).collect(Collectors.toList());
    }

    /**
     * 构建前端所需要树结构
     *
     * @param menus 菜单列表
     * @return 树结构列表
     */
    @Override
    public List<MenuDTO> buildMenuTree(List<MenuDTO> menus) {
        List<MenuDTO> returnList = new ArrayList<>();
        List<String> tempList = menus.stream().map(MenuDTO::getId).toList();
        for (MenuDTO menu : menus) {
            // 如果是顶级节点, 遍历该父节点的所有子节点
            if (!tempList.contains(menu.getParentId())) {
                recursionFn(menus, menu);
                returnList.add(menu);
            }
        }
        if (returnList.isEmpty()) {
            returnList = menus;
        }
        return returnList;
    }

    /**
     * 根据父节点的ID获取所有子节点
     *
     * @param list     分类表
     * @param parentId 传入的父节点ID
     * @return String
     */
    public List<MenuDTO> getChildPerms(List<MenuDTO> list, String parentId) {
        List<MenuDTO> returnList = new ArrayList<MenuDTO>();
        for (MenuDTO t : list) {
            // 一、根据传入的某个父节点ID,遍历该父节点的所有子节点
            if (t.getParentId().equals(parentId)) {
                recursionFn(list, t);
                returnList.add(t);
            }
        }
        return returnList;
    }

    /**
     * 递归列表
     *
     * @param list 分类表
     * @param t    子节点
     */
    private void recursionFn(List<MenuDTO> list, MenuDTO t) {
        // 得到子节点列表
        List<MenuDTO> childList = getChildList(list, t);
        t.setChildren(childList);
        for (MenuDTO tChild : childList) {
            if (hasChild(list, tChild)) {
                recursionFn(list, tChild);
            }
        }
    }

    /**
     * 得到子节点列表
     */
    private List<MenuDTO> getChildList(List<MenuDTO> list, MenuDTO t) {
        List<MenuDTO> tlist = new ArrayList<>();
        for (MenuDTO n : list) {
            if (n.getParentId().equals(t.getId())) {
                tlist.add(n);
            }
        }
        return tlist;
    }

    /**
     * 判断是否有子节点
     */
    private boolean hasChild(List<MenuDTO> list, MenuDTO t) {
        return !getChildList(list, t).isEmpty();
    }

    /**
     * 是否存在菜单子节点
     *
     * @param menuId 菜单ID
     * @return 结果
     */
    @Override
    public boolean hasChildByMenuId(String menuId) {
        Long result = menuRepositoryImpl.hasChildByMenuId(menuId);
        return result > 0L;
    }

    /**
     * 查询菜单使用数量
     *
     * @param menuId 菜单ID
     * @return 结果
     */
    @Override
    public boolean checkMenuExistRole(String menuId) {
        Long result = roleMenuRepositoryImpl.checkMenuExistRole(menuId);
        return result > 0L;
    }

    /**
     * 校验菜单名称是否唯一
     *
     * @param menu 菜单信息
     * @return 结果
     */
    @Override
    public boolean checkMenuNameUnique(MenuParams menu) {
        String menuId = StrUtil.isBlank(menu.getId()) ? "-1" : menu.getId();
        MenuDO menuDO = menuRepositoryImpl.checkMenuNameUnique(menu.getMenuName(), menu.getParentId());
        return !ObjectUtil.isNotEmpty(menuDO) || Objects.equals(menuDO.getId(), menuId);
    }

    /**
     * 获取路由名称
     *
     * @param menu 菜单信息
     * @return 路由名称
     */
    public String getRouteName(MenuDTO menu) {
        // 非外链并且是一级目录（类型为目录）
        if (isMenuFrame(menu)) {
            return "";
        }
        return getRouteName(menu.getPath());
    }

    /**
     * 获取路由名称，如没有配置路由名称则取路由地址
     *
     * @param path 路由地址
     * @return 路由名称（驼峰格式）
     */
    public String getRouteName(String path) {
        return StringUtils.capitalize(path);
    }

    /**
     * 获取路由地址
     *
     * @param menu 菜单信息
     * @return 路由地址
     */
    public String getRouterPath(MenuDTO menu) {
        String routerPath = menu.getPath();
        // 内链打开外网方式
        if (!menu.getParentId().equals("0") && isInnerLink(menu)) {
            routerPath = innerLinkReplaceEach(routerPath);
        }
        // 非外链并且是一级目录（类型为目录）
        if (menu.getParentId().equals("0") && MenuTypeEnum.TYPE_DIR.getCode().equals(menu.getMenuType())
                && MenuFrameEnum.NO_FRAME.getCode().equals(menu.getIsFrame())) {
            routerPath = menu.getPath();
        }
        // 非外链并且是一级目录（类型为菜单）
        else if (isMenuFrame(menu)) {
            routerPath = "/";
        }
        return routerPath;
    }

    /**
     * 获取组件信息
     *
     * @param menu 菜单信息
     * @return 组件信息
     */
    public String getComponent(MenuDTO menu) {
        String component = UserConstants.LAYOUT;
        if (StrUtil.isNotBlank(menu.getComponent()) && !isMenuFrame(menu)) {
            component = menu.getComponent();
        } else if (StrUtil.isBlank(menu.getComponent()) && menu.getParentId().equals("0") && isInnerLink(menu)) {
            component = UserConstants.INNER_LINK;
        } else if (StrUtil.isBlank(menu.getComponent()) && isParentView(menu)) {
            component = UserConstants.PARENT_VIEW;
        }
        return component;
    }

    /**
     * 是否为菜单内部跳转
     *
     * @param menu 菜单信息
     * @return 结果
     */
    public boolean isMenuFrame(MenuDTO menu) {
        return menu.getParentId().equals("0") && MenuTypeEnum.TYPE_MENU.getCode().equals(menu.getMenuType())
                && menu.getIsFrame().equals(MenuFrameEnum.NO_FRAME.getCode());
    }

    /**
     * 是否为内链组件
     *
     * @param menu 菜单信息
     * @return 结果
     */
    public boolean isInnerLink(MenuDTO menu) {
        return menu.getIsFrame().equals(MenuFrameEnum.NO_FRAME.getCode()) && StringUtils.ishttp(menu.getPath());
    }

    /**
     * 是否为parent_view组件
     *
     * @param menu 菜单信息
     * @return 结果
     */
    public boolean isParentView(MenuDTO menu) {
        return !menu.getParentId().equals("0") && MenuTypeEnum.TYPE_DIR.getCode().equals(menu.getMenuType());
    }

    /**
     * 新增保存菜单信息
     *
     * @param menu 菜单信息
     * @return 结果
     */
    @Override
    public boolean insertMenu(MenuParams menu) {
        MenuDO menuDO = menuAssembler.menuParamsToDo(menu);
        menuDO.setCreateBy(SecurityUtils.getUsername());
        return menuRepositoryImpl.save(menuDO);
    }

    /**
     * 修改保存菜单信息
     *
     * @param menu 菜单信息
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateMenu(MenuParams menu) {
        MenuDO menuDO = menuAssembler.menuParamsToDo(menu);
        menuDO.setUpdateBy(SecurityUtils.getUsername());
        menuRepositoryImpl.updateById(menuDO);

        List<String> menuIds = Collections.singletonList(menuDO.getId());
        List<MenuDO> menuDOS;
        List<String> allMenuIds = new ArrayList<>();
        while (CollectionUtil.isNotEmpty(menuIds)){
            allMenuIds.addAll(menuIds);
            menuDOS = menuRepositoryImpl.selectChildByMenuId(menuIds);
            menuIds = menuDOS.stream().map(MenuDO::getId).collect(Collectors.toList());
        }
        updateCacheByMenuIds(allMenuIds);
        return true;
    }

    /**
     * 修改菜单状态
     *
     * @param menu 菜单信息
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateMenuStatus(MenuParams menu) {

        List<String> menuIds = Collections.singletonList(menu.getId());
        MenuDO menuDOfirst = new MenuDO();
        menuDOfirst.setId(menu.getId());
        menuDOfirst.setMenuStatus(menu.getMenuStatus());
        menuDOfirst.setUpdateBy(SecurityUtils.getUsername());
        List<MenuDO> menuDOS = new ArrayList<>();
        menuDOS.add(menuDOfirst);
        List<String> allMenuIds = new ArrayList<>();

        while (!menuIds.isEmpty()) {
            allMenuIds.addAll(menuIds);
            List<MenuDO> updateDOs = menuDOS.stream()
                    .map(i -> {
                        MenuDO menuDO = new MenuDO();
                        menuDO.setId(i.getId());
                        menuDO.setMenuStatus(menu.getMenuStatus());
                        menuDO.setUpdateBy(SecurityUtils.getUsername());
                        return menuDO;
                    })
                    .toList();
            menuRepositoryImpl.updateBatchById(updateDOs);
            menuDOS = menuRepositoryImpl.selectChildByMenuId(menuIds);
            menuIds = menuDOS.stream().map(MenuDO::getId).toList();
        }

        updateCacheByMenuIds(allMenuIds);
        return true;
    }

    private void updateCacheByMenuIds(List<String> allMenuIds){
        List<RoleDO> roleDOS = roleService.selectRolesByMenuIds(allMenuIds);
        if (CollectionUtil.isNotEmpty(roleDOS)) {
            Set<String> roleIds = roleDOS.stream().map(RoleDO::getId).collect(Collectors.toSet());

            List<UserDO> userDOS = userService.selectAllocatedListByRoleIds(roleIds);

            if (CollectionUtil.isNotEmpty(userDOS)) {
                userDOS.forEach(i -> {
                    // 更新缓存用户信息
                    Set<String> keys = redisTemplate.keys(RedisKeyConstant.USER_TOKEN_KEY + i.getId() + ":*");
                    if (CollectionUtil.isNotEmpty(keys)) {
                        List<LoginVo> loginVos = redisTemplate.opsForValue().multiGet(keys);
                        if (CollectionUtil.isNotEmpty(loginVos)) {
                            loginVos.forEach(loginVo -> {
                                LoginVo newLoginVo = userService.info(i.getUserName());
                                newLoginVo.setToken(loginVo.getToken());
                                redisTemplate.opsForValue().set(RedisKeyConstant.USER_TOKEN_KEY + i.getId() + ":" + loginVo.getToken(), newLoginVo, 6, TimeUnit.HOURS);
                            });
                        }
                    }
                });
            }
        }

    }

    /**
     * 删除菜单管理信息
     *
     * @param menuId 菜单ID
     * @return 结果
     */
    @Override
    public boolean deleteMenuById(String menuId) {
        return menuRepositoryImpl.removeById(menuId);
    }

    /**
     * 内链域名特殊字符替换
     *
     * @return 替换后的内链域名
     */
    public String innerLinkReplaceEach(String path) {
        Map<String, String> replacements = Map.of(
                "http://", "",
                "https://", "",
                "www.", "",
                ".", "/",
                ":", "/"
        );
        for (Map.Entry<String, String> entry : replacements.entrySet()) {
            path = path.replace(entry.getKey(), entry.getValue());
        }
        return path;
    }

    /**
     * 根据角色ID查询权限
     *
     * @param roleId 角色ID
     * @return 权限列表
     */
    @Override
    public Set<String> selectMenuPermsByRoleId(String roleId) {
        List<String> perms = menuRepositoryImpl.selectMenuPermsByRoleId(roleId);
        Set<String> permsSet = new HashSet<>();
        for (String perm : perms) {
            if (StrUtil.isNotBlank(perm)) {
                permsSet.addAll(Arrays.asList(perm.trim().split(",")));
            }
        }
        return permsSet;
    }

    /**
     * 根据用户ID查询权限
     *
     * @param userId 用户ID
     * @return 权限列表
     */
    @Override
    public Set<String> selectMenuPermsByUserId(String userId) {
        List<String> perms = menuRepositoryImpl.selectMenuPermsByUserId(userId);
        Set<String> permsSet = new HashSet<>();
        for (String perm : perms) {
            if (StrUtil.isNotBlank(perm)) {
                permsSet.addAll(Arrays.asList(perm.trim().split(",")));
            }
        }
        return permsSet;
    }

    @Override
    public String fetchMenuTitleLinkedList(MenuTitleForm form) {
        List<MenuDO> menuList = menuRepositoryImpl.selectMenuTreeListByUrlAndPermission(form.getUrl(), form.getPermission());
        if (CollectionUtil.isEmpty(menuList)) {
            return "";
        }

        try {
            Map<String, MenuDO> menuDOMap = menuList.stream().collect(Collectors.toMap(MenuDO::getParentId, m -> m));
            MenuDO rootMenu = menuDOMap.get("0");
            return getChildMenuTitle(rootMenu.getId(), menuDOMap, rootMenu.getMenuName());
        } catch (DuplicateKeyException e) {
            log.error("获取日志标题得到重复数据，无法推算是哪条链路的日志");
            return "";
        }
    }

    private static String getChildMenuTitle(String parentId, Map<String, MenuDO> menuDOMap, String parentName) {
        MenuDO child = menuDOMap.get(parentId);
        if (child == null) {
            return parentName;
        }

        return parentName + "/" + getChildMenuTitle(child.getId(), menuDOMap, child.getMenuName());
    }
}
