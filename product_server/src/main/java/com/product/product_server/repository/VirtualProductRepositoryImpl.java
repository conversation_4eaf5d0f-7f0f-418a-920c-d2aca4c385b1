package com.product.product_server.repository;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.crafts_mirror.common.security.dataPermission.DataPermission;
import com.crafts_mirror.common.security.utils.SecurityUtils;
import com.crafts_mirror.utils.aop.OperationLog;
import com.crafts_mirror.utils.dp.LogTrackNumDto;
import com.crafts_mirror.utils.enums.product.VirtualProductStatusEnum;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.product.product_server.entity.dataObject.ProductCategoryDO;
import com.product.product_server.entity.dataObject.SelfProductDO;
import com.product.product_server.entity.dataObject.SpuProductDO;
import com.product.product_server.entity.dataObject.VirtualProductDO;
import com.product.product_server.entity.excelObject.VirtualProductInfoExcel;
import com.product.product_server.entity.form.VirtualProductSearchForSpuForm;
import com.product.product_server.entity.form.VirtualProductSearchForm;
import com.product.product_server.entity.form.VirtualSkuInfoUnderSpuForm;
import com.product.product_server.entity.vo.VirtualProductForSpuVo;
import com.product.product_server.entity.vo.VirtualProductListVo;
import com.product.product_server.enums.UpgradeStatusEnum;
import com.product.product_server.enums.VirtualProductTypeEnum;
import com.product.product_server.enums.VirtualSubTypeEnum;
import com.product.product_server.mapper.VirtualProductMapper;
import com.product.product_server.model.products.ImportSkuInfoDp;
import com.product.product_server.model.products.ImportVirtualSkuInfoDp;
import com.product.product_server.model.products.VirtualUpdateDp;
import com.product.product_server.model.virtualProduct.VirtualProductInfoDp;
import com.product.product_server.model.virtualProduct.VirtualSkuAndIdDp;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

import static com.crafts_mirror.utils.constant.SecurityConstants.ROLE_DATA_PER_TARGET_SALES;
import static com.crafts_mirror.utils.constant.SecurityConstants.ROLE_DATA_PER_VIR;

/**
 * @Description 虚拟sku相关repository类
 * <AUTHOR>
 * @Date 2023/12/6 14:55
 **/
@Service
public class VirtualProductRepositoryImpl extends ServiceImpl<VirtualProductMapper, VirtualProductDO> {

    public Map<String, List<VirtualSkuAndIdDp>> getVirtualSkuBySelfProductSku(List<String> idList) {
        if (CollectionUtil.isEmpty(idList)) {
            return new HashMap<>();
        }
        List<VirtualProductDO> productDOList = this.baseMapper.selectList(Wrappers.<VirtualProductDO>lambdaQuery()
                .in(VirtualProductDO::getSelfProductSkuId, idList));

        if (CollectionUtil.isEmpty(productDOList)) {
            return new HashMap<>();
        }

        return productDOList.stream()
                .collect(Collectors.toMap(VirtualProductDO::getSelfProductSkuId,
                        pro -> Collections.singletonList(new VirtualSkuAndIdDp(pro.getVirtualSku(), pro.getId())),
                        (v1, v2) -> {
                            List<VirtualSkuAndIdDp> list = new ArrayList<>(v1);
                            list.addAll(v2);
                            return list;
                        }));
    }

    public void saveVirtualProduct(ImportSkuInfoDp importSkuInfo, ImportVirtualSkuInfoDp virtualSkuInfo, String virtualRemarks, String selfProductId, String spuId) {
        VirtualProductDO virtualProduct = convertVirtualProduct(importSkuInfo, virtualSkuInfo, virtualRemarks, selfProductId, spuId);
        if (StrUtil.isNotBlank(virtualProduct.getOldSku())) {
            saveVirtualProductByLambda(virtualProduct,
                    Wrappers.<VirtualProductDO>lambdaQuery().eq(VirtualProductDO::getOldSku, virtualProduct.getOldSku()));
        } else if (StrUtil.isNotBlank(virtualProduct.getVirtualSku())) {
            saveVirtualProductByLambda(virtualProduct,
                    Wrappers.<VirtualProductDO>lambdaQuery().eq(VirtualProductDO::getVirtualSku, virtualProduct.getVirtualSku()));
        }
    }

    public long countBySpu(String checkId) {
        return baseMapper.selectCount(Wrappers.<VirtualProductDO>lambdaQuery().eq(VirtualProductDO::getSpuId, checkId));
    }

    public long countBySelfSku(String checkId) {
        return baseMapper.selectCount(Wrappers.<VirtualProductDO>lambdaQuery().eq(VirtualProductDO::getSelfProductSkuId, checkId));
    }

    public int deleteVirtualProductBySku(String selfSkuId) {
        return baseMapper.delete(Wrappers.<VirtualProductDO>lambdaQuery().eq(VirtualProductDO::getSelfProductSkuId, selfSkuId));
    }

    public List<VirtualProductDO> getAllVirtualProduct(List<String> selfSkuIdList) {
        return baseMapper.selectList(
                Wrappers.<VirtualProductDO>lambdaQuery().in(CollectionUtil.isNotEmpty(selfSkuIdList), VirtualProductDO::getSelfProductSkuId, selfSkuIdList)
        );
    }

    public boolean checkOldSkuVirtualExisted(Wrapper<VirtualProductDO> queryWrapper) {
        return this.baseMapper.selectCount(queryWrapper) >= 1;
    }

    public boolean checkVirtualSkuExisted(String virtualSku) {
        return this.baseMapper.exists(Wrappers.<VirtualProductDO>lambdaQuery().eq(VirtualProductDO::getVirtualSku, virtualSku));
    }

    public VirtualProductDO virtualProductDetail(String virtualProductId) {
        return this.baseMapper.selectById(virtualProductId);
    }

    @Transactional(rollbackFor = Exception.class)
    public int updateVirtualProduct(VirtualUpdateDp updateDp) {
        return this.baseMapper.update(
                Wrappers.<VirtualProductDO>lambdaUpdate()
                        .set(StrUtil.isNotBlank(updateDp.remarks()), VirtualProductDO::getRemarks, updateDp.remarks())
                        .set(updateDp.subType() != null, VirtualProductDO::getSubType, updateDp.subType())
                        .set(updateDp.productStatus() != null, VirtualProductDO::getProductStatus, updateDp.productStatus())
                        .set(updateDp.productType() != null, VirtualProductDO::getProductType, updateDp.productType())
                        .set(StrUtil.isNotBlank(updateDp.operator()), VirtualProductDO::getOperator, updateDp.operator())
                        .set(VirtualProductDO::getUpdateBy, SecurityUtils.getUsername())
                        .set(VirtualProductDO::getUpdateDate, new Date())
                        .eq(VirtualProductDO::getId, updateDp.id())
        );
    }

    @Transactional(rollbackFor = Exception.class)
    public int updateVirtualProductUpgrade(String virtualId, String upgradeId) {
        return this.baseMapper.update(
                Wrappers.<VirtualProductDO>lambdaUpdate()
                        .set(VirtualProductDO::getUpgradeId, upgradeId)
                        .set(VirtualProductDO::getUpdateBy, SecurityUtils.getUsername())
                        .set(VirtualProductDO::getUpdateDate, new Date())
                        .eq(VirtualProductDO::getId, virtualId)
        );
    }

    public int updateVirtualProductUpgradeToNull(List<String> virtualIds) {
        return this.baseMapper.update(
                Wrappers.<VirtualProductDO>lambdaUpdate()
                        .set(VirtualProductDO::getUpgradeId, null)
                        .set(VirtualProductDO::getUpdateBy, SecurityUtils.getUsername())
                        .set(VirtualProductDO::getUpdateDate, new Date())
                        .in(VirtualProductDO::getId, virtualIds)
        );
    }

    @Transactional(rollbackFor = Exception.class)
    public boolean saveVirtualProduct(List<VirtualProductDO> productDOList) {
        return this.saveBatch(productDOList);
    }

    public IPage<VirtualProductDO> getVirtualProductPage(VirtualProductSearchForm form) {
        IPage<VirtualProductDO> page = new Page<>(form.getCurrent(), form.getSize());
        String productName = form.getProductName();
        String buyer = form.getBuyer();
        String dataPermissionSql = new DataPermission().getDataPermissionUser(ROLE_DATA_PER_VIR, "t");
        return baseMapper.selectJoinPage(page, VirtualProductDO.class, new MPJLambdaWrapper<VirtualProductDO>()
                .selectAll(VirtualProductDO.class)
                .select(SpuProductDO::getSpu)
                .select(SpuProductDO::getSpuProductName)
                .leftJoin(SelfProductDO.class, SelfProductDO::getId, VirtualProductDO::getSelfProductSkuId)
                .leftJoin(ProductCategoryDO.class, ProductCategoryDO::getId, SelfProductDO::getCategoryId)
                .leftJoin(SpuProductDO.class, SpuProductDO::getId, VirtualProductDO::getSpuId)
                .like(StrUtil.isNotBlank(productName), SelfProductDO::getProductName, StrUtil.isBlank(productName) ? productName : productName.strip())
                .in(CollectionUtil.isNotEmpty(form.getSelfSkuList()), SelfProductDO::getSku, form.getSelfSkuList())
                .in(CollectionUtil.isNotEmpty(form.getVirtualSkuList()), VirtualProductDO::getVirtualSku, form.getVirtualSkuList())
                .in(CollectionUtil.isNotEmpty(form.getOldSkuList()), VirtualProductDO::getOldSku, form.getOldSkuList())
                .eq(StrUtil.isNotBlank(buyer), SelfProductDO::getBuyer, StrUtil.isBlank(buyer) ? buyer : buyer.strip())
                .eq(StrUtil.isNotBlank(form.getChannel()), VirtualProductDO::getChannel, form.getChannel())
                .eq(NumberUtil.isInteger(form.getSubType()), VirtualProductDO::getSubType, form.getSubType())
                .eq(NumberUtil.isInteger(form.getProductStatus()), VirtualProductDO::getProductStatus, form.getProductStatus())
                .in(CollectionUtil.isNotEmpty(form.getProductStatusList()), VirtualProductDO::getProductStatus, form.getProductStatusList())
                .eq(NumberUtil.isInteger(form.getProductType()), VirtualProductDO::getProductType, form.getProductType())
                .eq(StrUtil.isNotBlank(form.getCategoryId()), ProductCategoryDO::getParentId, form.getCategoryId())
                .isNull(form.getUpgradeStatus() != null && form.getUpgradeStatus().equals(UpgradeStatusEnum.NOT_UPGRADE.getCode()), VirtualProductDO::getUpgradeId)
                .isNotNull(form.getUpgradeStatus() != null && form.getUpgradeStatus().equals(UpgradeStatusEnum.UPGRADE.getCode()), VirtualProductDO::getUpgradeId)
                .like(StrUtil.isNotBlank(form.getSpuName()), SpuProductDO::getSpuProductName, form.getSpuName())
                .in(CollectionUtil.isNotEmpty(form.getSpuIdList()), SpuProductDO::getSpu, form.getSpuIdList())
                .apply(StrUtil.isNotBlank(dataPermissionSql), dataPermissionSql)
                .orderByDesc(VirtualProductDO::getCreateDate));
    }

    public List<VirtualProductDO> getVirtualProductList(VirtualProductSearchForm form) {
        String productName = form.getProductName();
        String buyer = form.getBuyer();
        return baseMapper.selectJoinList(VirtualProductDO.class, new MPJLambdaWrapper<VirtualProductDO>()
                .selectAll(VirtualProductDO.class)
                .leftJoin(SelfProductDO.class, SelfProductDO::getId, VirtualProductDO::getSelfProductSkuId)
                .like(StrUtil.isNotBlank(productName), SelfProductDO::getProductName, StrUtil.isBlank(productName) ? productName : productName.strip())
                .in(CollectionUtil.isNotEmpty(form.getSelfSkuList()), SelfProductDO::getSku, form.getSelfSkuList())
                .in(CollectionUtil.isNotEmpty(form.getFactoryInfoIds()), SelfProductDO::getFactoryId, form.getFactoryInfoIds())
                .in(CollectionUtil.isNotEmpty(form.getVirtualSkuList()), VirtualProductDO::getVirtualSku, form.getVirtualSkuList())
                .eq(StrUtil.isNotBlank(buyer), SelfProductDO::getBuyer, StrUtil.isBlank(buyer) ? buyer : buyer.strip())
                .eq(StrUtil.isNotBlank(form.getChannel()), VirtualProductDO::getChannel, form.getChannel())
                .eq(NumberUtil.isInteger(form.getSubType()), VirtualProductDO::getSubType, form.getSubType())
                .eq(NumberUtil.isInteger(form.getProductStatus()), VirtualProductDO::getProductStatus, form.getProductStatus())
                .eq(NumberUtil.isInteger(form.getProductType()), VirtualProductDO::getProductType, form.getProductType())
                .eq(StrUtil.isNotBlank(form.getDestinationSku()), VirtualProductDO::getVirtualSku, form.getDestinationSku())
                .or()
                .eq(StrUtil.isNotBlank(form.getDestinationSku()), VirtualProductDO::getOldSku, form.getDestinationSku())
                .orderByDesc(VirtualProductDO::getCreateDate));
    }

    public List<VirtualProductDO> getVirtualProductListUnderSpu(VirtualSkuInfoUnderSpuForm form) {
        String dataPermissionSql = new DataPermission().getDataPermissionUser(ROLE_DATA_PER_VIR, "t");
        return baseMapper.selectJoinList(VirtualProductDO.class, new MPJLambdaWrapper<VirtualProductDO>()
                .selectAll(VirtualProductDO.class)
                .leftJoin(SelfProductDO.class, SelfProductDO::getId, VirtualProductDO::getSelfProductSkuId)
                .leftJoin(ProductCategoryDO.class, ProductCategoryDO::getId, SelfProductDO::getCategoryId)
                .eq(StrUtil.isNotBlank(form.getSpu()), VirtualProductDO::getSpuId, form.getSpu())
                .eq(StrUtil.isNotBlank(form.getChannel()), VirtualProductDO::getChannel, form.getChannel())
                .like(StrUtil.isNotBlank(form.getProductName()), SelfProductDO::getProductName, StrUtil.isBlank(form.getProductName()) ? form.getProductName() : form.getProductName().strip())
                .like(StrUtil.isNotBlank(form.getBuyer()), SelfProductDO::getBuyer, form.getBuyer())
                .in(CollectionUtil.isNotEmpty(form.getSelfSkuList()), SelfProductDO::getSku, form.getSelfSkuList())
                .in(CollectionUtil.isNotEmpty(form.getVirtualSkuList()), VirtualProductDO::getVirtualSku, form.getVirtualSkuList())
                .in(CollectionUtil.isNotEmpty(form.getOldSkuList()), VirtualProductDO::getOldSku, form.getOldSkuList())
                .eq(NumberUtil.isInteger(form.getSubType()), VirtualProductDO::getSubType, form.getSubType())
                .eq(NumberUtil.isInteger(form.getProductStatus()), VirtualProductDO::getProductStatus, form.getProductStatus())
                .in(CollectionUtil.isNotEmpty(form.getProductStatusList()), VirtualProductDO::getProductStatus, form.getProductStatusList())
                .eq(NumberUtil.isInteger(form.getProductType()), VirtualProductDO::getProductType, form.getProductType())
                .eq(StrUtil.isNotBlank(form.getCategoryId()), ProductCategoryDO::getParentId, form.getCategoryId())
                .isNull(form.getUpgradeStatus() != null && form.getUpgradeStatus().equals(UpgradeStatusEnum.NOT_UPGRADE.getCode()), VirtualProductDO::getUpgradeId)
                .isNotNull(form.getUpgradeStatus() != null && form.getUpgradeStatus().equals(UpgradeStatusEnum.UPGRADE.getCode()), VirtualProductDO::getUpgradeId)
                .apply(StrUtil.isNotBlank(dataPermissionSql), dataPermissionSql)
                .orderByDesc(VirtualProductDO::getCreateDate));
    }

    public IPage<VirtualProductForSpuVo> getVirtualProductPage(VirtualProductSearchForSpuForm form) {
        IPage<VirtualProductForSpuVo> page = new Page<>(form.getCurrent(), form.getSize());
        return baseMapper.selectJoinPage(page, VirtualProductForSpuVo.class, new MPJLambdaWrapper<VirtualProductDO>()
                .selectAll(VirtualProductDO.class)
                .select(SelfProductDO::getProductName, SelfProductDO::getImage)
                .leftJoin(SelfProductDO.class, SelfProductDO::getId, VirtualProductDO::getSelfProductSkuId)
                .and(wq -> wq.isNull(VirtualProductDO::getSpuId).or().eq(VirtualProductDO::getSpuId, ""))
                .and(wq -> wq
                        .like(StrUtil.isNotBlank(form.getSearchInfo()), SelfProductDO::getProductName, form.getSearchInfo())
                        .or()
                        .like(StrUtil.isNotBlank(form.getSearchInfo()), VirtualProductDO::getVirtualSku, form.getSearchInfo()))
        );
    }

    public boolean deleteVirtualProductByVirtualIds(List<String> virtualProductIds) {
        return this.baseMapper.deleteByIds(virtualProductIds) > 0;
    }

    public boolean deleteSpuInVirtualProduct(List<String> spuIds) {
        this.baseMapper.update(Wrappers.<VirtualProductDO>lambdaUpdate()
                .set(VirtualProductDO::getSpuId, null)
                .set(VirtualProductDO::getUpdateDate, new Date())
                .in(VirtualProductDO::getSpuId, spuIds));
        return true;
    }

    @Transactional(rollbackFor = Exception.class)
    @OperationLog(content = "创建或编辑父SPU", operationType = "父SPU模块")
    public void updateSpuByVirtualId(List<String> virtualSkuList, LogTrackNumDto dto, String spuId) {
        // 先把原来spu下面对应的虚拟sku数据清除，然后再重新填充spu
        this.baseMapper.update(new VirtualProductDO(), Wrappers.<VirtualProductDO>lambdaUpdate()
                .set(VirtualProductDO::getSpuId, null)
                .eq(VirtualProductDO::getSpuId, spuId));

        this.baseMapper.update(new VirtualProductDO(), Wrappers.<VirtualProductDO>lambdaUpdate()
                .set(VirtualProductDO::getSpuId, spuId)
                .in(VirtualProductDO::getVirtualSku, virtualSkuList));
    }

    private void saveVirtualProductByLambda(VirtualProductDO virtualProduct, Wrapper<VirtualProductDO> queryWrapper) {
        VirtualProductDO existedProduct = baseMapper.selectOne(queryWrapper);
        if (existedProduct != null) {
            virtualProduct.setId(existedProduct.getId());
            baseMapper.updateById(virtualProduct);
        } else {
            baseMapper.insert(virtualProduct);
        }
    }

    public VirtualProductDO getVirtualProductByOldSku(String oldSku) {
        return getVirtualProductByLambda(Wrappers.<VirtualProductDO>lambdaQuery().eq(VirtualProductDO::getOldSku, oldSku));
    }

    public VirtualProductDO getVirtualProductByVirtualSku(String virtualSku) {
        return getVirtualProductByLambda(Wrappers.<VirtualProductDO>lambdaQuery().eq(VirtualProductDO::getVirtualSku, virtualSku));
    }

    private VirtualProductDO getVirtualProductByLambda(Wrapper<VirtualProductDO> queryWrapper) {
        return baseMapper.selectOne(queryWrapper);
    }

    private VirtualProductDO convertVirtualProduct(ImportSkuInfoDp importSkuInfo, ImportVirtualSkuInfoDp virtualSkuInfo, String virtualRemarks, String selfProductId, String spuId) {
        return VirtualProductDO.builder()
                .oldSku(importSkuInfo.oldSku())
                .spuId(spuId)
                .channel(virtualSkuInfo.channel())
                .virtualSku(virtualSkuInfo.virtualSku())
                .subType(VirtualSubTypeEnum.ofDesc(virtualSkuInfo.virtualProductSkuStatusDp().subType()).getCode())
                .productStatus(VirtualProductStatusEnum.ofDesc(virtualSkuInfo.virtualProductSkuStatusDp().productStatus()).getCode())
                .productType(VirtualProductTypeEnum.ofDesc(virtualSkuInfo.virtualProductSkuStatusDp().productType()).getCode())
                .remarks(virtualRemarks)
                .selfProductSkuId(selfProductId)
                .operator(virtualSkuInfo.operator())
                .build();
    }

    public List<VirtualProductListVo> getVirtualSkuList(VirtualProductSearchForm form) {
        String dataPermissionSql = null;
        if (form.getNeedDataPermission() == null || form.getNeedDataPermission()) {
            dataPermissionSql = new DataPermission().getDataPermissionUser(ROLE_DATA_PER_TARGET_SALES, "t");
        }

        List<VirtualProductDO> productDOList = baseMapper.selectJoinList(VirtualProductDO.class, new MPJLambdaWrapper<VirtualProductDO>()
                .leftJoin(SelfProductDO.class, SelfProductDO::getId, VirtualProductDO::getSelfProductSkuId)
                .leftJoin(SpuProductDO.class, SpuProductDO::getId, VirtualProductDO::getSpuId)
                .leftJoin(ProductCategoryDO.class, ProductCategoryDO::getId, SelfProductDO::getCategoryId)

                .eq(StrUtil.isNotBlank(form.getChannel()), VirtualProductDO::getChannel, form.getChannel())
                .eq(StrUtil.isNotBlank(form.getSubType()), VirtualProductDO::getSubType, form.getSubType())
                .eq(StrUtil.isNotBlank(form.getOperator()), VirtualProductDO::getOperator, form.getOperator())
                // 品类暂时只查父级
                .eq(StrUtil.isNotBlank(form.getCategoryId()), ProductCategoryDO::getParentId, form.getCategoryId())
                .in(CollectionUtil.isNotEmpty(form.getVirtualSkuList()), VirtualProductDO::getVirtualSku, form.getVirtualSkuList())
                .like(StrUtil.isNotBlank(form.getSpuName()), SpuProductDO::getSpuProductName, form.getSpuName())
                .in(CollectionUtil.isNotEmpty(form.getSpuIdList()), SpuProductDO::getSpu, form.getSpuIdList())
                .like(StrUtil.isNotBlank(form.getProductName()), SelfProductDO::getProductName, form.getProductName())
                .in(CollectionUtil.isNotEmpty(form.getSelfSkuList()), SelfProductDO::getSku, form.getSelfSkuList())

                .apply(StrUtil.isNotBlank(dataPermissionSql), dataPermissionSql)
        );
        return productDOList.stream()
                .map(m -> (VirtualProductListVo) VirtualProductListVo.builder().virtualSkuId(m.getId()).virtualSku(m.getVirtualSku()).build())
                .toList();
    }

    public List<VirtualProductInfoDp> getVirtualProductInfoListByVirtualId(List<String> virtualIdList) {
        return baseMapper.selectJoinList(VirtualProductInfoDp.class, new MPJLambdaWrapper<VirtualProductDO>()
                .selectAs(VirtualProductDO::getId, "virtualSkuId")
                .select(VirtualProductDO::getVirtualSku)
                .select(VirtualProductDO::getChannel)
                .select(VirtualProductDO::getSubType)
                .select(VirtualProductDO::getProductType)
                .select(VirtualProductDO::getOperator)
                .select(VirtualProductDO::getProductStatus)
                .select(SpuProductDO::getSpu)
                .selectAs(SpuProductDO::getSpuProductName, "spuName")
                .select(SelfProductDO::getProductName)
                .select(SelfProductDO::getImage)
                .selectAs(SelfProductDO::getSku, "selfSku")
                .leftJoin(SelfProductDO.class, SelfProductDO::getId, VirtualProductDO::getSelfProductSkuId)
                .leftJoin(SpuProductDO.class, SpuProductDO::getId, VirtualProductDO::getSpuId)
                .in(VirtualProductDO::getId, virtualIdList)
        );
    }

    public List<VirtualProductInfoExcel> getExportInfo(VirtualProductSearchForm form) {
        String productName = form.getProductName();
        String buyer = form.getBuyer();
        String dataPermissionSql = new DataPermission().getDataPermissionUser(ROLE_DATA_PER_VIR, "t");
        return baseMapper.selectJoinList(VirtualProductInfoExcel.class, new MPJLambdaWrapper<VirtualProductDO>()
                .select(SelfProductDO::getSku)
                .select(VirtualProductDO::getChannel)
                .select(SpuProductDO::getSpu)
                .select(SpuProductDO::getSpuProductName)
                .select(VirtualProductDO::getOldSku)
                .select(VirtualProductDO::getOperator)
                .select(VirtualProductDO::getProductStatus)
                .select(VirtualProductDO::getProductType)
                .select(VirtualProductDO::getSubType)
                .select(VirtualProductDO::getVirtualSku)
                .select(VirtualProductDO::getRemarks)
                .select(VirtualProductDO::getBorrowingStrategy)
                .leftJoin(SelfProductDO.class, SelfProductDO::getId, VirtualProductDO::getSelfProductSkuId)
                .leftJoin(ProductCategoryDO.class, ProductCategoryDO::getId, SelfProductDO::getCategoryId)
                .leftJoin(SpuProductDO.class, SpuProductDO::getId, VirtualProductDO::getSpuId)
                .like(StrUtil.isNotBlank(productName), SelfProductDO::getProductName, StrUtil.isBlank(productName) ? productName : productName.strip())
                .in(CollectionUtil.isNotEmpty(form.getSelfSkuList()), SelfProductDO::getSku, form.getSelfSkuList())
                .in(CollectionUtil.isNotEmpty(form.getVirtualSkuList()), VirtualProductDO::getVirtualSku, form.getVirtualSkuList())
                .in(CollectionUtil.isNotEmpty(form.getOldSkuList()), VirtualProductDO::getOldSku, form.getOldSkuList())
                .eq(StrUtil.isNotBlank(buyer), SelfProductDO::getBuyer, StrUtil.isBlank(buyer) ? buyer : buyer.strip())
                .eq(StrUtil.isNotBlank(form.getChannel()), VirtualProductDO::getChannel, form.getChannel())
                .eq(NumberUtil.isInteger(form.getSubType()), VirtualProductDO::getSubType, form.getSubType())
                .eq(NumberUtil.isInteger(form.getProductStatus()), VirtualProductDO::getProductStatus, form.getProductStatus())
                .in(CollectionUtil.isNotEmpty(form.getProductStatusList()), VirtualProductDO::getProductStatus, form.getProductStatusList())
                .eq(NumberUtil.isInteger(form.getProductType()), VirtualProductDO::getProductType, form.getProductType())
                // 品类暂时只查父级
                .eq(StrUtil.isNotBlank(form.getCategoryId()), ProductCategoryDO::getParentId, form.getCategoryId())
                .isNull(form.getUpgradeStatus() != null && form.getUpgradeStatus().equals(UpgradeStatusEnum.NOT_UPGRADE.getCode()), VirtualProductDO::getUpgradeId)
                .isNotNull(form.getUpgradeStatus() != null && form.getUpgradeStatus().equals(UpgradeStatusEnum.UPGRADE.getCode()), VirtualProductDO::getUpgradeId)
                .like(StrUtil.isNotBlank(form.getSpuName()), SpuProductDO::getSpuProductName, form.getSpuName())
                .in(CollectionUtil.isNotEmpty(form.getSpuIdList()), SpuProductDO::getSpu, form.getSpuIdList())
                .apply(StrUtil.isNotBlank(dataPermissionSql), dataPermissionSql)
                .orderByDesc(VirtualProductDO::getCreateDate));
    }

}
