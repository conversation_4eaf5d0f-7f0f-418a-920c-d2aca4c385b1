package com.inventory_server.applications.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.io.Serial;
import java.io.Serializable;


@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class InventoryFactoryRedundantInfoDto implements Serializable {

    @Serial
    private static final long serialVersionUID = 6840632203100758722L;
    private String inventoryInfoId;
    private Double redundantNum;
    private String watchBoardId;
    private String factoryPlanId;
    private String contractCode;
    private String factoryFinishedDate;
    private String destinationSku;
    /**
     * 状态
     * @see com.inventory_server.model.redundancy.enums.FactoryPlanInfoEnum
     */
    private String  isProduct;
}
