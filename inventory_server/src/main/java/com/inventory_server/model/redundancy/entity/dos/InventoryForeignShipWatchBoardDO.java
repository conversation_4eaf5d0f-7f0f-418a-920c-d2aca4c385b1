package com.inventory_server.model.redundancy.entity.dos;
import com.inventory_server.infrastructures.entity.PhysicalBaseEntity;
import lombok.Data;
import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.time.LocalDate;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.experimental.SuperBuilder;


@EqualsAndHashCode(callSuper = true)
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@TableName("cm_inventory_foreign_ship_watch_board")
public class InventoryForeignShipWatchBoardDO extends PhysicalBaseEntity {

  private String inventoryInfoId;
  private Integer type;
  private LocalDate arrivingDate;
  private Integer arrivingNum;
  private LocalDate judgeDate;
  private Double initialInventory;
  private Double safeInventory;
  private Integer initialTotalRedundancy;
  private Integer redundantNum;
  private Integer redundantType;
//  private Integer totalRedundantType;

}
