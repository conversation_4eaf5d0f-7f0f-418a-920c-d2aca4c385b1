package com.inventory_server.model.redundancy.entity.form;

import com.inventory_server.model.redundancy.entity.dto.NormalDeliveryInventorySaveDto;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/10/23 11:53
 **/
@Data
@Builder
public class NormalDeliveryInventoryForm implements Serializable {
    private List<NormalDeliveryInventorySaveDto> list;

    private Boolean needDelete;
}
