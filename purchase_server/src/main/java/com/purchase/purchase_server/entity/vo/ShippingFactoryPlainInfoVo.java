package com.purchase.purchase_server.entity.vo;

import com.purchase.purchase_server.entity.dto.delivery.ShippingDetailPlainDto;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;
import java.util.List;

/**
 * @Description 发货详情页计划数据vo类
 * <AUTHOR>
 * @Date 2024/3/26 15:02
 **/
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class ShippingFactoryPlainInfoVo implements Serializable {

    private List<ShippingDetailPlainDto> shippingDetailPlainDtoList;
}
