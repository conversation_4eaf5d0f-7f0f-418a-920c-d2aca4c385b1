package com.inventory_server.model.redundancy.listener.redundant;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.context.AnalysisContext;
import com.crafts_mirror.utils.provider.ApplicationContextProvider;
import com.inventory_server.applications.dto.FactoryRemainInventoryDto;
import com.inventory_server.model.product.entity.dos.VirtualProductDO;
import com.inventory_server.model.product.repository.databaseRepository.VirtualProductRepositoryImpl;
import com.inventory_server.model.redundancy.entity.dp.AmStockQuantityDp;
import com.inventory_server.model.redundancy.entity.dp.StockQuantityInfoOtherDp;
import com.inventory_server.model.redundancy.entity.vo.UpgradeInfoInteriorVo;
import com.inventory_server.model.redundancy.enums.StockQuantityImportSheetNameEnum;
import com.inventory_server.model.warehouse.entity.dto.SenboWarehouseDto;
import lombok.extern.slf4j.Slf4j;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description 导入补货计划-库存
 * <AUTHOR>
 * @Date 2024/1/16 15:49
 **/
@Slf4j
public class InventoryStockQuantityOtherImportListener extends AbstractInventoryImportListener<HashMap<Integer, String>> {

    public InventoryStockQuantityOtherImportListener(String fileName, List<String> errorList, List<SenboWarehouseDto> senboWarehouseList,
                                                     Map<String, AmStockQuantityDp> stockQuantityDpMap, DateTime dateTime,
                                                     Map<String, String> channelMapping, Map<String, String> channelIdNameMap) {
        super(fileName, errorList);
        this.stockQuantityDpMap = stockQuantityDpMap;
        this.dateTime = dateTime;
        this.channelMapping = channelMapping;
        this.channelIdNameMap = channelIdNameMap;
        this.warehouseNameIdMap = senboWarehouseList.stream().collect(Collectors.toMap(SenboWarehouseDto::getSenboWarehouse, c -> String.valueOf(c.getSenboWarehouseId())));
    }

    Map<String, AmStockQuantityDp> stockQuantityDpMap;
    DateTime dateTime;

    private final HashMap<Integer, String> stockQuantityHeadMap = new HashMap<>();

    private final Map<String, String> channelMapping;
    private final Map<String, String> channelIdNameMap;
    private final Map<String, String> warehouseNameIdMap;



    private final VirtualProductRepositoryImpl virtualProductRepositoryImpl = ApplicationContextProvider.getBean(VirtualProductRepositoryImpl.class);

    private final List<String> amChannelList = List.of("AM", "AC", "AMEU", "AK");

    private final List<String> lampChannelList1 = List.of("AM", "AC");

    private final Set<String> lampChannelSet2 = Set.of("AMEU", "AMEUSS", "AMEU2", "AMEU3");

    @Override
    public void invoke(HashMap<Integer, String> data, AnalysisContext analysisContext) {
//        log.warn("导入发货计划-------------导入excel进行中，成功解析excel");
        Integer approximateRowNumber = analysisContext.readSheetHolder().getApproximateTotalRowNumber();
        if (approximateRowNumber != null && approximateRowNumber > TOTAL_ROW_NUMBER) {
            throw new RuntimeException(String.format("导入数据超过%d行，请缩小导入数据量", TOTAL_ROW_NUMBER));
        }
        //动态读取 excel逻辑删除造成数据null问题
        data.entrySet().removeIf(entry -> entry.getKey() > this.stockQuantityHeadMap.size() - 1);
        new StockQuantityInfoOtherDp(data);
        // 替换数据格式
        HashMap<String, String> product = new HashMap<>();

        String destinationSku = data.get(0);
        VirtualProductDO virtualProductDO = virtualProductRepositoryImpl.getOneByVirtualSkuOrOldSku(destinationSku);
        if (virtualProductDO == null) {
            throw new IllegalArgumentException(String.format("虚拟sku %s，不存在", destinationSku));
        }
        final double ZERO = 0.0;
        double ameuNum;
        double ameuKNum;
        for (Map.Entry<Integer, String> entry : new HashMap<>(data).entrySet()) {
            Integer key = entry.getKey();
            String value = entry.getValue();
            // 根据第二个HashMap中的映射关系，将key替换为对应的value
            if (stockQuantityHeadMap.containsKey(key)) {
                String channel = channelIdNameMap.getOrDefault(virtualProductDO.getChannel(), virtualProductDO.getChannel());
                String virtualChannel = channelMapping.getOrDefault(channel, channel);
                String head = stockQuantityHeadMap.get(key);
                double storeNum = NumberUtil.parseDouble(product.getOrDefault(head, "0"));
                if (analysisContext.readSheetHolder().getSheetName().equals(StockQuantityImportSheetNameEnum.AM_STOCK_QUANTITY.getCode())) {
                    if (key > 0 && key <= amChannelList.size()) {
                        String amChannel = amChannelList.get(key - 1);
                        double channelValue = Double.parseDouble(data.get(key));
                        if (channelValue > ZERO && !virtualChannel.equals(amChannel)) {
                            throw new IllegalArgumentException("产品渠道与仓库渠道不一致");
                        }
                        double totalValue = storeNum;
                        if (virtualChannel.equals(amChannel)) {
                            double additionalValue = NumberUtil.parseDouble(data.get(5)) + NumberUtil.parseDouble(data.get(6));
                            totalValue += NumberUtil.parseDouble(value) + additionalValue;
                        }
                        product.put(head, String.valueOf(totalValue));
                    } else if (key > amChannelList.size() + 2) {
                        Double totalValue = NumberUtil.parseDouble(value) + storeNum;
                        product.put(head, String.valueOf(totalValue));
                    }
                } else if (analysisContext.readSheetHolder().getSheetName().equals(StockQuantityImportSheetNameEnum.LAMP_STOCK_QUANTITY.getCode())) {
                    if (key > 0 && key <= lampChannelList1.size()) {
                        String lampChannel = lampChannelList1.get(key - 1);
                        double channelValue = Double.parseDouble(data.get(key));
                        if (channelValue > ZERO && !virtualChannel.equals(lampChannel)) {
                            throw new IllegalArgumentException("产品渠道与仓库渠道不一致");
                        }
                        double totalValue = storeNum;
                        if (virtualChannel.equals(lampChannel)) {
                            totalValue += NumberUtil.parseDouble(value) + NumberUtil.parseDouble(data.get(3));
                        }
                        product.put(head, String.valueOf(totalValue));
                    } else if (key > lampChannelList1.size() && key < lampChannelList1.size() + lampChannelSet2.size()) {
                        double channelValue = Double.parseDouble(data.get(key));
                        if (channelValue > ZERO && !lampChannelSet2.contains(virtualChannel)) {
                            throw new IllegalArgumentException("产品渠道与仓库渠道不一致");
                        }
                        if (!lampChannelSet2.contains(virtualChannel)) {
                            continue;
                        }
                        String AMEU_WAREHOUSE = "FBA(AMEU)";
                        String AMEU_K_WAREHOUSE = "FBA(AMEU)-K";
                        if (warehouseNameIdMap.get(AMEU_WAREHOUSE).equals(head)) {
                            ameuNum = NumberUtil.parseDouble(value);
                            double finalAmeuNum = ameuNum;
                            product.compute(head, (k, v) -> String.valueOf(finalAmeuNum > 0 ? finalAmeuNum + NumberUtil.parseDouble(data.get(5)) : 0));
                        } else if (warehouseNameIdMap.get(AMEU_K_WAREHOUSE).equals(head)){
                            ameuKNum = NumberUtil.parseDouble(value);
                            if (Double.parseDouble(product.get(warehouseNameIdMap.get(AMEU_WAREHOUSE))) > 0) {
                                product.put(head, String.valueOf(ameuKNum));
                            } else if (ameuKNum > 0){
                                product.put(head, String.valueOf(ameuKNum + NumberUtil.parseDouble(data.get(5))));
                            } else {
                                product.compute(warehouseNameIdMap.get(AMEU_WAREHOUSE), (k, v) -> String.valueOf(NumberUtil.parseDouble(data.get(5))));
                            }
                        }
                    } else if (key > lampChannelList1.size() + lampChannelSet2.size() + 1) {
                        product.put(head, String.valueOf(NumberUtil.parseDouble(value) + storeNum));
                    }
                }
            }
        }
        
        String resultSku = virtualProductDO.getVirtualSku();
        if (StrUtil.isNotBlank(virtualProductDO.getUpgradeId())) {
            UpgradeInfoInteriorVo upgradeInfoInteriorVo = selectUpgradeInfo(virtualProductDO.getUpgradeId());

            if (ObjectUtil.isNotEmpty(upgradeInfoInteriorVo)) {
                if (upgradeInfoInteriorVo.getOriginalSkuId().equals(virtualProductDO.getId())) {
                    resultSku = upgradeInfoInteriorVo.getUpgradeSku();
                }
            }
        }
        String finalResultSku = resultSku;
        for (Map.Entry<String, String> entry : product.entrySet()) {
            if (!entry.getKey().equals("虚拟SKU")) {
                String warehouse = entry.getKey();
                String storeNum = entry.getValue();
                FactoryRemainInventoryDto factoryRemainInventoryDto = FactoryRemainInventoryDto.builder()
                        .virtualSku(destinationSku.toUpperCase())
                        .warehouse(warehouse)
                        .enableUsingDate(dateTime)
                        .storeNum(Double.valueOf(storeNum)).build();


                stockQuantityDpMap.entrySet().stream()
                        .filter(stockEntry -> stockEntry.getKey().equals(finalResultSku))
                        .findFirst()
                        .ifPresentOrElse(stockEntry -> stockEntry.getValue().addStockQuantity(factoryRemainInventoryDto), () -> {
                            AmStockQuantityDp quantityDp = new AmStockQuantityDp(finalResultSku, new ArrayList<>());
                            quantityDp.addStockQuantity(factoryRemainInventoryDto);
                            stockQuantityDpMap.put(finalResultSku, quantityDp);
                        });
            }
        }
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext analysisContext) {

    }

    /* 这里会一行行的返回头
     *
     * @param headMap
     * @param context
     */
    @Override
    public void invokeHeadMap(Map<Integer, String> headMap, AnalysisContext context) {
        //删除headMap中的空值
        headMap.values().removeIf(value -> value == null || value.isEmpty());
        List<String> headMapList = new ArrayList<>(headMap.values());

        String sheetName = context.readSheetHolder().getSheetName();
        for (int i = 0; i < headMapList.size(); i++) {
            String headValue = headMapList.get(i);
            boolean useNameIdMap = isUseNameIdMap(i, headValue, sheetName);

            if (useNameIdMap) {
                if (!warehouseNameIdMap.containsKey(headValue)) {
                    throw new IllegalArgumentException(sheetName + "表头错误，" + headValue + "不存在，请检查表头是否正确");
                }
                this.stockQuantityHeadMap.put(i, warehouseNameIdMap.get(headValue));
            } else {
                this.stockQuantityHeadMap.put(i, headValue);
            }

        }
    }

    private boolean isUseNameIdMap(int i, String headValue, String sheetName) {
        if (i == 0 && !headValue.equals("虚拟SKU")) {
            throw new RuntimeException(sheetName + "表头错误，请检查表头是否正确");
        }

        boolean isAMInventorySheet = sheetName.equals(StockQuantityImportSheetNameEnum.AM_STOCK_QUANTITY.getCode());
        if (isAMInventorySheet && (i == 5 || i == 6)) {
            String expectedHeader = switch (i) {
                case 5 -> "正在接收中";
                case 6 -> "预留库存（转运+处理中）";
                default -> throw new IllegalStateException("Unexpected value: " + i);
            };
            if (!headValue.equals(expectedHeader)) {
                throw new IllegalArgumentException(sheetName + "表头错误，请检查表头是否正确");
            }
        }

        //灯具
        boolean isLampInventorySheet = sheetName.equals(StockQuantityImportSheetNameEnum.LAMP_STOCK_QUANTITY.getCode());
        if (isLampInventorySheet && i == 5) {
            if (!headValue.equals("预留库存（转运+处理中）")) {
                throw new IllegalArgumentException(sheetName + "表头错误，请检查表头是否正确");
            }
        }

        return (isAMInventorySheet && ((i > 0 && i <= amChannelList.size()) || i > amChannelList.size() + 2)) ||
                (isLampInventorySheet && ((i >= 1 && i <= 4) || i > 5));
    }
}
