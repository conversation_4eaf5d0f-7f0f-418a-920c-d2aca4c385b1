package com.purchase.purchase_server.entity.dto.Lcl;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;


@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class LclDataSevenDayDTO {

    /**
     * 区间-仓的总体积
     */
    private BigDecimal totalVolume;

    /**
     * 区间-有发货比例并且发货的虚拟sku
     */
    private Map<String , Map<String, Double>> virtualRatio;

    /**
     * 区间-有发货比例并且发货的虚拟sku体积
     */
    private Map<String , BigDecimal> virtualVolume;

    /**
     * 发货数据
     */
    private List<LclTrialShippingInventoryDTO> sortedSevenList;
}