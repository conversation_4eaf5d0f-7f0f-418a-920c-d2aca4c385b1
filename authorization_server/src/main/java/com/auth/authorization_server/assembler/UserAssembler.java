package com.auth.authorization_server.assembler;

import com.auth.authorization_server.model.dataObject.UserDO;
import com.auth.authorization_server.domain.vo.UserVO;
import com.auth.authorization_server.domain.dto.UserDTO;
import org.mapstruct.Mapper;

import java.util.List;


/**
 * 数据转换工具类
 *
 * <AUTHOR>
 * @date 2024/5/11
 */

@Mapper(componentModel = "spring", uses = UserConvert.class)
public interface UserAssembler {
    UserVO userDoToVo(UserDO val);

    List<UserVO> userDoListToVo(List<UserDO> val);

    UserDO userDtoToDo(UserDTO val);
}














