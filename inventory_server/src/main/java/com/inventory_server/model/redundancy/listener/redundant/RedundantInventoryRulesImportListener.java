package com.inventory_server.model.redundancy.listener.redundant;


import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.metadata.data.ReadCellData;
import com.alibaba.excel.util.ConverterUtils;
import com.crafts_mirror.utils.provider.ApplicationContextProvider;
import com.inventory_server.model.interiorRepository.ShippingRatioRepository;
import com.inventory_server.model.product.entity.dos.VirtualProductDO;
import com.inventory_server.model.product.repository.databaseRepository.VirtualProductRepositoryImpl;
import com.inventory_server.model.redundancy.entity.dp.InvOrRepDaysRulesDp;
import com.inventory_server.model.redundancy.entity.dp.InventoryRulesDp;
import com.inventory_server.model.redundancy.entity.dp.ShippingRulesDp;
import com.inventory_server.model.redundancy.entity.dto.ImportInventoryRulesDto;
import com.inventory_server.model.redundancy.entity.vo.UpgradeInfoInteriorVo;
import com.inventory_server.model.warehouse.entity.dto.SenboWarehouseDto;
import com.inventory_server.model.warehouse.entity.vo.SenboWarehouseVo;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;

import static com.inventory_server.model.redundancy.enums.RedundantInventoryRulesImportHeadEnum.*;


/**
 * @Description 导入补货计划-备货规则
 * <AUTHOR>
 * @Date 2024/1/15 15:49
 **/
@Slf4j
public class RedundantInventoryRulesImportListener extends AbstractInventoryImportListener<Map<Integer, String>> {

    public RedundantInventoryRulesImportListener(String fileName, List<String> errorList, ImportInventoryRulesDto importInventoryRulesDto) {
        super(fileName, errorList);
        this.importInventoryRulesDto = importInventoryRulesDto;
    }

    private final Map<Integer, String> rulesHeadMap = new HashMap<>(32);

    private final ImportInventoryRulesDto importInventoryRulesDto;

    private final Set<String> existedSku = new HashSet<>();

    private final VirtualProductRepositoryImpl virtualProductRepositoryImpl = ApplicationContextProvider.getBean(VirtualProductRepositoryImpl.class);

    private final ShippingRatioRepository shippingRatioRepository = ApplicationContextProvider.getBean(ShippingRatioRepository.class);


    @Override
    public void invoke(Map<Integer, String> data, AnalysisContext analysisContext) {
        Integer approximateRowNumber = analysisContext.readSheetHolder().getApproximateTotalRowNumber();
        if (approximateRowNumber != null && approximateRowNumber > TOTAL_ROW_NUMBER) {
            throw new RuntimeException(String.format("导入数据超过%d行，请缩小导入数据量", TOTAL_ROW_NUMBER));
        }

        // 替换数据格式
        Map<String, String> keyReplacementMap = new HashMap<>();
        for (Map.Entry<Integer, String> entry : new HashMap<>(data).entrySet()) {
            Integer key = entry.getKey();
            String value = entry.getValue();
            // 根据第二个HashMap中的映射关系，将key替换为对应的value
            if (rulesHeadMap.containsKey(key)) {
                data.remove(key);
                keyReplacementMap.put(rulesHeadMap.get(key), value);
            }
        }
        String virtualSku = keyReplacementMap.get("*虚拟SKU");
        VirtualProductDO virtualProductDO = virtualProductRepositoryImpl.getOneByVirtualSkuOrOldSku(virtualSku);
        if (virtualProductDO == null) {
            throw new IllegalArgumentException(String.format("虚拟sku %s，不存在", virtualSku));
        }

        if (StrUtil.isNotBlank(virtualProductDO.getUpgradeId())) {
            UpgradeInfoInteriorVo upgradeInfoInteriorVo = selectUpgradeInfo(virtualProductDO.getUpgradeId());
            if (ObjectUtil.isNotEmpty(upgradeInfoInteriorVo)) {
                if (upgradeInfoInteriorVo.getOriginalSkuId().equals(virtualProductDO.getId())) {
                    importInventoryRulesDto.getInventoryRulesUpgradeMap().put(upgradeInfoInteriorVo.getOriginalSku(), upgradeInfoInteriorVo.getUpgradeSku());
                    return;
                }
            }
        }

        // 获取发货比例的哈希表
        SenboWarehouseVo virtualShippingRatio = shippingRatioRepository.getVirtualShippingRatio(virtualProductDO.getId());

        List<SenboWarehouseDto> warehouseList = virtualShippingRatio.getSenboWarehouseList();

        Map<String, Double> shippingRatioMap = new HashMap<>();
        warehouseList.forEach(warehouse -> {
            BigDecimal ratio = BigDecimal.valueOf(warehouse.getShippingRatio());
            BigDecimal result = ratio.divide(new BigDecimal("100"), 3, RoundingMode.HALF_UP);
            shippingRatioMap.put(String.valueOf(warehouse.getSenboWarehouseId()), result.doubleValue());
        });
        ShippingRulesDp shippingRulesDp = new ShippingRulesDp(shippingRatioMap);
        InvOrRepDaysRulesDp daysRulesDp = getDaysRulesDp(keyReplacementMap);
        importInventoryRulesDto.getInventoryRulesDpMap().put(virtualSku, new InventoryRulesDp(virtualSku, daysRulesDp, shippingRulesDp));

        if (virtualSku.equals(virtualProductDO.getOldSku())) {
            throw new IllegalArgumentException(String.format("不可以填写老sku：%s", virtualSku));
        }

        if (!existedSku.add(virtualSku)) {
            throw new IllegalArgumentException(String.format("虚拟sku %s 重复", virtualSku));
        }

        if (ObjectUtil.isEmpty(virtualShippingRatio) || CollectionUtil.isEmpty(virtualShippingRatio.getVirtualShippingRatioDtoList())) {
            throw new IllegalArgumentException("虚拟sku:" + virtualProductDO.getVirtualSku() + "发货比例不存在");
        }

        double sum = shippingRatioMap.values().stream().filter(Objects::nonNull).mapToDouble(i -> i).sum();
        if (BigDecimal.valueOf(sum).setScale(3, RoundingMode.HALF_UP).doubleValue() != 1.0){
            throw new IllegalArgumentException("发货比例总和不为100%");
        }

        if (daysRulesDp.safeDays() == 0) {
            throw new IllegalArgumentException("安全库存为期初库存，安全天数最小值为1");
        }

        if (daysRulesDp.shippingCircle() == 0) {
            throw new IllegalArgumentException("发货周期最小值为1");
        }

        if (daysRulesDp.purchaseCircle() == 0) {
            throw new IllegalArgumentException("采购周期最小值为1");
        }
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext analysisContext) {

    }

    /**
     * 这里会一行行的返回头
     *
     * @param headMap 表头map
     * @param context 上下文
     */
    @Override
    public void invokeHead(Map<Integer, ReadCellData<?>> headMap, AnalysisContext context) {
        Map<Integer, String> map = ConverterUtils.convertToStringMap(headMap, context);
        //删除map中的空值
        map.values().removeIf(Objects::isNull);
        List<String> headList = getCodeList();
        List<String> headMapList = new ArrayList<>(map.values());

        //比较两个list集合值是否相等
        if (!headList.equals(headMapList)) {
            throw new RuntimeException("备货规则表头错误，请检查表头是否正确");
        }
        for (Map.Entry<Integer, String> entry : map.entrySet()) {
            if (isExist(entry.getValue())) {
                Integer key = entry.getKey();
                String value = entry.getValue();
                if (ofCode(entry.getValue()) != null) {
                    rulesHeadMap.put(key, ofCode(value).getCode());
                }
            } else {
                throw new RuntimeException("备货规则表头错误，请检查表头是否正确");
            }
        }
    }

    private InvOrRepDaysRulesDp getDaysRulesDp(Map<String, String> map) {
        int purchaseDays = convertToInt(map.get(PURCHASE_DAYS.getCode()), "采购下单天数");
        int transitDays = convertToInt(map.get(TRANSIT_DAYS.getCode()), "国内中转天数");
        int safeDays = convertToInt(map.get(SAFE_DAYS.getCode()), "安全天数");
        int repFrequency = convertToInt(map.get(REP_FREQUENCY.getCode()), "发货周期");
        int purchaseCycle = convertToInt(map.get(PURCHASE_CYCLE.getCode()), "采购周期");

        return new InvOrRepDaysRulesDp(purchaseDays, transitDays, safeDays, repFrequency, purchaseCycle);
    }

    private int convertToInt(String value, String key) {
        try {
            if (StrUtil.isNotBlank(value)) {
                int intValue = Integer.parseInt(value);
                if (intValue >= 0) {
                    return intValue;
                }
                throw new IllegalArgumentException(key + "-请正确填写非负整数");
            } else {
                throw new IllegalArgumentException(key + "不能为空");
            }
        } catch (NumberFormatException e) {
            throw new IllegalArgumentException(key + "-请正确填写非负整数");
        }
    }
}
