package com.sales_server.model.targetSales.entity.form;

import com.sales_server.model.targetSales.entity.dto.TargetDaySalesDto;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * @Description 编辑目标日销表单
 * <AUTHOR>
 * @Date 2025/3/11 17:13
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DaySalesEditForm implements Serializable {
    private String virtualSkuId;

    private List<TargetDaySalesDto> targetDaySalesList;

    private String content;
}
