package com.purchase.purchase_server.entity.form;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * @Description 排柜拼柜列表页搜索表单
 * <AUTHOR>
 * @Date 2025/5/25 11:24
 **/
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
public class LclSearchPageForm extends com.crafts_mirror.utils.dp.BasePageForm implements Serializable {

    /**
     * cm_lcl_consolidation_record 主键
     */
    private String recordId;

    /**
     * 柜子id
     */
    private String containerId;

    private List<String> virtualSkuList;
    private List<String> selfSkuList;
    private List<String> oldSkuList;
    private List<String> spuIdList;
    private List<String> productStatusList;
    private String productName;
    private String spuProductName;
    private String contractCode;
    private String factoryCode;
    private String shipmentCode;

    private String factoryFinishedStartDate;
    private String factoryFinishedEndDate;

//    private String shippingStartDate;
//    private String shippingEndDate;

    /**
     * 单箱是否装满
     */
    private Integer isPackageFull;

    /**
     * 货件来源
     */
    private String sourceType;
    /**
     * 地址代号
     */
    private String addressCode;
    /**
     * 目的仓
     */
    private String destinationWarehouse;
    /**
     * 货件状态
     */
    private Integer shipmentStatus;
    /**
     * 装载状态
     */
    private Integer loadingStatus;
    /**
     * 运输方式
     */
    private Integer transportationMode;

    /**
     * 需要搜索商品、供应商快照信息
     * @return 若有下列任一搜索项即返回true
     */
    public boolean needSearchSnapshotInfo() {
        return CollectionUtil.isNotEmpty(virtualSkuList) ||
                CollectionUtil.isNotEmpty(selfSkuList) ||
                CollectionUtil.isNotEmpty(oldSkuList) ||
                CollectionUtil.isNotEmpty(spuIdList) ||
                CollectionUtil.isNotEmpty(productStatusList) ||
                StrUtil.isNotBlank(productName) ||
                StrUtil.isNotBlank(spuProductName) ||
                StrUtil.isNotBlank(factoryCode);
    }

    /**
     * 需要搜索工厂交货信息
     * @return 若需要搜索下列任一信息则返回true
     */
    public boolean needSearchDetailInfo() {
        return StrUtil.isNotBlank(factoryFinishedStartDate) ||
                StrUtil.isNotBlank(factoryFinishedEndDate) ||
//                StrUtil.isNotBlank(shippingStartDate) ||
//                StrUtil.isNotBlank(shippingEndDate) ||
                StrUtil.isNotBlank(containerId) ||
                StrUtil.isNotBlank(contractCode) ||
                isPackageFull != null;
    }

    public boolean needSearchDetail() {
        return needSearchSnapshotInfo() || needSearchDetailInfo();
    }
}
