package com.purchase.purchase_server.entity.dataObject;

import com.baomidou.mybatisplus.annotation.TableName;
import com.purchase.purchase_server.entity.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.Date;


/**
 * 在途以及海外仓库存数据
 *
 * @Date 2024/5/8 9:15
 **/
@EqualsAndHashCode(callSuper = true)
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@TableName("cm_delivery_foreign_inventory")
public class DeliveryForeignInventoryDO extends BaseEntity {

    private Date enableUsingDate;
    private String warehouseId;
    private String shipmentCode;
    private String virtualSku;
    private String shippingProjectId;
    private Integer storeNum;
    /**
     * 出货时间
     */
    private Date startShippingDate;

  /**
   * 发货类型
   * @see com.purchase.purchase_server.enums.DeliveryTypeEnum
   */
  private Integer deliveryType;

  /**
   * 备注
   */
  private String remarks;
    /**
     * 虚拟skuId
     */
    private String virtualSkuId;
    /**
     * sku类型
     *
     * @see com.purchase.purchase_server.enums.IsOldStatusEnum
     */
    private String isOldStatus;
    /**
     * 快照id
     */
    private String productSnapshotId;
}
