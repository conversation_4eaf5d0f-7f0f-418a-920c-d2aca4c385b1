package com.product.product_server.utils.easyExcelUtil.listener;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.metadata.data.ReadCellData;
import com.alibaba.excel.util.ConverterUtils;
import com.crafts_mirror.utils.enums.product.VirtualProductStatusEnum;
import com.product.product_server.entity.dataObject.VirtualProductDO;
import com.product.product_server.entity.excelObject.ProductStatusExcel;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;
import java.util.Objects;

/**
 * @Description 更新子体类型
 * <AUTHOR>
 * @Date 2024/8/8 11:15
 **/
@Slf4j
public class ProductStatusUpdateListener extends AbstractUpdateVirtualProductInfoListener<ProductStatusExcel>{
    public ProductStatusUpdateListener(String fileName, String importExcelId) {
        super(fileName, importExcelId);
    }

    @Override
    public void invoke(ProductStatusExcel data, AnalysisContext context) {
        log.warn("更新产品状态-------------导入excel进行中，成功解析excel");
        Integer approximateRowNumber = context.readSheetHolder().getApproximateTotalRowNumber();
        if(approximateRowNumber != null && approximateRowNumber > 10000) {
            throw new RuntimeException("导入数据超过10000行，请缩小导入数据量");
        }

        VirtualProductDO info = getVirtualProductInfo(data.getVirtualSku());
        VirtualProductStatusEnum productStatus = VirtualProductStatusEnum.ofDesc(data.getProductStatus());
        if (productStatus == null) {
            throw new IllegalArgumentException("产品状态为空或者系统未找到对应的产品状态");
        }

        updateVirtualProduct(info.getId(), null, productStatus.getCode(), null, null);
    }

    @Override
    public void invokeHead(Map<Integer, ReadCellData<?>> headMap, AnalysisContext context) {
        Map<Integer,String> map = ConverterUtils.convertToStringMap(headMap, context);
        for (Map.Entry<Integer, String> entry : map.entrySet()) {
            if (map.size() == 2){
                String value = entry.getValue();
                if (!Objects.equals(value, "* 虚拟SKU") && !Objects.equals(value, "* 产品状态")) {
                    throw new RuntimeException("表头错误，请检查表头是否正确");
                }
            }else {
                throw new RuntimeException("表头错误，请检查表头是否正确");
            }
        }
        super.invokeHead(headMap, context);
    }
}
