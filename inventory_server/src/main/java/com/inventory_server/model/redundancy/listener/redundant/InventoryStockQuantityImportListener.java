package com.inventory_server.model.redundancy.listener.redundant;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.context.AnalysisContext;
import com.crafts_mirror.utils.provider.ApplicationContextProvider;
import com.inventory_server.model.product.entity.dos.VirtualProductDO;
import com.inventory_server.model.product.repository.databaseRepository.VirtualProductRepositoryImpl;
import com.inventory_server.model.redundancy.entity.dp.StockQuantityDp;
import com.inventory_server.model.redundancy.entity.excelObj.InventoryStockQuantityInfoExcel;
import com.inventory_server.model.redundancy.entity.vo.UpgradeInfoInteriorVo;
import com.inventory_server.model.redundancy.enums.StockQuantityImportHeadEnum;
import com.inventory_server.model.warehouse.entity.dto.SenboWarehouseDto;
import com.inventory_server.model.warehouse.service.IWarehouseService;
import lombok.extern.slf4j.Slf4j;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description 导入补货计划-库存
 * <AUTHOR>
 * @Date 2024/1/16 15:49
 **/
@Slf4j
public class InventoryStockQuantityImportListener extends AbstractInventoryImportListener<InventoryStockQuantityInfoExcel> {

    public InventoryStockQuantityImportListener(String fileName, List<String> errorList, Map<String, StockQuantityDp> stockQuantityDpMap) {
        super(fileName, errorList);
        this.stockQuantityDpMap = stockQuantityDpMap;
    }

    Map<String, StockQuantityDp> stockQuantityDpMap;

    private Map<String, Integer> nameIdMap = new HashMap<>();

    private final IWarehouseService warehouseService = ApplicationContextProvider.getBean(IWarehouseService.class);

    private final VirtualProductRepositoryImpl virtualProductRepositoryImpl = ApplicationContextProvider.getBean(VirtualProductRepositoryImpl.class);

    @Override
    public void invoke(InventoryStockQuantityInfoExcel product, AnalysisContext analysisContext) {
        Integer approximateRowNumber = analysisContext.readSheetHolder().getApproximateTotalRowNumber();
        if (approximateRowNumber != null && approximateRowNumber > TOTAL_ROW_NUMBER) {
            throw new RuntimeException(String.format("导入数据超过%d行，请缩小导入数据量", TOTAL_ROW_NUMBER));
        }

        if (StrUtil.isBlank(product.getShipmentCode())) {
            throw new IllegalArgumentException("货件号不能为空");
        }

        if (product.getShipmentCode().length() > 30) {
            throw new IllegalArgumentException("货件号长度超过30");
        }

        String destinationSku = product.getVirtualSku();
        VirtualProductDO virtualProductDO = virtualProductRepositoryImpl.getOneByVirtualSkuOrOldSku(destinationSku);
        if (virtualProductDO == null) {
            throw new IllegalArgumentException(String.format("虚拟sku %s，不存在", destinationSku));
        }
        product.setStoreHouse(Optional.ofNullable(nameIdMap.get(product.getStoreHouse()))
                .orElseThrow(() -> new IllegalArgumentException("请填写正确仓库")).toString());

        String resultSku = virtualProductDO.getVirtualSku();
        if (StrUtil.isNotBlank(virtualProductDO.getUpgradeId())){
            UpgradeInfoInteriorVo upgradeInfoInteriorVo = selectUpgradeInfo(virtualProductDO.getUpgradeId());

            if (ObjectUtil.isNotEmpty(upgradeInfoInteriorVo)){
                if (upgradeInfoInteriorVo.getOriginalSkuId().equals(virtualProductDO.getId())){
                    resultSku = upgradeInfoInteriorVo.getUpgradeSku();
                }
            }
        }
        String finalResultSku = resultSku;

        stockQuantityDpMap.entrySet().stream()
                .filter(entry -> entry.getKey().equals(finalResultSku))
                .findFirst()
                .ifPresentOrElse(entry -> entry.getValue().addStockQuantity(product),
                        () -> {
                            StockQuantityDp quantityDp = new StockQuantityDp(finalResultSku, new ArrayList<>());
                            quantityDp.addStockQuantity(product);
                            stockQuantityDpMap.put(finalResultSku, quantityDp);
                        });
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext analysisContext) {
        //  document why this method is empty
    }

    /* 这里会一行行的返回头
     *
     * @param headMap
     * @param context
     */
    @Override
    public void invokeHeadMap(Map<Integer, String> headMap, AnalysisContext context) {
        List<String> headList = StockQuantityImportHeadEnum.getCodeList();
        List<SenboWarehouseDto> senboWarehouseList = warehouseService.getSenboWarehouseListWithoutMiddleTransit();
        this.nameIdMap = senboWarehouseList.stream().collect(Collectors.toMap(SenboWarehouseDto::getSenboWarehouse, SenboWarehouseDto::getSenboWarehouseId));
        //删除headMap中的空值
        headMap.entrySet().removeIf(entry -> entry.getValue() == null || entry.getValue().isEmpty());
        List<String> headMapList = new ArrayList<>(headMap.values());

        //比较两个list集合值是否相等
        if (!headList.equals(headMapList)) {
            throw new RuntimeException("在途表头错误，请检查表头是否正确");
        }
    }
}
