package com.purchase.purchase_server.repository.redis.impl;

import com.purchase.purchase_server.model.targetSales.entity.dto.TargetDaySalesDto;
import com.purchase.purchase_server.repository.redis.ITargetSalesRedisRepository;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.Cursor;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ScanOptions;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Comparator;
import java.util.Map;
import java.util.TreeMap;

import static com.crafts_mirror.utils.constant.DateFormatConstant.YYYY_MM_DD_DATE_FORMAT_SLASH;
import static com.crafts_mirror.utils.constant.RedisKeyConstant.TARGET_SALES_VIRTUAL_SKU_PREFIX;

/**
 * <AUTHOR>
 * @Date 2025/3/18 14:19
 **/
@Service
@Slf4j
public class TargetSalesRedisRepositoryImpl implements ITargetSalesRedisRepository {

    @Resource
    private RedisTemplate<String, TargetDaySalesDto> redisTemplate;

    private final DateTimeFormatter formatter = DateTimeFormatter.ofPattern(YYYY_MM_DD_DATE_FORMAT_SLASH);

    @Override
    public Map<String, Double> getTargetSales(String virtualId, LocalDate startDate) {
        Map<String, Double> result = new TreeMap<>(Comparator.comparing(c -> c));
        String redisKey = TARGET_SALES_VIRTUAL_SKU_PREFIX + virtualId;

        ScanOptions options = ScanOptions.scanOptions()
                .count(1000) // 每批扫描数量
                .build();
        try (Cursor<Map.Entry<Object, Object>> cursor = redisTemplate.opsForHash().scan(redisKey, options)) {
            while (cursor.hasNext()) {
                Map.Entry<Object, Object> entry = cursor.next();
                String key = (String) entry.getKey();
                LocalDate localDate = LocalDate.parse(key, formatter);
                if (startDate.isAfter(localDate)) {
                    continue;
                }
                result.put(key, (Double) entry.getValue());
            }
        } catch (Exception e) {
            log.error("获取虚拟skuId：{} 的目标日销时出现异常", virtualId, e);
            throw new IllegalArgumentException("获取虚目标日销时出现异常，请联系研发处理");
        }

        return result;
    }
}
