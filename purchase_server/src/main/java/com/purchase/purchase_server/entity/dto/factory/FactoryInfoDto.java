package com.purchase.purchase_server.entity.dto.factory;

import com.purchase.purchase_server.entity.dataObject.SelfProductDO;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * @Description 供应商信息
 * <AUTHOR>
 * @Date 2024/6/5 20:39
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
public class FactoryInfoDto implements Serializable {


    @Serial
    private static final long serialVersionUID = 7515939731017083940L;
    private String id;
    /**
     * 供应商代码
     */
    @NotBlank(message = "供应商代码不能为空")
    @Length(max = 20, message = "供应商代码最多20个字符")
    @Pattern(regexp = "^[A-Za-z0-9]+$", message = "供应商代码只能是数字和字母")
    private String factoryCode;
    /**
     * 供应商名称
     */
    @NotBlank(message = "供应商名称不能为空")
    @Length(max = 100, message = "供应商名称最多20个字符")
    private String factoryName;
    /**
     * 简称
     */
    @Length(max = 20, message = "简称最多20个字符")
    private String shortName;
    /**
     * 地址代号
     */
    private String addressCode;
    /**
     * 采购员
     */
    @NotBlank(message = "采购员不能为空")
    @Length(max = 20, message = "采购员最多20个字符")
    private String purchaser;
    /**
     * 采购员账号
     */
    private String purchaserUserName;
    /**
     * 跟单人
     */
    @NotBlank(message = "跟单人不能为空")
    @Length(max = 20, message = "跟单人最多20个字符")
    private String orderTracker;
    /**
     * 跟单人账号
     */
    private String orderTrackerUserName;
    /**
     * 备注
     */
    @Length(max = 255, message = "备注最多255个字符")
    private String remark;
    /**
     * 创建时间
     */
    private String createDate;
    /**
     * 禁用状态
     */
    private String disableStatus;

    /**
     * 币种
     */
    private String currency;
    /**
     * 采购员列表
     */
    private List<SelfProductDO> selfProductDOList;
}
