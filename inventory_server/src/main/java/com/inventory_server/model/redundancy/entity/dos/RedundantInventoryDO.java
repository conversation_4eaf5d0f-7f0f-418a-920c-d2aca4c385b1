package com.inventory_server.model.redundancy.entity.dos;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.inventory_server.infrastructures.entity.PhysicalBaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.time.LocalDate;

/**
 * @Description 库存冗余列表页
 * <AUTHOR>
 * @Date 2024/5/7 17:52
 **/
@EqualsAndHashCode(callSuper = true)
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@TableName("cm_inventory_info")
public class RedundantInventoryDO extends PhysicalBaseEntity {

    private String virtualSkuId;

    private Double foreignRedundantInventory;

    private Integer onShippingRedundantInventory;

    private Double factoryRedundantInventory;

    /**
     * 海外仓理论售罄时间
     */
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private LocalDate foreignTheoreticalSoldOutDate;

    /**
     * 全链路海外仓理论售罄时间
     */
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private LocalDate fullLinkTheoreticalSoldOutDate;

    private LocalDate calFinishedDate;

    /**
     * 售罄前断货天数
     */
    private Integer daysBeforeSoldOut;

    /**
     * 全链路售罄前断货天数
     */
    private Integer fullLinkDaysBeforeSoldOut;

    /**
     * 全链路可售天数
     */
    private Integer fullLinkSalableDays;

    /**
     * 海外仓可售天数
     */
    private Integer salableDays;

    private String importFileId;

    private Integer isArtificialImport;

    private String snapshotId;

    private String endDateDesc;
}