package com.inventory_server.model.warehouse.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.inventory_server.model.warehouse.entity.dto.SenboWarehouseDto;
import com.inventory_server.model.warehouse.entity.form.SenboWarehouseForm;
import com.inventory_server.model.warehouse.entity.form.WarehouseDetailPageForm;
import com.inventory_server.model.warehouse.entity.vo.WarehouseDetailVo;

import java.util.List;
import java.util.Map;

public interface IWarehouseService {

    IPage<WarehouseDetailVo> getWarehouseDetailList(WarehouseDetailPageForm form);

    List<SenboWarehouseDto> getSenboWarehouseList(SenboWarehouseDto dto);

    List<SenboWarehouseDto> getSenboWarehouseListWithoutMiddleTransit();

    int getMinHeadShipDate(Map<String, Double> saleRatioMap);

    String getMinHeadShipDateWarehouse(Map<String, Double> saleRatioMap);

    boolean checkWarehouseUnique(SenboWarehouseDto dto);

    SenboWarehouseDto getInfoSenboWarehouse(String id);
    IPage<SenboWarehouseDto> getSenboWarehousePageList(SenboWarehouseForm form);
}
