package com.inventory_server.model.redundancy.repository.dataRepository;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.inventory_server.infrastructures.entity.PhysicalBaseEntity;
import com.inventory_server.model.redundancy.entity.dos.InventoryLocalWatchBoardDO;
import com.inventory_server.model.redundancy.mapper.LocalWatchBoardMapper;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Description 本地仓冗余试算看板
 * <AUTHOR>
 * @Date 2024/12/14 15:37
 **/
@Service
public class LocalWatchBoardRepository extends ServiceImpl<LocalWatchBoardMapper, InventoryLocalWatchBoardDO> {

    public void deleteByInventoryInfoId(String inventoryId) {
        List<InventoryLocalWatchBoardDO> list = baseMapper.selectList(Wrappers.<InventoryLocalWatchBoardDO>lambdaQuery()
                .eq(InventoryLocalWatchBoardDO::getInventoryInfoId, inventoryId));
        if (CollectionUtil.isEmpty(list)) {
            return;
        }
        List<String> idList = list.stream().map(PhysicalBaseEntity::getId).toList();
        baseMapper.deleteByIds(idList);
    }

    public List<InventoryLocalWatchBoardDO> getLocalInventoryWatchBoardByInfoId(String inventoryId) {
        return baseMapper.selectList(Wrappers.<InventoryLocalWatchBoardDO>lambdaQuery()
                .eq(InventoryLocalWatchBoardDO::getInventoryInfoId, inventoryId)
                .orderByAsc(InventoryLocalWatchBoardDO::getJudgeDate)
        );
    }
}
