package com.purchase.purchase_server.service.shipping;

import com.purchase.purchase_server.entity.dto.delivery.ShippingProjectWatchBoardWarehouseDto;
import com.purchase.purchase_server.entity.form.DeliveryCalculationForm;
import com.purchase.purchase_server.entity.form.InteriorInfoQuery;
import com.purchase.purchase_server.entity.vo.DeliveryCalculationVo;

import java.util.List;
import java.util.Set;

public interface InteriorDeliveryCalService {

    /**
     * 计算发货
     * @param form
     * @return
     */
    DeliveryCalculationVo calDelivery(DeliveryCalculationForm form, boolean needReCalFactoryInventory);

    DeliveryCalculationVo calMockResult(DeliveryCalculationForm form);

    List<ShippingProjectWatchBoardWarehouseDto> getWatchBoardInfoList(List<String> factoryFinishedIdList);

    Set<String> getSnapIds(InteriorInfoQuery info);

}
