package com.inventory_server.model.redundancy.entity.dp;


import com.inventory_server.model.product.entity.dp.ImportVirtualSkuInfoDp;

import java.util.HashMap;

/**
 * @Description 库存计划导入-AM库存，BTB库存，LB库存
 * <AUTHOR>
 * @Date 2023/12/29 10:56
 **/
public record StockQuantityInfoOtherDp(HashMap<Integer,String> data) implements CheckDeliveryInfo{
    public StockQuantityInfoOtherDp(HashMap<Integer,String> data) {
        this.data = data;
        new ImportVirtualSkuInfoDp(data.get(0));
        this.isValidNumber(data);

    }
    private void isValidNumber(HashMap<Integer,String> map){
        map.forEach((key, value) -> {
            if (key != 0 ){
                checkNum(value);
            }
        });
    }
}
