package com.inventory_server.model.redundancy.service.impl;

import cn.hutool.core.date.DateTime;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.TypeReference;
import com.crafts_mirror.common.security.utils.SecurityUtils;
import com.crafts_mirror.utils.enums.ResponseCodeEnum;
import com.crafts_mirror.utils.utils.DateUtils;
import com.crafts_mirror.utils.utils.RestTemplateUtils;
import com.inventory_server.applications.dto.FactoryFinishedInventoryDto;
import com.inventory_server.applications.dto.FactoryRemainInventoryDto;
import com.inventory_server.applications.vo.response.ResultDTO;
import com.inventory_server.model.product.entity.dos.SelfProductDO;
import com.inventory_server.model.product.entity.dos.VirtualProductDO;
import com.inventory_server.model.product.entity.dto.ProductSnapshotDto;
import com.inventory_server.model.product.repository.interiorRepository.ISnapshotRepository;
import com.inventory_server.model.redundancy.entity.aggregate.InventoryFactoryPlanInfoA;
import com.inventory_server.model.redundancy.entity.dos.InventoryForeignStoreDO;
import com.inventory_server.model.redundancy.entity.dos.InventorySaleRulesDO;
import com.inventory_server.model.redundancy.entity.dos.RedundantInventoryDO;
import com.inventory_server.model.redundancy.entity.dp.*;
import com.inventory_server.model.redundancy.entity.form.DeliveryCalculationForm;
import com.inventory_server.model.redundancy.entity.vo.DeliveryCalculationVo;
import com.inventory_server.model.redundancy.service.*;
import com.inventory_server.model.warehouse.entity.dto.SenboWarehouseDto;
import com.inventory_server.model.warehouse.service.IWarehouseService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static com.crafts_mirror.utils.constant.SystemConstant.DELIVERY_CALCULATION_URL;

/**
 * @Description 冗余库存信息获取、初步处理、数据储存、展示的service层
 * <AUTHOR>
 * @Date 2024/5/7 16:38
 **/
@Service
@Slf4j
public class InventoryMockTableServiceImpl implements IInventoryMockTableService {
    @Resource
    private ISnapshotRepository snapshotRepository;

    @Resource
    private IInventoryForeignStoreService inventoryForeignStoreService;

    @Resource
    private IInventorySaleRulesService inventorySaleRulesService;

    @Resource
    @Lazy
    private IRedundantInventoryService redundantInventoryService;

    @Resource
    private IWarehouseService warehouseService;

    @Resource
    protected RestTemplate restTemplate;

    @Resource
    private IInventoryFactoryPlanInfoService inventoryFactoryPlanInfoService;

    @Override
    public DeliveryCalculationVo getInventoryMockTable(RedundantInventoryDO inventoryInfoDO) {
        // 商品快照信息
        ProductSnapshotDto productSnapshot = snapshotRepository.getProductSnapshotBySnapshotId(inventoryInfoDO.getSnapshotId());
        SelfProductDO selfProduct = JSON.to(SelfProductDO.class, productSnapshot.getSelfData());
        VirtualProductDO virtualProduct = JSON.to(VirtualProductDO.class, productSnapshot.getVirtualData());
        String virtualSku = virtualProduct.getVirtualSku();

        List<SenboWarehouseDto> senboWarehouseList = warehouseService.getSenboWarehouseListWithoutMiddleTransit();
        Map<String, Integer> nameIdMap = senboWarehouseList.stream()
                .collect(Collectors.toMap(SenboWarehouseDto::getSenboWarehouse, SenboWarehouseDto::getSenboWarehouseId));

        String inventoryInfoId = inventoryInfoDO.getId();
        List<InventoryForeignStoreDO> foreignStoreList = inventoryForeignStoreService.getForeignStoreListByInfoIds(Collections.singletonList(inventoryInfoId));
        List<FactoryRemainInventoryDto> factoryRemainInventoryList = foreignStoreList.stream()
                .map(foreignStore -> {
                    FactoryRemainInventoryDto factoryRemainInventory = new FactoryRemainInventoryDto();
                    factoryRemainInventory.setVirtualSku(virtualSku);
                    factoryRemainInventory.setWarehouse(nameIdMap.get(foreignStore.getWarehouse()).toString());
                    factoryRemainInventory.setEnableUsingDate(new DateTime(foreignStore.getEnableUsingDate()));
                    factoryRemainInventory.setStoreNum(foreignStore.getInventoryNum().doubleValue());
                    return factoryRemainInventory;
                }).collect(Collectors.toList());

        // 发货规则
        InventorySaleRulesDO saleRules = inventorySaleRulesService.getInventorySaleByInfoId(inventoryInfoId);
        Map<String, Integer> headShippingDateMap = JSONObject.parseObject(saleRules.getHeadShippingDate(), new TypeReference<>() {
        });
        Map<String, Double> saleRatio = JSONObject.parseObject(saleRules.getSaleRatio(), new TypeReference<>() {
        });
        InventoryRulesDp inventoryRules = new InventoryRulesDp(virtualSku,
                new InvOrRepDaysRulesDp(saleRules.getPurchaseDays(), saleRules.getTransitDays(),
                        saleRules.getSafeDays(), saleRules.getShippingCircle(), saleRules.getPurchaseCircle()
                ),
                new ShippingRulesDp(saleRatio));

        // 计划里的数据
        List<FactoryFinishedInventoryDto> factoryFinishedInventoryList = new ArrayList<>();
        List<InventoryFactoryPlanInfoA> planInfoList = inventoryFactoryPlanInfoService.getPlanInfoListByInfoId(inventoryInfoId);
        for (InventoryFactoryPlanInfoA planInfo : planInfoList) {
            factoryFinishedInventoryList.add(FactoryFinishedInventoryDto.builder()
                    .shippingNum(planInfo.getStoreNum().doubleValue())
                    .virtualSku(planInfo.getDestinationSku())
                    .contractCode(planInfo.getContractCode())
                    .factoryFinishedDate(planInfo.getFactoryFinishedDate())
                    .remark(planInfo.getRemarks())
                    .build());
        }

        // 目标日销
        TreeMap<String, BigDecimal> targetSalesMap = JSONObject.parseObject(saleRules.getSaleDestination(), new TypeReference<>() {
        });
        TargetSalesDp targetSalesDp = new TargetSalesDp(virtualSku, targetSalesMap);
        StockQuantityDp stockQuantity = new StockQuantityDp(virtualSku, factoryRemainInventoryList);
        InventoryCalDp inventoryCalDp = new InventoryCalDp(virtualProduct, inventoryRules, targetSalesDp, stockQuantity,
                new FactoryPlanDp(virtualSku, factoryFinishedInventoryList), selfProduct, null);
        List<InventoryCalDp> inventoryCalDpList = Collections.singletonList(inventoryCalDp);
        DateTime dateTime = DateUtils.convertToDateTime(inventoryInfoDO.getCalFinishedDate());
        DeliveryCalculationForm form = redundantInventoryService.convertToRequestForm(inventoryCalDpList, dateTime,
                true, true, false, headShippingDateMap, saleRules.getChangeableSafeDays());
        RestTemplateUtils restTemplateUtil = new RestTemplateUtils(restTemplate, SecurityUtils.getToken());
        ResultDTO result = restTemplateUtil.post(form, ResultDTO.class, DELIVERY_CALCULATION_URL);
        if (!Objects.equals(result.getStatus(), ResponseCodeEnum.OK.getCode())) {
            log.error("冗余库存计算时获取发货数据失败，异常原因：{}", result.getMessage());
            throw new RuntimeException("冗余库存计算时获取发货数据失败，异常原因：" + result.getMessage());
        }
        DeliveryCalculationVo deliveryCalculationVo = JSON.to(DeliveryCalculationVo.class, result.getData());
        redundantInventoryService.supplementMockTable(deliveryCalculationVo.getDeliveryCalResultList().getFirst(),
                inventoryInfoDO, targetSalesMap, senboWarehouseList, saleRatio);

        return deliveryCalculationVo;
    }

    /**
     * 删除模拟试算表格
     *
     * @param inventoryId 库存ID
     */
    @Override
    public void deleteInventoryMockTableByInfoId(String inventoryId) {
//        inventoryMockTableRepository.deleteInventoryMockTableByInfoId(inventoryId);
    }
}
