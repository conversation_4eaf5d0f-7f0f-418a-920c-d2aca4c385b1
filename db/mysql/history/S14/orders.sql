SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for cm_yicang_purchase_order_delivery_schedule
-- ----------------------------
DROP TABLE IF EXISTS `cm_yicang_purchase_order_delivery_schedule`;
CREATE TABLE `cm_yicang_purchase_order_delivery_schedule`
(
    `id`                         varchar(50) NOT NULL,
    `expected_delivery_date`     datetime    NULL DEFAULT NULL COMMENT '预期交货时间',
    `expected_delivery_quantity` int         NULL DEFAULT NULL COMMENT '预期交货数量',
    `sb_po_virtual_id`           varchar(50) NULL DEFAULT NULL COMMENT '采购单虚拟表Id',
    `status`                     char(1)     NOT NULL,
    `create_by`                  varchar(50) NULL DEFAULT NULL COMMENT '创建人',
    `update_by`                  varchar(50) NULL DEFAULT NULL COMMENT '更新人',
    `create_date`                datetime    NULL DEFAULT NULL COMMENT '创建时间',
    `update_date`                datetime    NULL DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE,
    INDEX `yc_po_virtual_id` (`sb_po_virtual_id` ASC) USING BTREE
) ENGINE = InnoDB
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci COMMENT = '易仓采购单交期计划表'
  ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for cm_yicang_purchase_order_detail
-- ----------------------------
DROP TABLE IF EXISTS `cm_yicang_purchase_order_detail`;
CREATE TABLE `cm_yicang_purchase_order_detail`
(
    `id`                   varchar(50)  NOT NULL,
    `product_id`           int          NULL DEFAULT NULL COMMENT '产品Id',
    `qty_expected`         int          NULL DEFAULT NULL COMMENT '预期数量',
    `qty_pay`              int          NULL DEFAULT NULL COMMENT '(良品上架数量)付款数量',
    `qty_receving`         int          NULL DEFAULT NULL COMMENT '实收数量',
    `qty_free`             int          NULL DEFAULT NULL COMMENT '赠送数量',
    `unit_price`           float        NULL DEFAULT NULL COMMENT '单价（不含税）',
    `contract_price`       float        NULL DEFAULT NULL COMMENT '单价（含税）',
    `total_price`          float        NULL DEFAULT NULL COMMENT '总价',
    `currency_code`        varchar(50)  NULL DEFAULT NULL COMMENT '币种',
    `sb_self_sku_id`       varchar(50)  NULL DEFAULT NULL COMMENT '自定义产品id（senbo）',
    `sb_self_sku_name`     varchar(255) NULL DEFAULT NULL COMMENT '自定义产品名称（senbo）',
    `image`                varchar(255) NULL DEFAULT NULL COMMENT '图片',
    `buyer`                varchar(255) NULL DEFAULT NULL COMMENT '产品经理',
    `product_sku`          varchar(255) NULL DEFAULT NULL COMMENT '产品代码（自定义）',
    `product_title`        varchar(255) NULL DEFAULT NULL COMMENT '产品名称',
    `sp_supplier_sku`      varchar(255) NULL DEFAULT NULL COMMENT '供应商品号',
    `is_free`              varchar(50)  NULL DEFAULT NULL COMMENT '是否赠品\r\n0否，\r\n1是',
    `note`                 longtext     NULL DEFAULT NULL COMMENT '产品采购备注',
    `pop_external_number`  varchar(255) NULL DEFAULT NULL COMMENT '外部单号',
    `transfer_qty`         int          NULL DEFAULT NULL COMMENT '头程占用数量',
    `po_tax_rate`          int          NULL DEFAULT NULL COMMENT '税率',
    `first_receive_time`   datetime     NULL DEFAULT NULL COMMENT '首次到货时间',
    `pop_platform_product` varchar(255) NULL DEFAULT NULL COMMENT '平台产品代码：如1688产品ID beta',
    `pop_platform_sku`     varchar(255) NULL DEFAULT NULL COMMENT '平台产品款式：如1688产品的specId',
    `po_status`            varchar(255) NULL DEFAULT NULL COMMENT '产品采购状态',
    `sb_po_id`             varchar(50)  NOT NULL COMMENT '采购单表id',
    `status`               char(1)      NOT NULL,
    `create_by`            varchar(50)  NULL DEFAULT NULL COMMENT '创建人',
    `update_by`            varchar(50)  NULL DEFAULT NULL COMMENT '更新人',
    `create_date`          datetime     NULL DEFAULT NULL COMMENT '创建时间',
    `update_date`          datetime     NULL DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE,
    INDEX `yc_po_id` (`sb_po_id` ASC) USING BTREE
) ENGINE = InnoDB
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci COMMENT = '易仓采购单明细'
  ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for cm_yicang_purchase_order_system_track
-- ----------------------------
DROP TABLE IF EXISTS `cm_yicang_purchase_order_system_track`;
CREATE TABLE `cm_yicang_purchase_order_system_track`
(
    `id`       varchar(50)  NOT NULL,
    `name`     varchar(255) NULL DEFAULT NULL COMMENT '跟单状态中文名称',
    `name_en`  varchar(255) NULL DEFAULT NULL COMMENT '跟单状态英文名称',
    `sb_po_id` varchar(50)  NOT NULL COMMENT '采购单表id',
    PRIMARY KEY (`id`) USING BTREE,
    INDEX `yc_po_id` (`sb_po_id` ASC) USING BTREE
) ENGINE = InnoDB
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci COMMENT = '易仓系统跟单状态表'
  ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for cm_yicang_purchase_order_track
-- ----------------------------
DROP TABLE IF EXISTS `cm_yicang_purchase_order_track`;
CREATE TABLE `cm_yicang_purchase_order_track`
(
    `id`       varchar(50)  NOT NULL,
    `name`     varchar(255) NULL DEFAULT NULL COMMENT '跟单状态中文名称',
    `name_en`  varchar(255) NULL DEFAULT NULL COMMENT '跟单状态英文名称',
    `sb_po_id` varchar(50)  NULL DEFAULT NULL COMMENT '采购单表id',
    PRIMARY KEY (`id`) USING BTREE,
    INDEX `yc_po_id` (`sb_po_id` ASC) USING BTREE
) ENGINE = InnoDB
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci COMMENT = '易仓采购单跟单状态表'
  ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for cm_yicang_purchase_order_virtual
-- ----------------------------
DROP TABLE IF EXISTS `cm_yicang_purchase_order_virtual`;
CREATE TABLE `cm_yicang_purchase_order_virtual`
(
    `id`              varchar(50)  NOT NULL,
    `virtual_sku_id`  varchar(50)  NULL DEFAULT NULL COMMENT 'senbo虚拟skuId',
    `destination_sku` varchar(255) NULL DEFAULT NULL COMMENT 'senbo虚拟sku',
    `is_old_status`   char(1)      NOT NULL COMMENT 'sku类型(0-虚拟sku 1-老sku)',
    `channel`         varchar(255) NULL DEFAULT NULL COMMENT '渠道',
    `operator`        varchar(255) NULL DEFAULT NULL COMMENT '运营',
    `qty_eta`         int          NULL DEFAULT NULL COMMENT '采购量',
    `sb_po_detail_id` varchar(50)  NULL DEFAULT NULL COMMENT '采购单明细表id',
    `status`          char(1)      NOT NULL,
    `create_by`       varchar(50)  NULL DEFAULT NULL COMMENT '创建人',
    `update_by`       varchar(50)  NULL DEFAULT NULL COMMENT '更新人',
    `create_date`     datetime     NULL DEFAULT NULL COMMENT '创建时间',
    `update_date`     datetime     NULL DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE,
    INDEX `yc_po_detail_id` (`sb_po_detail_id` ASC) USING BTREE
) ENGINE = InnoDB
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci COMMENT = '易仓采购单虚拟sku表'
  ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for cm_yicang_purchase_orders
-- ----------------------------
DROP TABLE IF EXISTS `cm_yicang_purchase_orders`;
CREATE TABLE `cm_yicang_purchase_orders`
(
    `id`                         varchar(50)  NOT NULL,
    `po_id`                      int          NULL     DEFAULT NULL COMMENT '采购单Id',
    `po_code`                    varchar(255) NULL     DEFAULT NULL COMMENT '采购单号',
    `warehouse_id`               int          NULL     DEFAULT NULL COMMENT '仓库Id',
    `shipping_method_id_head`    int          NULL     DEFAULT NULL COMMENT '头程运输方式',
    `tracking_no`                varchar(255) NULL     DEFAULT NULL COMMENT '跟踪号',
    `ref_no`                     varchar(255) NULL     DEFAULT NULL COMMENT '参考号',
    `suppiler_id`                int          NULL     DEFAULT NULL COMMENT '供应商Id',
    `payable_amount`             float        NULL     DEFAULT NULL COMMENT '总应付金额',
    `actually_amount`            float        NULL     DEFAULT NULL COMMENT '总实付金额',
    `currency_code`              varchar(255) NULL     DEFAULT NULL COMMENT '币种',
    `pay_status`                 int          NULL     DEFAULT NULL COMMENT '付款状态：\r\n0未申请付款，\r\n3已申请未付款，1已付款 ，\r\n2未付清',
    `po_staus`                   int          NULL     DEFAULT NULL COMMENT '采购单状态：\r\n10已确认(供应商反馈)，\r\n1待确认，\r\n2审核中，\r\n3已审批(入库在途)，\r\n5到货异常，\r\n8已完成，\r\n9撤销，\r\n11已取消(供应商反馈) (\'10\'、\'11\' 这2种状态未开通供应商系统忽略)',
    `date_create`                datetime     NULL     DEFAULT NULL COMMENT '创建时间',
    `date_eta`                   datetime     NULL     DEFAULT NULL COMMENT '预计到货时间',
    `date_release`               datetime     NULL     DEFAULT NULL COMMENT '审核时间',
    `po_completion_time`         datetime     NULL     DEFAULT NULL COMMENT '采购单完成时间',
    `po_update_time`             datetime     NULL     DEFAULT NULL COMMENT '更新时间\r\n\r\n（\r\n\r\n以下业务场景下会更新：\r\n1，采购单， \r\n\r\n   操作 ： 编辑、提交审核、撤销提交审核 、 采购单撤销、强制完成采购单、批量导入跟踪号信息\r\n\r\n \r\n\r\n2，收货、\r\n\r\n \r\n\r\n3，质检（以及操作未到货）\r\n\r\n \r\n\r\n4，取消上架\r\n\r\n \r\n\r\n5，出纳付款，操作“确认付款”\r\n\r\n \r\n\r\n6，采购变更，变更单， 操作：创建变更单、作废、驳回、审核通过\r\n\r\n \r\n\r\n7，收货异常处理（触发更新：payable_amount“应付金额”、receiving_exception_status “收货异常状态” 或者 po_staus “采购单状态”时）\r\n\r\n \r\n\r\n8，QC异常处理（触发更新：qc_exception_status “QC异常状态” 或者 po_staus“采购单状态”时）\r\n\r\n \r\n\r\n9，类型为“退货”、“良品换货”、“次品换良品”出库单，使用的采购单批次，确认出库）',
    `to_warehouse_id`            int          NULL     DEFAULT NULL COMMENT '中转仓库Id',
    `receiving_exception`        int          NULL     DEFAULT NULL COMMENT '收货异常：\r\n0没有异常、\r\n1收货异常、\r\n2QC异常\r\n3收货&质检异常',
    `operator_purchase`          int          NULL     DEFAULT NULL COMMENT '采购员（用户ID）',
    `receiving_exception_handle` int          NULL     DEFAULT NULL COMMENT '收货异常是否已经处理\r\n0无、\r\n1没有处理、\r\n2已经处理',
    `return_verify`              varchar(255) NULL     DEFAULT NULL COMMENT '采购单是否通过审批，未通过或通过',
    `pay_ship_amount`            float        NULL     DEFAULT NULL COMMENT '	运输金额',
    `create_type`                int          NULL     DEFAULT NULL COMMENT '建立方式，\r\n0系统生成，\r\n1人工建立',
    `pts_status_sort`            varchar(255) NULL     DEFAULT NULL COMMENT '跟单状态序号',
    `account_type`               int          NULL     DEFAULT NULL COMMENT '结算方式',
    `pts_oprater`                int          NULL     DEFAULT NULL COMMENT '跟单员（用户ID）',
    `transaction_no`             varchar(255) NULL     DEFAULT NULL COMMENT '交易支付单号，如淘宝交易号',
    `ps_id`                      int          NULL     DEFAULT NULL COMMENT '采购单承运商ID',
    `po_remark`                  longtext     NULL     DEFAULT NULL COMMENT '备注',
    `receiving_exception_status` int          NULL     DEFAULT NULL COMMENT '收货异常状态',
    `qc_exception_status`        int          NULL     DEFAULT NULL COMMENT '质检异常状态',
    `supplier_name`              varchar(255) NULL     DEFAULT NULL COMMENT '供应商名称',
    `supplier_code`              varchar(255) NULL     DEFAULT NULL COMMENT '供应商代码',
    `warehouse_code`             varchar(255) NULL     DEFAULT NULL COMMENT '仓库代码',
    `warehouse_desc`             varchar(255) NULL     DEFAULT NULL COMMENT '仓库描述',
    `receiving_code`             varchar(255) NULL     DEFAULT NULL COMMENT '入库单号',
    `verify`                     int          NULL     DEFAULT NULL COMMENT '是否是审核未通过，回退的采购单\r\n0否\r\n1是',
    `mark_eta`                   int          NULL     DEFAULT NULL COMMENT '	到货（\r\n1今日到\r\n2晚到\r\n）',
    `pts_name`                   varchar(255) NULL     DEFAULT NULL COMMENT '跟单员',
    `ps_name`                    varchar(255) NULL     DEFAULT NULL COMMENT '承运商名称（ps_id为0时不返回）',
    `ps_url`                     varchar(255) NULL     DEFAULT NULL COMMENT '承运商网址（ps_id为0时不返回）',
    `pt_note`                    longtext     NULL     DEFAULT NULL COMMENT '最新的采购单跟踪备注',
    `pt_add_time`                varchar(255) NULL     DEFAULT NULL COMMENT '最新的采购单跟踪备注时间',
    `qty_expected_all`           int          NULL     DEFAULT NULL COMMENT '预期总数',
    `qty_receving_all`           int          NULL     DEFAULT NULL COMMENT '实到总数',
    `qty_eta_all`                int          NULL     DEFAULT NULL COMMENT '采购总数',
    `trackings`                  int          NULL     DEFAULT NULL COMMENT '跟踪号数量',
    `po_is_net`                  int          NULL     DEFAULT NULL COMMENT '是否网采：\r\n0否，\r\n1是',
    `pay_type`                   int          NULL     DEFAULT NULL COMMENT '支付方式，\r\n1现金、\r\n2在线，\r\n3银行账号',
    `bank_name`                  varchar(255) NULL     DEFAULT NULL COMMENT '银行名称，支付方式为银行账号时返回',
    `pay_account`                varchar(255) NULL     DEFAULT NULL COMMENT '支付账户',
    `sum_amount`                 float        NULL     DEFAULT NULL COMMENT '预期总金额',
    `date_expected`              datetime     NULL     DEFAULT NULL COMMENT '预计出厂时间',
    `po_type`                    int          NULL     DEFAULT NULL COMMENT '补货方式：\r\n0缺货入库，\r\n1警报入库，\r\n2特采入库，\r\n3正常入库，\r\n4样品采购入库，5备货采购，\r\n6试销采购，\r\n7返修入库',
    `single_net_number`          varchar(255) NULL     DEFAULT NULL COMMENT '网采单号（多个）格式：[\"1111111\",\"222222\"]',
    `total_tax_fee`              float        NULL     DEFAULT NULL COMMENT '税费',
    `payment_note`               longtext     NULL     DEFAULT NULL COMMENT '采购审核备注',
    `company`                    varchar(255) NULL     DEFAULT NULL COMMENT '采购公司',
    `operator_create`            varchar(255) NULL     DEFAULT NULL COMMENT '创建人',
    `tracking_no_set`            varchar(255) NULL     DEFAULT NULL COMMENT '跟踪号集合（所有跟踪号，格式：[\"123\",\"234\"]）beta版未上线',
    `user_organization_id`       int          NULL     DEFAULT NULL COMMENT '组织机构id',
    `is_rebate_tax`              int          NULL     DEFAULT NULL COMMENT '是否退税（0-否 1-是）',
    `user_organization_name`     varchar(255) NULL     DEFAULT NULL COMMENT '组织机构名称',
    `account_proportion`         int          NULL     DEFAULT NULL COMMENT '预付比例',
    `payment_cycle_type`         varchar(255) NULL     DEFAULT NULL COMMENT '支付周期',
    `is_re_examine`              int          NULL     DEFAULT NULL COMMENT '是否需要复审 0 不需要 1 需要',
    `latest_receiving_time`      datetime     NULL     DEFAULT NULL COMMENT '最近收货时间',
    `is_imported`                char(1)      NOT NULL DEFAULT '0' COMMENT '是否导入过，0：未导入过，1：导入过',
    `status`                     char(1)      NOT NULL,
    `create_by`                  varchar(50)  NULL     DEFAULT NULL COMMENT '创建人',
    `update_by`                  varchar(50)  NULL     DEFAULT NULL COMMENT '更新人',
    `create_date`                datetime     NULL     DEFAULT NULL COMMENT '创建时间',
    `update_date`                datetime     NULL     DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci COMMENT = '易仓采购单表'
  ROW_FORMAT = DYNAMIC;

SET FOREIGN_KEY_CHECKS = 1;
