package com.purchase.purchase_server.entity.vo;


import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.TypeReference;
import com.purchase.purchase_server.entity.bo.RepVirtualSkuDateBO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;
import java.util.Map;
import java.util.TreeMap;
import java.util.stream.Collectors;

import static com.crafts_mirror.utils.constant.DateFormatConstant.YYYY_MM_DD_HH_MM_SS_DATE_FORMAT_HYPHEN;

/**
 * @Description 补货计划列表页展示
 * <AUTHOR>
 * @Date 2024/1/17 14:21
 **/
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class ReplenishmentProjectSkuListVo implements Serializable {

    @Serial
    private static final long serialVersionUID = -4460730709400324731L;
    /**
     * 补货计划id
     */
    private String repProjectId;
    /**
     * 自定义sku
     */
    private String selfSku;
    /**
     * 图片
     */
    private String image;
    /**
     * 供应商代码
     */
    private String factoryCode;
    /**
     *产品名称
     */
    private String productName;
    /**
     * 海外仓库存
     */
    private Map<String,Integer> overseasInventory;
    /**
     * 海外仓在途
     */
    private Map<String,Integer> overseasShipping;
    /**
     * 本地库存
     */
    private Map<String,Integer> localInventory;
    /**
     * 建议采购量
     */
    private Integer advicePurchaseNum;
    /**
     * 已采纳采购量
     */
    private Integer acceptedPurchaseNum;
    /**
     * 操作人
     */
    private String updateBy;
    /**
     * 计划生成时间
     */
    private String createDate;
    /**
     * 最后编辑时间
     */
    private String updateDate;


    public static List<ReplenishmentProjectSkuListVo> convertRepProjectSkuList(List<RepVirtualSkuDateBO> list) {
        if (CollectionUtil.isNotEmpty(list)) {
            return list.stream().map(ReplenishmentProjectSkuListVo::convertRepProjectSku).collect(Collectors.toList());
        }

        return null;
    }

    private static ReplenishmentProjectSkuListVo convertRepProjectSku(RepVirtualSkuDateBO repVirtualSkuBO) {
        return ReplenishmentProjectSkuListVo.builder()
                .repProjectId(repVirtualSkuBO.getId())
                .selfSku(repVirtualSkuBO.getSelfSku())
                .image(repVirtualSkuBO.getImage())
                .factoryCode(repVirtualSkuBO.getFactoryCode())
                .productName(repVirtualSkuBO.getProductName())
                .overseasInventory(JSONObject.parseObject(repVirtualSkuBO.getOverseasInventory(), new TypeReference<TreeMap<String, Integer>>() {
                }))
                .overseasShipping(JSONObject.parseObject(repVirtualSkuBO.getOverseasShipping(), new TypeReference<TreeMap<String, Integer>>() {
                }))
                .localInventory(JSONObject.parseObject(repVirtualSkuBO.getLocalInventory(), new TypeReference<TreeMap<String, Integer>>() {
                }))
                .advicePurchaseNum(repVirtualSkuBO.getAdvicePurchaseNum())
                .acceptedPurchaseNum(repVirtualSkuBO.getAcceptedPurchaseNum())
                .updateBy(repVirtualSkuBO.getUpdateBy())
                .updateDate(DateUtil.format(repVirtualSkuBO.getUpdateDate(), YYYY_MM_DD_HH_MM_SS_DATE_FORMAT_HYPHEN))
                .createDate(DateUtil.format(repVirtualSkuBO.getCreateDate(), YYYY_MM_DD_HH_MM_SS_DATE_FORMAT_HYPHEN))
                .build();
    }
}
