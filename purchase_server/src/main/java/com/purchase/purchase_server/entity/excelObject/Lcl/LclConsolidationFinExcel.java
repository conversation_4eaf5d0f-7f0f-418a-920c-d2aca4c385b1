package com.purchase.purchase_server.entity.excelObject.Lcl;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.alibaba.excel.enums.poi.HorizontalAlignmentEnum;
import com.alibaba.excel.enums.poi.VerticalAlignmentEnum;
import com.purchase.purchase_server.entity.excelObject.BaseExcel;
import lombok.*;

import java.io.Serial;
import java.io.Serializable;

/**
 * @Description excel导出装柜数据 转换成的实体列
 * <AUTHOR>
 * @Date 2024/1/19 17:00
 **/
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
//excel内容居中
@ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER, verticalAlignment = VerticalAlignmentEnum.CENTER)
//excel字体大小
//@ContentFontStyle(fontHeightInPoints = 15)
public class LclConsolidationFinExcel extends BaseExcel implements Serializable {

    @Serial
    private static final long serialVersionUID = -349237617600388921L;
    @ExcelProperty("合同号")
    private String contractCode;
    @ExcelProperty("虚拟SKU")
    private String destinationSku;
    @ExcelProperty("工厂交期")
    private String factoryFinishedDate;
    @ExcelProperty("发货数量")
    private Integer lclShippingNum;
    @ExcelProperty("未安排数量")
    private Integer factoryRemainNum;
    @ExcelProperty("原发货数量")
    private Integer originalShippingNum;
}
