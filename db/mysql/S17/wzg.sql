CREATE TABLE `cm_product_category`
(
    `id`            int          NOT NULL AUTO_INCREMENT,
    `category_name` varchar(255) NOT NULL COMMENT '品类名称',
    `parent_id`     int          DEFAULT NULL COMMENT '父级id',
    `is_leaf`       char(1)      NOT NULL COMMENT '是否为叶子节点：0-否，1-是',
    `ancestors`     varchar(255) DEFAULT NULL COMMENT '祖籍列表',
    `category_sort` int          NOT NULL COMMENT '排序',
    `status`        char(1)      DEFAULT NULL,
    `create_by`     varchar(50)  DEFAULT NULL,
    `create_date`   datetime     DEFAULT NULL,
    `update_by`     varchar(50)  DEFAULT NULL,
    `update_date`   datetime     DEFAULT NULL,
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci COMMENT ='产品分类表';

ALTER TABLE `crafts_mirror`.`cm_self_product`
    ADD COLUMN `category_id` int DEFAULT NULL COMMENT '产品品类id' AFTER `product_name`;

INSERT INTO `cm_product_category` VALUES (1, '家具', 0, '0', '0', 1, '0', 'wzg', '2025-02-07 10:14:29', NULL, NULL);
INSERT INTO `cm_product_category` VALUES (2, '灯具', 0, '0', '0', 2, '0', 'wzg', '2025-02-07 10:14:49', NULL, NULL);
INSERT INTO `cm_product_category` VALUES (3, '其他', 1, '1', '1', 3, '0', 'wzg', '2025-02-07 10:15:13', NULL, NULL);
INSERT INTO `cm_product_category` VALUES (4, '其他', 2, '1', '2', 4, '0', 'wzg', '2025-02-07 10:15:35', NULL, NULL);

