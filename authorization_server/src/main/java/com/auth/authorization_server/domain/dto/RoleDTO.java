package com.auth.authorization_server.domain.dto;

import com.auth.authorization_server.enums.RoleStatusEnum;
import com.auth.authorization_server.model.BasePageForm;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.util.Set;

/**
 * 角色表 cm_sys_role
 *
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
public class RoleDTO extends BasePageForm {

    @Serial
    private static final long serialVersionUID = -5230627251187887379L;
    /**
     * 角色id
     */
    private String id;

    /**
     * 角色名称
     */
    private String roleName;


    /**
     * 显示顺序
     */
    private Integer roleSort;

    /**
     * 角色状态（0正常 1停用）
     *
     * @see RoleStatusEnum
     */
    private String roleStatus;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建时间
     */
    private String createDate;

    /**
     * 备注
     */
    private String remarks;

    /**
     * 用户是否存在此角色标识 默认不存在
     */
    private boolean flag = false;

    /**
     * 菜单组
     */
    private Long[] menuIds;

    /**
     * 角色菜单权限
     */
    private Set<String> permissions;
}
