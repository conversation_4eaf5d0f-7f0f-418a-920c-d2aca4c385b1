package com.inventory_server.model.redundancy.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @date 2024/5/14
 **/
@Getter
@AllArgsConstructor
public enum InventoryMockTableTypeEnum {

    ShippingInventory(0, "模拟在途"),
    RemainInventory(1, "模拟剩余库存"),
    DaysSaleInventory(2, "模拟日销");

    private final Integer code;
    private final String desc;

    public static FactoryPlanInfoEnum ofCode(String code) {
        return Arrays.stream(FactoryPlanInfoEnum.values())
                .filter(it -> it.getCode().equals(code))
                .findFirst()
                .orElse(null);
    }
}
