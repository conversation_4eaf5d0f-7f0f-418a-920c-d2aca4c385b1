package com.auth.authorization_server.service.impl;


import cn.hutool.core.collection.CollectionUtil;
import com.auth.authorization_server.assembler.UserAssembler;
import com.auth.authorization_server.domain.dto.DeptDTO;
import com.auth.authorization_server.domain.dto.RoleDTO;
import com.auth.authorization_server.domain.dto.UserDTO;
import com.auth.authorization_server.domain.vo.UserVO;
import com.auth.authorization_server.enums.DataPermissionTypeEnum;
import com.auth.authorization_server.enums.RoleStatusEnum;
import com.auth.authorization_server.model.dataObject.RoleDataPermissionDO;
import com.auth.authorization_server.model.dataObject.UserDO;
import com.auth.authorization_server.repository.RoleDataPermissionRepositoryImpl;
import com.auth.authorization_server.repository.UserRepositoryImpl;
import com.auth.authorization_server.req.UserParams;
import com.auth.authorization_server.service.ISysRoleService;
import com.auth.authorization_server.service.ISysUserInteriorService;
import com.crafts_mirror.common.security.dataPermission.vo.RoleDataPerVO;
import com.crafts_mirror.common.security.utils.SecurityUtils;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 角色 业务层处理
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class SysUserInteriorServiceImpl implements ISysUserInteriorService {

    @Resource
    private UserRepositoryImpl userRepository;

    @Resource
    private RoleDataPermissionRepositoryImpl roleDataPerRepository;

    @Resource
    private ISysRoleService roleService;

    @Resource
    private UserAssembler userAssembler;

    /**
     * 查询用户列表
     *
     * @return 用户信息集合信息
     */
    @Override
    public List<UserVO> selectUserList() {
        List<UserDO> list = userRepository.list();
        return userAssembler.userDoListToVo(list);
    }

    @Override
    public List<UserVO> selectUserInfo(UserParams params) {
        List<UserDO> userDO = userRepository.selectUserList(params);
        return userAssembler.userDoListToVo(userDO);
    }

    @Override
    public RoleDataPerVO getDataPermissionUser(String moduleId) {
        RoleDataPerVO roleDataPerVO = new RoleDataPerVO();
        String userId = SecurityUtils.getUserId();
        if (UserDO.isAdmin(userId)) {
            return null;
        }
        List<RoleDTO> roleDTOS = roleService.selectRolesByUserId(userId);
        List<String> userNames = new ArrayList<>();

        List<String> roleIds = roleDTOS.stream().filter( i -> i.isFlag() &&
                RoleStatusEnum.YES_STATUS.getCode().equals(i.getRoleStatus())).map(RoleDTO::getId).toList();
        if (CollectionUtil.isEmpty(roleIds)) {
            // 无权限
            userNames.add("-1");
            roleDataPerVO.setUserNames(userNames);
            return roleDataPerVO;
        }
        List<RoleDataPermissionDO> roleDataByRoleId = roleDataPerRepository.getRoleDataByRoleIds(roleIds);

        if (CollectionUtil.isEmpty(roleDataByRoleId)) {
            //所有权限
            return null;
        }
        Map<String, List<RoleDataPermissionDO>> roleDataMap = roleDataByRoleId.stream().collect(Collectors.groupingBy(RoleDataPermissionDO::getRoleId));

        for (String roleId : roleIds) {
            List<RoleDataPermissionDO> roleDataList = roleDataMap.getOrDefault(roleId, new ArrayList<>());
            if (CollectionUtil.isNotEmpty(roleDataList)) {
                List<RoleDataPermissionDO> moduleListById = roleDataList.stream().filter(roleData ->
                        DataPermissionTypeEnum.MODULE.getCode().equals(roleData.getType()) &&
                                moduleId.equals(roleData.getDataPermissionId())
                ).toList();
                if (CollectionUtil.isEmpty(moduleListById)) {
                    //所有权限
                    return null;
                }
            } else {
                //所有权限
                return null;
            }
        }
        // 用户权限
        List<String> userRoleDataList = roleDataByRoleId.stream().filter(roleData ->
                DataPermissionTypeEnum.USER.getCode().equals(roleData.getType())
        ).map(RoleDataPermissionDO::getDataPermissionId).toList();

        if (CollectionUtil.isNotEmpty(userRoleDataList)) {
            List<UserDTO> userDTOS = userRepository.selectUserByIds(userRoleDataList);
            userNames.addAll(userDTOS.stream().map(UserDTO::getUserName).toList());
        }
        userNames.add(SecurityUtils.getUsername());

        // 部门权限
        List<String> deptIds = roleDataByRoleId.stream()
                .filter(roleData -> DataPermissionTypeEnum.DEPT.getCode().equals(roleData.getType()))
                .map(RoleDataPermissionDO::getDataPermissionId)
                .flatMap(deptId -> {
                    // 本部门
                    if ("0".equals(deptId)) {
                        UserDTO userDTO = userRepository.selectUserById(userId);
                        return userDTO.getDepts().stream()
                                .map(DeptDTO::getId);
                    } else {
                        return Stream.of(deptId);
                    }
                })
                .toList();
        roleDataPerVO.setUserNames(userNames.stream().distinct().toList());
        roleDataPerVO.setDeptIds(deptIds.stream().distinct().toList());


        return roleDataPerVO;
    }
}
