package com.purchase.purchase_server.entity.dataObject;

import com.purchase.purchase_server.entity.BaseEntity;
import lombok.Data;
import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.experimental.SuperBuilder;

import java.util.Date;


/**
 * @Date 2024/5/8 9:15
 **/
@EqualsAndHashCode(callSuper = true)
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@TableName("cm_replenishment_calculation_log")
public class ReplenishmentCalculationLogDO extends BaseEntity {

  private String type;
  private String info;
  private String virtualSku;
  private String warehouse;

  public ReplenishmentCalculationLogDO(String type, String virtualSku, String warehouse) {
    super();
    super.setCreateDate(new Date());
    this.type = type;
    this.virtualSku = virtualSku;
    this.warehouse = warehouse;
  }
}
