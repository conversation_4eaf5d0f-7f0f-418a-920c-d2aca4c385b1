package com.purchase.purchase_server.assembler;

import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.purchase.purchase_server.entity.dataObject.FactoryContainerDO;
import com.purchase.purchase_server.entity.dataObject.FactoryFinancialDO;
import com.purchase.purchase_server.entity.dataObject.FactoryInfoDO;
import com.purchase.purchase_server.entity.dataObject.ShipmentPlanDO;
import com.purchase.purchase_server.entity.dto.factory.FactoryContainerDto;
import com.purchase.purchase_server.entity.dto.factory.FactoryFinancialDto;
import com.purchase.purchase_server.entity.dto.factory.FactoryInfoDto;
import com.purchase.purchase_server.entity.excelObject.FactoryContainerExcel;
import com.purchase.purchase_server.entity.excelObject.FactoryInfoAndFinancialExcel;
import com.purchase.purchase_server.entity.excelObject.ShipmentPlanExcel;
import com.purchase.purchase_server.enums.FactoryFinancialPaymentTimeEnum;
import com.purchase.purchase_server.enums.FactoryFinancialSettementEnum;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;

import java.util.List;


/**
 * 数据转换工具类
 *
 * <AUTHOR>
 * @date 2024/5/11
 */

@Mapper(componentModel = "spring", uses = ShipmentConvert.class)
public interface ShipmentAssembler {
    ShipmentPlanDO planExcelToDO(ShipmentPlanExcel val);

}














