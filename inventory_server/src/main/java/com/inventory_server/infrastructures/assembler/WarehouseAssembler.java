package com.inventory_server.infrastructures.assembler;

import com.inventory_server.model.warehouse.entity.dos.WarehouseSenboInfoDO;
import com.inventory_server.model.warehouse.entity.dos.WarehouseYiCangInfoDO;
import com.inventory_server.model.warehouse.entity.dto.SenboWarehouseDto;
import com.inventory_server.model.warehouse.entity.dto.WarehouseDetailDto;
import com.inventory_server.model.warehouse.entity.form.SenboWarehouseForm;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import java.util.List;

@Mapper(componentModel = "spring", uses = RedundantInventoryConvert.class)
public interface WarehouseAssembler {

//    @Mapping(source = "warehouseCode", target = "ycWarehouseCode")
    List<WarehouseYiCangInfoDO> convertYiCangDtoToYiCangInfoDO(List<WarehouseDetailDto> warehouseDetailList);

    @Mapping(source = "senboWarehouseId", target = "id")
    @Mapping(source = "senboWarehouse", target = "warehouse")
    @Mapping(source = "urgentHeadShippingDate", target = "urgentHeadShipDate")
    WarehouseSenboInfoDO convertSenboWarehouseFormToInfoDO(SenboWarehouseForm form);

    @Mapping(source = "id", target = "senboWarehouseId")
    @Mapping(source = "warehouse", target = "senboWarehouse")
    SenboWarehouseDto convertSenboWarehouseDOToInfoDTO(WarehouseSenboInfoDO val);

}
