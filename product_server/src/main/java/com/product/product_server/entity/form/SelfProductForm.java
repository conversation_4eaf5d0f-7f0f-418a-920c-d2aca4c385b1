package com.product.product_server.entity.form;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.crafts_mirror.utils.dp.BasePageForm;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * @Description 自定义商品列表查询表单
 * <AUTHOR>
 * @Date 2023/12/6 11:07
 **/
@EqualsAndHashCode(callSuper = true)
@Data
public class SelfProductForm extends BasePageForm implements Serializable {
    @Serial
    private static final long serialVersionUID = -1439495840202434L;

    /**
     * 品名
     */
    private String productName;

    /**
     * sku
     */
    private String sku;

    private List<String> selfSkuList;

    private List<String> selfSkuIdList;
    /**
     * 采购人员
     */
    private String buyer;
    /**
     * 供应商代码
     */
    private String factoryCode;
    /**
     * 品类id
     */
    private String categoryId;

    /**
     * 是否商检
     */
    private Integer commodityInspection;

    /**
     * 件的类型
     * @see com.product.product_server.enums.PCSTypeEnum
     */
    private Integer pcsType;
}
