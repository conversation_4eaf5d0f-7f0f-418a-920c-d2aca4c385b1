package com.purchase.purchase_server.entity.vo.Lcl;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @Description 排柜拼柜列表页信息数据
 * <AUTHOR>
 * @Date 2025/5/25 11:47
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
public class LclContainerInfoVo implements Serializable {
    private String containerId;

    private String shipmentCode;

    private String shippingStartDate;

    private String sourceType;

    private String destinationWarehouse;

    private String addressCode;

    /**
     * 供应商数量
     */
    private Integer factoryCount;

    private Integer shippingQuantity;

    /**
     * 发货体积
     */
    private Double shippingVolume;

    /**
     * 容器装载率
     */
    private Double containerLoadingRate;

    /**
     * 盈余体积
     */
    private Double surplusVolume;

    /**
     * 商检体积占比
     */
    private Double inspectionVolumeRatio;

    /**
     * 发货重量
     */
    private Double shippingWeight;

    /**
     * 货件大类
     */
    private String shipmentCategory;
    /**
     * 货件状态
     */
    private Integer shipmentStatus;

    /**
     * 装载状态
     */
    private Integer loadingStatus;

    /**
     * 运输方式
     */
    private Integer transportationMode;
}
