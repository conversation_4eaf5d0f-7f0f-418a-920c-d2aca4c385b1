package com.purchase.purchase_server.entity.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @Description 补货试算看板本地库存建议返回给前端的数据
 * <AUTHOR>
 * @Date 2024/1/22 14:41
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ReplenishmentDetailWatchBoardShippingVo implements Serializable {
    private TrialMockInventoryVo trialMockInventoryVo;

    private TrialWatchBoardVo trialWatchBoardVo;
}
