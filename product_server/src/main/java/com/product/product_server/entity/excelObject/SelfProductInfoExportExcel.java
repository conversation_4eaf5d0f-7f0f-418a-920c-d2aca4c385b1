package com.product.product_server.entity.excelObject;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.*;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @Description excel导出产品转换成的实体列
 * <AUTHOR>
 * @Date 2023/12/7 17:00
 **/
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SelfProductInfoExportExcel extends BaseExcel implements Serializable {

    @Serial
    private static final long serialVersionUID = 3559544387200290394L;
    @ExcelProperty("* 大类")
    private String category;

    @ExcelProperty("* SKU")
    private String sku;

    @ExcelProperty("* 产品名称")
    private String productName;

    @ExcelProperty("图片链接")
    private String image;

    @ExcelProperty("* 产品经理")
    private String buyer;

    @ExcelProperty("* 供应商代码")
    private String factoryCode;

    @ExcelProperty("自定义SKU备注")
    private String selfRemarks;

    @ExcelProperty("渠道")
    private String channel;

    @ExcelProperty("父SPU ID")
    private String spu;

    @ExcelProperty("父SPU品名")
    private String spuProductName;

    @ExcelProperty("老SKU")
    private String oldSku;

    @ExcelProperty("虚拟SKU")
    private String virtualSku;

    @ExcelProperty("运营")
    private String operator;

    @ExcelProperty("子体类型")
    private String subType;

    @ExcelProperty("产品状态")
    private String productStatus;

    @ExcelProperty("产品类型")
    private String productType;

    @ExcelProperty("虚拟SKU备注")
    private String virtualRemarks;

    @ExcelProperty("* 外箱规格长")
    private Double caseLength;

    @ExcelProperty("* 外箱规格宽")
    private Double caseWidth;

    @ExcelProperty("* 外箱规格高")
    private Double caseHeight;

    @ExcelProperty("* 重量单位")
    private String weightUnit;

    @ExcelProperty("* 单箱毛重")
    private Double singleCaseGrossWeight;

    @ExcelProperty("* 单箱数量(pcs)")
    private Integer containerLoad;

    @ExcelProperty("* 是否商检")
    private String commodityInspection;

    @ExcelProperty("* 币种")
    private String currency;

    @ExcelProperty("* 含税")
    private String taxes;

    @ExcelProperty("* 生产交期")
    private String purchaseDate;

    @ExcelProperty("* 单价")
    private BigDecimal price;

    @ExcelProperty("含税单价")
    private BigDecimal priceWithTaxes;

    @ExcelProperty("借货策略")
    private String borrowingStrategy;

    @ExcelIgnore
    private String categoryId;
}
