package com.purchase.purchase_server.repository.dataRepository;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.crafts_mirror.common.security.dataPermission.DataPermission;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.purchase.purchase_server.entity.bo.RepExcelProjectSkuBO;
import com.purchase.purchase_server.entity.bo.RepExcelTrialPurchaseBO;
import com.purchase.purchase_server.entity.bo.RepVirtualSkuDateBO;
import com.purchase.purchase_server.entity.dataObject.*;
import com.purchase.purchase_server.entity.form.ReplenishmentProjectForm;
import com.purchase.purchase_server.enums.*;
import com.purchase.purchase_server.mapper.ReplenishmentProjectMapper;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

import static com.crafts_mirror.utils.constant.SecurityConstants.ROLE_DATA_PER_REPLENISH;

/**
 * <AUTHOR>
 * @description 针对表【cm_replenishment_project】的数据库操作Service实现
 * @createDate 2024-01-17 10:45:51
 */
@Service
public class ReplenishmentProjectRepositoryImpl extends ServiceImpl<ReplenishmentProjectMapper, ReplenishmentProjectDO> {

    public IPage<RepVirtualSkuDateBO> pageList(ReplenishmentProjectForm form) {
        IPage<RepVirtualSkuDateBO> iPage = new Page<>(form.getCurrent(), form.getSize());
        return this.baseMapper.selectJoinPage(iPage, RepVirtualSkuDateBO.class, buildFlowPageQueryWrapper(form, true));
    }

    public List<RepVirtualSkuDateBO> selectCountList(ReplenishmentProjectForm form) {
        return this.baseMapper.selectJoinList(RepVirtualSkuDateBO.class, buildFlowPageQueryWrapper(form, false));
    }

    public static MPJLambdaWrapper<ReplenishmentProjectDO> buildFlowPageQueryWrapper(ReplenishmentProjectForm query, boolean flag) {
        String dataPermissionSql = flag ? new DataPermission().getDataPermissionUser(ROLE_DATA_PER_REPLENISH, "t3") : null;

        MPJLambdaWrapper<ReplenishmentProjectDO> LambdaWrapper = new MPJLambdaWrapper<>();
        LambdaWrapper.select(ReplenishmentVirtualSkuPurchaseDO::getId)
                .selectAs(ReplenishmentProjectDO::getId, "replenishmentProjectId")
                .select(ReplenishmentProjectDO::getFullLinkDaysBeforeSoldOut)
                .select(ReplenishmentProjectDO::getFullLinkTheoreticalSoldOutDate)
                .select(ReplenishmentVirtualSkuPurchaseDO::getExpectedSoldOutDate)
                .select("t3.self_data ->> '$.image' as image")
                .select("t3.self_data ->> '$.buyer' as buyer")
                .selectAs(ProductSnapshotDO::getVirtualSku, "destination_sku")
                .select("t3.virtual_data ->> '$.channel' as channel")
                .select("t3.virtual_data ->> '$.subType' as sub_type")
                .select("t3.virtual_data ->> '$.productStatus' as product_status")
                .select("t3.virtual_data ->> '$.productType' as product_type")
                .select("t3.virtual_data ->> '$.operator' as operator")
                .select("t3.virtual_data ->> '$.oldSku' as oldSku")
                .select(ProductSnapshotDO::getSelfSku)
                .select("t3.self_data->>'$.productName' as product_name")
                .select(FactoryInfoDO::getFactoryCode)
                .select(FactoryInfoDO::getPurchaser)

                .select(ReplenishmentVirtualSkuPurchaseDO::getLocalInventory)
                .select(ReplenishmentVirtualSkuPurchaseDO::getUpdateDate)
                .select("SUM(CASE WHEN t2.purchase_num_type = 0 THEN t2.advice_purchase_num ELSE 0 END) AS advicePurchaseNum")
                .select("SUM(CASE WHEN t2.purchase_num_type = 1 THEN t2.advice_purchase_num ELSE 0 END) AS actualReplenishmentNum")
                .select("SUM(CASE WHEN t2.purchase_num_type = 2 THEN t2.advice_purchase_num ELSE 0 END) as operationConfirmedNum")
                .select(ReplenishmentVirtualSkuPurchaseDO::getReason)
                .select(ReplenishmentVirtualSkuPurchaseDO::getConfirmedReason)
                .select(ReplenishmentVirtualSkuPurchaseDO::getOperatorRemark)

                .select(ReplenishmentVirtualSkuPurchaseDO::getActualDailySalesNum)
                .select(ReplenishmentVirtualSkuPurchaseDO::getTargetSalesNum)
                .select(ReplenishmentVirtualSkuPurchaseDO::getSubEntityRate)
                .select(ReplenishmentVirtualSkuPurchaseDO::getParentEntityRate)
                .select("(CASE WHEN EXISTS (SELECT 1 FROM cm_replenishment_trial_purchase_inventory sub WHERE sub.replenishment_virtual_purchase_id = t1.id AND sub.purchase_num_type = 2 " +
                        (StrUtil.isNotBlank(query.getAdvicePurchaseStartDate()) && StrUtil.isNotBlank(query.getAdvicePurchaseEndDate()) ?
                                "AND sub.advice_purchase_date BETWEEN '" + query.getAdvicePurchaseStartDate() +
                                        "' AND '" + query.getAdvicePurchaseEndDate() + "'" : "") + " AND (sub.operation_confirmed_status = 0 OR sub.operation_confirmed_status IS NULL)) THEN '未确认' ELSE '已确认' END) as operation_confirmed_status")
                .leftJoin(ReplenishmentVirtualSkuPurchaseDO.class, ReplenishmentVirtualSkuPurchaseDO::getReplenishmentProjectId, ReplenishmentProjectDO::getId)
                .leftJoin(ReplenishmentTrialPurchaseInventoryDO.class, on -> on
                        .eq(ReplenishmentTrialPurchaseInventoryDO::getReplenishmentVirtualPurchaseId, ReplenishmentVirtualSkuPurchaseDO::getId)
                        .between(StrUtil.isNotBlank(query.getAdvicePurchaseStartDate()) && StrUtil.isNotBlank(query.getAdvicePurchaseEndDate()),
                                ReplenishmentTrialPurchaseInventoryDO::getAdvicePurchaseDate, query.getAdvicePurchaseStartDate(), query.getAdvicePurchaseEndDate()))
                .leftJoin(ProductSnapshotDO.class, ProductSnapshotDO::getId, ReplenishmentVirtualSkuPurchaseDO::getProductSnapshotId)
                .leftJoin(SelfProductDO.class, SelfProductDO::getId, ProductSnapshotDO::getSelfSkuId)
                .leftJoin(FactoryInfoDO.class, FactoryInfoDO::getId, SelfProductDO::getFactoryId)

                .eq(ReplenishmentProjectDO::getReplenishmentRecordId, query.getId())
                //是否打折
                .func(StrUtil.isNotBlank(query.getIsDiscount()), i -> {
                    if (ViReplenishmentIsDiscountEnum.NORMAL.getCode().equals(query.getIsDiscount())) {
                        i.like(ReplenishmentVirtualSkuPurchaseDO::getReason, ReplenishmentStatusReasonEnum.NORMAL.getDesc());
                    } else if (ViReplenishmentIsDiscountEnum.DISCOUNT.getCode().equals(query.getIsDiscount())) {
                        i.and(j -> {
                            j.like(ReplenishmentVirtualSkuPurchaseDO::getReason, ReplenishmentStatusReasonEnum.PARENT_STANDARD_CHILD_NOT.getDesc())
                                    .or()
                                    .like(ReplenishmentVirtualSkuPurchaseDO::getReason, ReplenishmentStatusReasonEnum.PARENT_NOT_CHILD_STANDARD.getDesc());

                        });

                    } else if (ViReplenishmentIsDiscountEnum.NO_REP.getCode().equals(query.getIsDiscount())) {
                        i.and(j -> {
                            j.like(ReplenishmentVirtualSkuPurchaseDO::getReason, ReplenishmentStatusReasonEnum.PARENT_NOT.getDesc())
                                    .or()
                                    .like(ReplenishmentVirtualSkuPurchaseDO::getReason, ReplenishmentStatusReasonEnum.PARENT_CHILD_NOT.getDesc());
                        });
                    }
                })
                // 运营确认状态筛选
                .apply(StrUtil.isNotBlank(query.getOperationConfirmedStatus()),
                        OperationConfirmedStatusEnum.CONFIRMED.getCode().equals(query.getOperationConfirmedStatus()) ?
                                "NOT EXISTS (SELECT 1 FROM cm_replenishment_trial_purchase_inventory sub WHERE sub.replenishment_virtual_purchase_id = t1.id AND sub.purchase_num_type = 2 " +
                                        (StrUtil.isNotBlank(query.getAdvicePurchaseStartDate()) && StrUtil.isNotBlank(query.getAdvicePurchaseEndDate()) ?
                                                "AND sub.advice_purchase_date BETWEEN '" + query.getAdvicePurchaseStartDate() +
                                                        "' AND '" + query.getAdvicePurchaseEndDate() + "'" : "") + " AND (sub.operation_confirmed_status = 0 OR sub.operation_confirmed_status IS NULL))" :
                                "EXISTS (SELECT 1 FROM cm_replenishment_trial_purchase_inventory sub WHERE sub.replenishment_virtual_purchase_id = t1.id AND sub.purchase_num_type = 2 " +
                                        (StrUtil.isNotBlank(query.getAdvicePurchaseStartDate()) && StrUtil.isNotBlank(query.getAdvicePurchaseEndDate()) ?
                                                "AND sub.advice_purchase_date BETWEEN '" + query.getAdvicePurchaseStartDate() +
                                                        "' AND '" + query.getAdvicePurchaseEndDate() + "'" : "") + " AND (sub.operation_confirmed_status = 0 OR sub.operation_confirmed_status IS NULL))")
                .in(CollectionUtil.isNotEmpty(query.getSelfSkuList()), ProductSnapshotDO::getSelfSku, query.getSelfSkuList())
                .in(CollectionUtil.isNotEmpty(query.getVirtualSkuList()), ProductSnapshotDO::getVirtualSku, query.getVirtualSkuList())
                .in(CollectionUtil.isNotEmpty(query.getOldSkuList()), "t3.virtual_data ->> '$.channel'", query.getOldSkuList())
                //.between(StrUtil.isNotBlank(query.getAdvicePurchaseStartDate()) && StrUtil.isNotBlank(query.getAdvicePurchaseEndDate()),
                //        ReplenishmentTrialPurchaseInventoryDO::getAdvicePurchaseDate, query.getAdvicePurchaseStartDate(), query.getAdvicePurchaseEndDate())
                .apply(StrUtil.isNotBlank(query.getProductName()), "t3.self_data->>'$.productName' like {0}", StrUtil.isBlank(query.getProductName()) ? query.getProductName() : "%" + query.getProductName().strip() + "%")
                .eq(StrUtil.isNotBlank(query.getFactoryCode()), FactoryInfoDO::getFactoryCode, Optional.ofNullable(query.getFactoryCode()).map(String::strip).orElse(null))
                .eq(StrUtil.isNotEmpty(query.getProductStatus()), "t3.virtual_data ->> '$.productStatus'", query.getProductStatus())
                .in(CollectionUtil.isNotEmpty(query.getProductStatusList()), "t3.virtual_data ->> '$.productStatus'", query.getProductStatusList())
                .eq(StrUtil.isNotEmpty(query.getSubType()), "t3.virtual_data ->> '$.subType'", query.getSubType())
                .apply(StrUtil.isNotBlank(query.getOperator()), "FIND_IN_SET('" + query.getOperator() + "',t3.virtual_data->>'$.operator')")
                .groupBy(ReplenishmentProjectDO::getId)
                .groupBy(ReplenishmentProjectDO::getFullLinkDaysBeforeSoldOut)
                .groupBy(ReplenishmentProjectDO::getFullLinkTheoreticalSoldOutDate)
                .groupBy(ReplenishmentVirtualSkuPurchaseDO::getId)
                .groupBy("t3.self_data ->> '$.image'")
                .groupBy(ProductSnapshotDO::getVirtualSku)
                .groupBy("t3.virtual_data ->> '$.channel'")
                .groupBy("t3.virtual_data ->> '$.subType'")
                .groupBy("t3.virtual_data ->> '$.productStatus'")
                .groupBy("t3.virtual_data ->> '$.productType'")
                .groupBy("t3.self_data->>'$.productName'")
                .groupBy(ProductSnapshotDO::getSelfSku)
                .groupBy(FactoryInfoDO::getFactoryCode)
                .groupBy(FactoryInfoDO::getPurchaser)
                .groupBy("t3.self_data ->> '$.buyer'")
                .groupBy(ReplenishmentVirtualSkuPurchaseDO::getLocalInventory)
                .groupBy(ReplenishmentVirtualSkuPurchaseDO::getUpdateDate)
                .groupBy(ReplenishmentVirtualSkuPurchaseDO::getReason)
                .groupBy(ReplenishmentVirtualSkuPurchaseDO::getConfirmedReason)
                .having(StrUtil.isNotBlank(query.getIsNotReplenishment()) &&
                        ViReplenishmentStatusEnum.RESTOCK_REQUIRED.getCode().equals(query.getIsNotReplenishment()), "SUM(CASE WHEN t2.purchase_num_type = 0 THEN t2.advice_purchase_num ELSE 0 END) > 0")
                .having(StrUtil.isNotBlank(query.getIsNotReplenishment()) &&
                        ViReplenishmentStatusEnum.NOT_RESTOCK_REQUIRED.getCode().equals(query.getIsNotReplenishment()), "COALESCE(SUM( CASE WHEN t2.purchase_num_type = 0 THEN t2.advice_purchase_num ELSE 0 END ), 0) = 0")
                .apply(StrUtil.isNotBlank(dataPermissionSql), dataPermissionSql);
        if (StrUtil.isNotBlank(query.getSort()) && StrUtil.isNotBlank(query.getDirection())) {
            if (RepVirtualSkuPurchaseDirectionAndSortEnum.ofCode(query.getSort()) != null) {
                LambdaWrapper.orderBy(true, RepVirtualSkuPurchaseDirectionAndSortEnum.ASC.getCode().equals(query.getDirection()), RepVirtualSkuPurchaseDirectionAndSortEnum.sortMap.getOrDefault(query.getSort(), ReplenishmentVirtualSkuPurchaseDO::getReplenishmentProjectId));
            } else if (RepProjectDirectionAndSortEnum.ofCode(query.getSort()) != null) {
                if (query.getSort().equals(RepProjectDirectionAndSortEnum.FULL_LINK_THEORETICAL_SOLD_OUT_DATE.getCode())) {
                    LambdaWrapper.last("ORDER BY full_link_theoretical_sold_out_date IS NULL ASC, full_link_theoretical_sold_out_date " + query.getDirection());
                } else {
                    LambdaWrapper.orderBy(true, RepProjectDirectionAndSortEnum.ASC.getCode().equals(query.getDirection()), RepProjectDirectionAndSortEnum.sortMap.getOrDefault(query.getSort(), ReplenishmentProjectDO::getReplenishmentRecordId));
                }
            }
        }
        ;
        return LambdaWrapper;
    }

    public List<RepExcelProjectSkuBO> selectExcelRepProjectData(ReplenishmentProjectForm form) {
        try {
            return this.baseMapper.selectJoinList(RepExcelProjectSkuBO.class, buildFlowExcelQueryWrapper(form));
        } catch (Exception e) {
            log.error(e.getMessage());
            throw new RuntimeException(e);
        }
    }

    public static MPJLambdaWrapper<ReplenishmentProjectDO> buildFlowExcelQueryWrapper(ReplenishmentProjectForm query) {
        String dataPermissionSql = new DataPermission().getDataPermissionUser(ROLE_DATA_PER_REPLENISH, "t3");
        MPJLambdaWrapper<ReplenishmentProjectDO> LambdaWrapper = new MPJLambdaWrapper<>();
        LambdaWrapper
                .select(ProductSnapshotDO::getSelfSkuId)
                .select(ProductSnapshotDO::getSelfSku)
                .select("t3.virtual_data->>'$.channel' as channel")
                .select(ProductSnapshotDO::getVirtualSkuId)
                .select(ProductSnapshotDO::getVirtualSku)
                .select(ReplenishmentRulesDO::getHeadShippingDays)
                .leftJoin(ReplenishmentVirtualSkuPurchaseDO.class, ReplenishmentVirtualSkuPurchaseDO::getReplenishmentProjectId, ReplenishmentProjectDO::getId)
                .leftJoin(ReplenishmentTrialPurchaseInventoryDO.class, ReplenishmentTrialPurchaseInventoryDO::getReplenishmentVirtualPurchaseId, ReplenishmentVirtualSkuPurchaseDO::getId)
                .leftJoin(ProductSnapshotDO.class, ProductSnapshotDO::getId, ReplenishmentVirtualSkuPurchaseDO::getProductSnapshotId)
                .leftJoin(SelfProductDO.class, SelfProductDO::getId, ProductSnapshotDO::getSelfSkuId)
                .leftJoin(FactoryInfoDO.class, FactoryInfoDO::getId, SelfProductDO::getFactoryId)
                .leftJoin(ReplenishmentRulesDO.class, ReplenishmentRulesDO::getId, ReplenishmentVirtualSkuPurchaseDO::getRulesId)

                .eq(ReplenishmentProjectDO::getReplenishmentRecordId, query.getId())
                .eq(ReplenishmentVirtualSkuPurchaseDO::getReplenishmentStatus, ViReplenishmentStatusEnum.RESTOCK_REQUIRED.getCode())
                .in(CollectionUtil.isNotEmpty(query.getSelfSkuList()), ProductSnapshotDO::getSelfSku, query.getSelfSkuList())
                .in(CollectionUtil.isNotEmpty(query.getVirtualSkuList()), ProductSnapshotDO::getVirtualSku, query.getVirtualSkuList())
                .in(CollectionUtil.isNotEmpty(query.getOldSkuList()), "t3.virtual_data->>'$.oldSku'", query.getOldSkuList())
                //是否打折
                .func(StrUtil.isNotBlank(query.getIsDiscount()), i -> {
                    if (ViReplenishmentIsDiscountEnum.NORMAL.getCode().equals(query.getIsDiscount())) {
                        i.like(ReplenishmentVirtualSkuPurchaseDO::getReason, ReplenishmentStatusReasonEnum.NORMAL.getDesc());
                    } else if (ViReplenishmentIsDiscountEnum.DISCOUNT.getCode().equals(query.getIsDiscount())) {
                        i.and(j -> {
                            j.like(ReplenishmentVirtualSkuPurchaseDO::getReason, ReplenishmentStatusReasonEnum.PARENT_STANDARD_CHILD_NOT.getDesc())
                                    .or()
                                    .like(ReplenishmentVirtualSkuPurchaseDO::getReason, ReplenishmentStatusReasonEnum.PARENT_NOT_CHILD_STANDARD.getDesc());
                        });

                    } else if (ViReplenishmentIsDiscountEnum.NO_REP.getCode().equals(query.getIsDiscount())) {
                        i.and(j -> {
                            j.like(ReplenishmentVirtualSkuPurchaseDO::getReason, ReplenishmentStatusReasonEnum.PARENT_NOT.getDesc())
                                    .or()
                                    .like(ReplenishmentVirtualSkuPurchaseDO::getReason, ReplenishmentStatusReasonEnum.PARENT_CHILD_NOT.getDesc());
                        });
                    }
                })
                // 运营确认状态筛选
                .apply(StrUtil.isNotBlank(query.getOperationConfirmedStatus()),
                        OperationConfirmedStatusEnum.CONFIRMED.getCode().equals(query.getOperationConfirmedStatus()) ?
                                "NOT EXISTS (SELECT 1 FROM cm_replenishment_trial_purchase_inventory sub WHERE sub.replenishment_virtual_purchase_id = t1.id AND sub.purchase_num_type = 2 " +
                                        (StrUtil.isNotBlank(query.getAdvicePurchaseStartDate()) && StrUtil.isNotBlank(query.getAdvicePurchaseEndDate()) ?
                                                "AND sub.advice_purchase_date BETWEEN '" + query.getAdvicePurchaseStartDate() +
                                                        "' AND '" + query.getAdvicePurchaseEndDate() + "'" : "") + " AND (sub.operation_confirmed_status = 0 OR sub.operation_confirmed_status IS NULL))" :
                                "EXISTS (SELECT 1 FROM cm_replenishment_trial_purchase_inventory sub WHERE sub.replenishment_virtual_purchase_id = t1.id AND sub.purchase_num_type = 2 " +
                                        (StrUtil.isNotBlank(query.getAdvicePurchaseStartDate()) && StrUtil.isNotBlank(query.getAdvicePurchaseEndDate()) ?
                                                "AND sub.advice_purchase_date BETWEEN '" + query.getAdvicePurchaseStartDate() +
                                                        "' AND '" + query.getAdvicePurchaseEndDate() + "'" : "") + " AND (sub.operation_confirmed_status = 0 OR sub.operation_confirmed_status IS NULL))")

                .eq(StrUtil.isNotBlank(query.getTrialStatus()), ReplenishmentTrialPurchaseInventoryDO::getTrialStatus, query.getTrialStatus())
                .between(StrUtil.isNotBlank(query.getAdvicePurchaseStartDate()) && StrUtil.isNotBlank(query.getAdvicePurchaseEndDate()),
                        ReplenishmentTrialPurchaseInventoryDO::getAdvicePurchaseDate, query.getAdvicePurchaseStartDate(), query.getAdvicePurchaseEndDate())
                .apply(StrUtil.isNotBlank(query.getProductName()), "t3.self_data->>'$.productName' like {0}", StrUtil.isBlank(query.getProductName()) ? query.getProductName() : "%" + query.getProductName().strip() + "%")
                .eq(StrUtil.isNotBlank(query.getFactoryCode()), FactoryInfoDO::getFactoryCode, Optional.ofNullable(query.getFactoryCode()).map(String::strip).orElse(null))
                .eq(StrUtil.isNotEmpty(query.getProductStatus()), "t3.virtual_data ->> '$.productStatus'", query.getProductStatus())
                .in(CollectionUtil.isNotEmpty(query.getProductStatusList()), "t3.virtual_data ->> '$.productStatus'", query.getProductStatusList())
                .eq(StrUtil.isNotEmpty(query.getSubType()), "t3.virtual_data ->> '$.subType'", query.getSubType())
                .apply(StrUtil.isNotBlank(query.getOperator()), "FIND_IN_SET('" + query.getOperator() + "',t3.virtual_data->>'$.operator')")
                .groupBy(FactoryInfoDO::getFactoryCode)
                .groupBy(ProductSnapshotDO::getSelfSkuId)
                .groupBy(ProductSnapshotDO::getSelfSku)
                .groupBy("t3.virtual_data->>'$.channel'")
                .groupBy(ProductSnapshotDO::getVirtualSkuId)
                .groupBy(ProductSnapshotDO::getVirtualSku)

                .groupBy(FactoryInfoDO::getFactoryName)
                .groupBy("t3.self_data->>'$.productName'")
                .groupBy("t3.virtual_data->>'$.subType'")
                .groupBy("t3.virtual_data->>'$.productStatus'")
                .groupBy(ReplenishmentVirtualSkuPurchaseDO::getActualDailySalesNum)
                .groupBy(ReplenishmentVirtualSkuPurchaseDO::getTargetSalesNum)
                .groupBy(ReplenishmentVirtualSkuPurchaseDO::getSubEntityRate)
                .groupBy(ReplenishmentVirtualSkuPurchaseDO::getParentEntityRate)
                .groupBy(ReplenishmentVirtualSkuPurchaseDO::getReason)
                .groupBy(ReplenishmentVirtualSkuPurchaseDO::getConfirmedReason)
                .groupBy(ReplenishmentProjectDO::getCreateDate)
                .groupBy(ReplenishmentRulesDO::getHeadShippingDays)
                .having("SUM(CASE WHEN t2.purchase_num_type = 0 THEN t2.advice_purchase_num ELSE 0 END) > 0")
                .apply(StrUtil.isNotBlank(dataPermissionSql), dataPermissionSql)
                .orderByDesc(ProductSnapshotDO::getSelfSku)
                .orderByAsc(ProductSnapshotDO::getVirtualSku)
                .orderByAsc(ReplenishmentProjectDO::getCreateDate);
        return LambdaWrapper;
    }

    public List<RepExcelTrialPurchaseBO> selectExcelRepTrialPurchaseData(ReplenishmentProjectForm form, List<String> virtualSkuIds) {
        try {
            return baseMapper.selectJoinList(RepExcelTrialPurchaseBO.class, buildFlowExcelRepTrialQueryWrapper(form, virtualSkuIds));
        } catch (Exception e) {
            log.error(e.getMessage());
            throw new RuntimeException(e);
        }
    }

    public static MPJLambdaWrapper<ReplenishmentProjectDO> buildFlowExcelRepTrialQueryWrapper(ReplenishmentProjectForm query, List<String> virtualSkuIds) {
        MPJLambdaWrapper<ReplenishmentProjectDO> LambdaWrapper = new MPJLambdaWrapper<>();
        LambdaWrapper
                .select(FactoryInfoDO::getFactoryCode)
                .select(FactoryInfoDO::getPurchaser)
                .select("t3.self_data->>'$.buyer' as buyer")
                .select(ProductSnapshotDO::getSelfSkuId)
                .select(ProductSnapshotDO::getSelfSku)
                .select("t3.self_data->>'$.purchaseDate' as purchaseDate")
                .select("t3.self_data->>'$.containerLoad' as container_load")
                .select("t3.self_data->>'$.productName' as product_name ")
                .select(ProductSnapshotDO::getVirtualSkuId)
                .select(ProductSnapshotDO::getVirtualSku)
                .select("t3.virtual_data->>'$.channel' as channel")
                .select("t3.virtual_data->>'$.subType' as sub_type")
                .select("t3.virtual_data->>'$.productStatus' as product_status")
                .select("t3.virtual_data->>'$.productType' as product_type")
                .select("t3.virtual_data ->> '$.operator' as operator")
                .select(ReplenishmentVirtualSkuPurchaseDO::getActualDailySalesNum)
                .select(ReplenishmentVirtualSkuPurchaseDO::getTargetSalesNum)
                .select(ReplenishmentVirtualSkuPurchaseDO::getSubEntityRate)
                .select(ReplenishmentVirtualSkuPurchaseDO::getParentEntityRate)
                .select(ReplenishmentVirtualSkuPurchaseDO::getReason)
                .select(ReplenishmentVirtualSkuPurchaseDO::getConfirmedReason)
                .select(ReplenishmentVirtualSkuPurchaseDO::getOperatorRemark)
                .select(ReplenishmentProjectDO::getFullLinkDaysBeforeSoldOut)
                .select(ReplenishmentProjectDO::getFullLinkTheoreticalSoldOutDate)
                .select(ReplenishmentTrialPurchaseInventoryDO::getDestinationWarehouse)
                .select(ReplenishmentTrialPurchaseInventoryDO::getAdvicePurchaseNum)
                .select(ReplenishmentTrialPurchaseInventoryDO::getPurchaseNumType)
                .select(ReplenishmentProjectDO::getCreateDate)
                .select(ReplenishmentTrialPurchaseInventoryDO::getAdvicePurchaseDate)
                .select(ReplenishmentTrialPurchaseInventoryDO::getExpectedFactoryFinishedDate)
                .select(ReplenishmentTrialPurchaseInventoryDO::getExpectedShippingStartDate)
                .select(ReplenishmentTrialPurchaseInventoryDO::getExpectedArrivingDate)
                .select(ReplenishmentTrialPurchaseInventoryDO::getIsChangedArrivingDate)
                .select(ReplenishmentRulesDO::getPurchaseDays)
                .select("CEIL(t2.advice_purchase_num / t3.self_data->>'$.containerLoad') as advice_container_load_num")
                .leftJoin(ReplenishmentVirtualSkuPurchaseDO.class, ReplenishmentVirtualSkuPurchaseDO::getReplenishmentProjectId, ReplenishmentProjectDO::getId)
                .leftJoin(ReplenishmentTrialPurchaseInventoryDO.class, ReplenishmentTrialPurchaseInventoryDO::getReplenishmentVirtualPurchaseId, ReplenishmentVirtualSkuPurchaseDO::getId)
                .leftJoin(ProductSnapshotDO.class, ProductSnapshotDO::getId, ReplenishmentVirtualSkuPurchaseDO::getProductSnapshotId)
                .leftJoin(ReplenishmentRulesDO.class, ReplenishmentRulesDO::getId, ReplenishmentVirtualSkuPurchaseDO::getRulesId)
                .leftJoin(SelfProductDO.class, SelfProductDO::getId, ProductSnapshotDO::getSelfSkuId)
                .leftJoin(FactoryInfoDO.class, FactoryInfoDO::getId, SelfProductDO::getFactoryId)

                .eq(ReplenishmentProjectDO::getReplenishmentRecordId, query.getId())
                .eq(ReplenishmentVirtualSkuPurchaseDO::getReplenishmentStatus, ViReplenishmentStatusEnum.RESTOCK_REQUIRED.getCode())
                .in(CollectionUtil.isNotEmpty(query.getSelfSkuList()), ProductSnapshotDO::getSelfSku, query.getSelfSkuList())
                .in(CollectionUtil.isNotEmpty(query.getVirtualSkuList()), ProductSnapshotDO::getVirtualSku, query.getVirtualSkuList())
                //是否打折
                .func(StrUtil.isNotBlank(query.getIsDiscount()), i -> {
                    if (ViReplenishmentIsDiscountEnum.NORMAL.getCode().equals(query.getIsDiscount())) {
                        i.like(ReplenishmentVirtualSkuPurchaseDO::getReason, ReplenishmentStatusReasonEnum.NORMAL.getDesc());
                    } else if (ViReplenishmentIsDiscountEnum.DISCOUNT.getCode().equals(query.getIsDiscount())) {
                        i.and(j -> {
                            j.like(ReplenishmentVirtualSkuPurchaseDO::getReason, ReplenishmentStatusReasonEnum.PARENT_STANDARD_CHILD_NOT.getDesc())
                                    .or()
                                    .like(ReplenishmentVirtualSkuPurchaseDO::getReason, ReplenishmentStatusReasonEnum.PARENT_NOT_CHILD_STANDARD.getDesc());

                        });

                    } else if (ViReplenishmentIsDiscountEnum.NO_REP.getCode().equals(query.getIsDiscount())) {
                        i.and(j -> {
                            j.like(ReplenishmentVirtualSkuPurchaseDO::getReason, ReplenishmentStatusReasonEnum.PARENT_NOT.getDesc())
                                    .or()
                                    .like(ReplenishmentVirtualSkuPurchaseDO::getReason, ReplenishmentStatusReasonEnum.PARENT_CHILD_NOT.getDesc());

                        });
                    }
                })
                // 运营确认状态筛选
                .apply(StrUtil.isNotBlank(query.getOperationConfirmedStatus()),
                        OperationConfirmedStatusEnum.CONFIRMED.getCode().equals(query.getOperationConfirmedStatus()) ?
                                "NOT EXISTS (SELECT 1 FROM cm_replenishment_trial_purchase_inventory sub WHERE sub.replenishment_virtual_purchase_id = t1.id AND sub.purchase_num_type = 2 " +
                                        (StrUtil.isNotBlank(query.getAdvicePurchaseStartDate()) && StrUtil.isNotBlank(query.getAdvicePurchaseEndDate()) ?
                                                "AND sub.advice_purchase_date BETWEEN '" + query.getAdvicePurchaseStartDate() +
                                                        "' AND '" + query.getAdvicePurchaseEndDate() + "'" : "") + " AND (sub.operation_confirmed_status = 0 OR sub.operation_confirmed_status IS NULL))" :
                                "EXISTS (SELECT 1 FROM cm_replenishment_trial_purchase_inventory sub WHERE sub.replenishment_virtual_purchase_id = t1.id AND sub.purchase_num_type = 2 " +
                                        (StrUtil.isNotBlank(query.getAdvicePurchaseStartDate()) && StrUtil.isNotBlank(query.getAdvicePurchaseEndDate()) ?
                                                "AND sub.advice_purchase_date BETWEEN '" + query.getAdvicePurchaseStartDate() +
                                                        "' AND '" + query.getAdvicePurchaseEndDate() + "'" : "") + " AND (sub.operation_confirmed_status = 0 OR sub.operation_confirmed_status IS NULL))")

                .between(StrUtil.isNotBlank(query.getAdvicePurchaseStartDate()) && StrUtil.isNotBlank(query.getAdvicePurchaseEndDate()),
                        ReplenishmentTrialPurchaseInventoryDO::getAdvicePurchaseDate, query.getAdvicePurchaseStartDate(), query.getAdvicePurchaseEndDate())
                .apply(StrUtil.isNotBlank(query.getProductName()), "t3.self_data->>'$.productName' like {0}", StrUtil.isBlank(query.getProductName()) ? query.getProductName() : "%" + query.getProductName().strip() + "%")
                .in(CollectionUtil.isNotEmpty(query.getOldSkuList()), "t3.virtual_data->>'$.oldSku'", query.getOldSkuList())
                .eq(StrUtil.isNotBlank(query.getFactoryCode()), FactoryInfoDO::getFactoryCode, Optional.ofNullable(query.getFactoryCode()).map(String::strip).orElse(null))
                .eq(StrUtil.isNotBlank(query.getTrialStatus()), ReplenishmentTrialPurchaseInventoryDO::getTrialStatus, query.getTrialStatus())
                .eq(StrUtil.isNotEmpty(query.getProductStatus()), "t3.virtual_data ->> '$.productStatus'", query.getProductStatus())
                .eq(StrUtil.isNotEmpty(query.getSubType()), "t3.virtual_data ->> '$.subType'", query.getSubType())
                .in(CollectionUtil.isNotEmpty(virtualSkuIds), ReplenishmentVirtualSkuPurchaseDO::getVirtualSkuId, virtualSkuIds)
                .orderByDesc(FactoryInfoDO::getFactoryCode)
                .orderByDesc(ProductSnapshotDO::getSelfSku)
                .orderByDesc(ProductSnapshotDO::getVirtualSku)
                .orderByDesc(ReplenishmentTrialPurchaseInventoryDO::getDestinationWarehouse)
                .orderByAsc(ReplenishmentProjectDO::getCreateDate)
                .orderByAsc(ReplenishmentTrialPurchaseInventoryDO::getAdvicePurchaseDate);
        return LambdaWrapper;
    }

    public List<ReplenishmentProjectDO> selectByRecordId(String recordId) {
        return this.baseMapper.selectList(Wrappers.<ReplenishmentProjectDO>lambdaQuery()
                .eq(ReplenishmentProjectDO::getReplenishmentRecordId, recordId)
        );
    }

    public void deleteByIds(List<String> ids) {
        if (CollectionUtil.isNotEmpty(ids)) {
            baseMapper.deleteByIds(ids);
        }
    }

    public List<String> selectRecordIdBySnap(List<String> snapIds) {
        return baseMapper.selectJoinList(String.class, new MPJLambdaWrapper<ReplenishmentProjectDO>()
                .distinct()
                .select(ReplenishmentProjectDO::getReplenishmentRecordId)
                .leftJoin(ReplenishmentVirtualSkuPurchaseDO.class, ReplenishmentVirtualSkuPurchaseDO::getReplenishmentProjectId, ReplenishmentProjectDO::getId)
                .in(CollectionUtil.isNotEmpty(snapIds), ReplenishmentVirtualSkuPurchaseDO::getProductSnapshotId, snapIds)
        );
    }


}




