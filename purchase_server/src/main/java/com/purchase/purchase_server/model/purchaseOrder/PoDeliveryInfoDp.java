package com.purchase.purchase_server.model.purchaseOrder;

import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.purchase.purchase_server.entity.excelObject.PoDeliveryScheduleExcel;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.stream.Stream;

import static com.crafts_mirror.utils.constant.DateFormatConstant.YYYY_MM_DD_DATE_FORMAT_SLASH;
import static com.crafts_mirror.utils.constant.DateFormatConstant.YYYY_M_D_DATE_FORMAT_SLASH;

/**
 * @Description 采购单交货计划导入-计划数据校验
 * <AUTHOR>
 * @Date 2024/11/11 10:56
 **/
public record PoDeliveryInfoDp(PoDeliveryScheduleExcel data) {
    public PoDeliveryInfoDp(PoDeliveryScheduleExcel data) {
        this.data = data;
        checkFillingRules(data);
        getTime(data.getDeliveryDate());
        checkNum(data.getQtyEta(), data.getDeliveryQuantity());


    }

    private void checkFillingRules(PoDeliveryScheduleExcel product) {
        boolean hasRefNo = StrUtil.isNotBlank(product.getRefNo());
        boolean hasDestinationSku = StrUtil.isNotBlank(product.getDestinationSku());
        boolean hasQtyEta = StrUtil.isNotBlank(product.getQtyEta());
        boolean hasDeliveryDate = StrUtil.isNotBlank(product.getDeliveryDate());
        boolean hasDeliveryQuantity = StrUtil.isNotBlank(product.getDeliveryQuantity());

        // 统计非空字段数量
        long filledFieldCount = Stream.of(hasDestinationSku, hasQtyEta, hasDeliveryDate, hasDeliveryQuantity)
                .filter(b -> b)
                .count();

        // 判断填写情况是否符合要求
        boolean isValidCase1 = hasRefNo && filledFieldCount == 0; // 只填参考号
        boolean isValidCase2 = hasRefNo && hasDestinationSku && hasQtyEta && filledFieldCount == 2; // 填写参考号、虚拟sku和采购数量
        boolean isValidCase3 = hasRefNo && filledFieldCount == 4; // 全部填写

        if (!isValidCase1 && !isValidCase2 && !isValidCase3) {
            throw new IllegalArgumentException("数据填写不符合要求，只允许以下三种填写方式：1.仅填写参考号 2.填写参考号、虚拟SKU和采购数量 3.全部填写");
        }
    }

    public void getTime(String daysDuration) {
        DateTimeFormatter inputFormatter = DateTimeFormatter.ofPattern(YYYY_M_D_DATE_FORMAT_SLASH);
        DateTimeFormatter outputFormatter = DateTimeFormatter.ofPattern(YYYY_MM_DD_DATE_FORMAT_SLASH);
        try {
            if (StrUtil.isBlank(daysDuration)) {
                return;
            }
            // 尝试解析日期字符串
            LocalDate date = LocalDate.parse(daysDuration, inputFormatter);
            // 将日期格式化为所需的格式
            date.format(outputFormatter);
        } catch (DateTimeParseException e) {
            throw new IllegalArgumentException("工厂交期-请正确填写日期");
        }
    }

    public void checkNum(String qtyEta, String deliveryQuantity) {
        if (StrUtil.isNotBlank(qtyEta)) {
            if (!(NumberUtil.isInteger(qtyEta) && Integer.parseInt(qtyEta) > 0)) {
                throw new IllegalArgumentException("采购数量-请正确填写数字");
            }
        }
        if (StrUtil.isNotBlank(deliveryQuantity)) {
            if (!(NumberUtil.isInteger(deliveryQuantity) && Integer.parseInt(deliveryQuantity) >= 0)) {
                throw new IllegalArgumentException("未安排数量-请正确填写数字");
            }
        }
    }
}
