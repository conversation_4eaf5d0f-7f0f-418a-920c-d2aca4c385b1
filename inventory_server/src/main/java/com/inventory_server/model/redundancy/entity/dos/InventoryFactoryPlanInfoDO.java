package com.inventory_server.model.redundancy.entity.dos;

import com.baomidou.mybatisplus.annotation.TableName;
import com.inventory_server.infrastructures.entity.PhysicalBaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.Date;


@EqualsAndHashCode(callSuper = true)
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@TableName("cm_inventory_factory_plan_info")
public class InventoryFactoryPlanInfoDO extends PhysicalBaseEntity {

  private String inventoryInfoId;
  private String contractCode;
  private Date factoryFinishedDate;
  private Integer storeNum;
  private Integer shippingNum;
  private String virtualSkuId;
  private Integer isOld;
  private String remarks;
}
