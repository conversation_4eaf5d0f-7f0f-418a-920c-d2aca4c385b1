package com.purchase.purchase_server.entity.dataObject;

import com.baomidou.mybatisplus.annotation.TableName;
import com.purchase.purchase_server.entity.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * @Description 发货计划实体类
 * <AUTHOR>
 * @Date 2024/1/9 19:13
 **/
@EqualsAndHashCode(callSuper = true)
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@TableName("cm_shipping_project")
public class ShippingProjectDO extends BaseEntity {
    /**
     * 虚拟skuId
     */
    private String virtualSkuId;
    /**
     * 发货记录ID
     */
    private String shippingRecordId;

    private String isTestUnsalableInventory;
}
