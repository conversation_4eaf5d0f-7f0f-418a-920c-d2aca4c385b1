package com.inventory_server.model.redundancy.entity.dp;


import com.inventory_server.applications.dto.FactoryRemainInventoryDto;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

public record AmStockQuantityDp(String destinationSku, List<FactoryRemainInventoryDto> list) {

    public AmStockQuantityDp(String destinationSku, List<FactoryRemainInventoryDto> list) {
        this.destinationSku = destinationSku;
        this.list = Optional.ofNullable(list).orElse(new ArrayList<>());
    }

    public void addStockQuantity(FactoryRemainInventoryDto infoExcel) {
        if (!(Optional.ofNullable(infoExcel.getWarehouse()).isPresent() &&
                Optional.ofNullable(infoExcel.getStoreNum()).isPresent() &&
                Optional.ofNullable(infoExcel.getVirtualSku()).isPresent() &&
                Optional.ofNullable(infoExcel.getEnableUsingDate()).isPresent())) {
            throw new IllegalArgumentException("请填写内容");
        }

        double quantity = infoExcel.getStoreNum();
        // 如果数量为0的话，该行数据过滤掉，在试算以及后续结果展示中都不存在
        if (quantity == 0) {
            return;
        }
        list.stream()
                .filter(item -> item.getWarehouse().equals(infoExcel.getWarehouse())
                        && item.getEnableUsingDate().equals(infoExcel.getEnableUsingDate())
                        && item.getVirtualSku().equals(infoExcel.getVirtualSku())
                )
                .findFirst()
                .ifPresentOrElse(
                        item -> item.setStoreNum(item.getStoreNum() + quantity),
                        () -> list.add(FactoryRemainInventoryDto.builder()
                                .virtualSku(infoExcel.getVirtualSku())
                                .warehouse(infoExcel.getWarehouse())
                                .enableUsingDate(infoExcel.getEnableUsingDate())
                                .storeNum(quantity).build())
                );

    }
}
