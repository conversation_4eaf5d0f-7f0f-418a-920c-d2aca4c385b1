package com.purchase.purchase_server.entity.dto.delivery;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2024/5/10 9:57
 **/
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class DeliveryCalResultFactoryFinishedDto {
    /**
     * 合同号
     */
    private String contractCode;

    /**
     * 工厂交货时间
     */
    private Date factoryFinishedDate;
    /**
     * 剩余数量
     */
    private Integer factoryRemainNum;
    /**
     * 工厂交货数量
     */
    private Integer factoryShippingPackageNum;

    /**
     * 目标sku
     */
    private String destinationSku;

    /**
     * 备注
     */
    private String remarks;
}
