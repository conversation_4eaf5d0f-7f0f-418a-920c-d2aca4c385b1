package com.purchase.purchase_server.utils.commonUtils;

import com.purchase.purchase_server.enums.lcl.ShipmentCodeCreationEnum;
import lombok.Data;

import java.util.Optional;
import java.util.Set;

import static com.purchase.purchase_server.enums.lcl.ShipmentCodeCreationEnum.*;

/**
 * @Description 货件号生成工具类
 * <AUTHOR>
 * @Date 2025/5/27 13:33
 **/
@Data
public class ShipmentCodeGenerator {
    private Integer furnitureCodeSuffix;
    private Integer lampCodeSuffix;
    private Integer mixCodeSuffix;
    private Set<String> existedShipmentCodeSet;
    private String yearSuffix;


    public ShipmentCodeGenerator(Integer furnitureCodeSuffix, Integer lampCodeSuffix, Integer mixCodeSuffix, Set<String> existedShipmentCodeSet) {
        this.furnitureCodeSuffix = Optional.ofNullable(furnitureCodeSuffix).orElse(0);
        this.lampCodeSuffix = Optional.ofNullable(lampCodeSuffix).orElse(0);
        this.mixCodeSuffix = Optional.ofNullable(mixCodeSuffix).orElse(0);
        this.existedShipmentCodeSet = existedShipmentCodeSet;
        this.yearSuffix = ShipmentCodeCreationEnum.getYearSuffix();
    }

    public String generateShipmentCode(String shipmentCategory) {
        String shipmentCode = "";
        if (shipmentCategory.equals(FURNITURE.getType())) {
            // 家具货件号
            shipmentCode = furnitureProcess();
        } else if (shipmentCategory.equals(LAMP.getType())) {
            // 灯具货件号
            shipmentCode = lampProcess();
        }else if (shipmentCategory.equals(MIX.getType())) {
            // 家具+灯具货件号
            shipmentCode = mixProcess();

        }
        return shipmentCode;
    }

    private String furnitureProcess() {
        String shipmentCodePrefix = FURNITURE.getPrefix() + yearSuffix;
        String shipmentCode;
        do {
            shipmentCode = shipmentCodePrefix + furnitureCodeSuffix;
            furnitureCodeSuffix++;
        } while (existedShipmentCodeSet.contains(shipmentCode));
        return shipmentCode;
    }

    private String lampProcess() {
        String shipmentCodePrefix = LAMP.getPrefix() + yearSuffix;
        String shipmentCode;
        do {
            shipmentCode = shipmentCodePrefix + lampCodeSuffix;
            lampCodeSuffix++;
        } while (existedShipmentCodeSet.contains(shipmentCode));
        return shipmentCode;
    }

    private String mixProcess() {
        String shipmentCodePrefix = MIX.getPrefix() + yearSuffix;
        String shipmentCode;
        do {
            shipmentCode = shipmentCodePrefix + mixCodeSuffix;
            mixCodeSuffix++;
        } while (existedShipmentCodeSet.contains(shipmentCode));
        return shipmentCode;
    }
}
