package com.purchase.purchase_server.mapper;

import com.github.yulichang.base.MPJBaseMapper;
import com.purchase.purchase_server.entity.dataObject.MockInventoryDO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Description 模拟数据mapper层
 * <AUTHOR>
 * @Date 2024/1/5 17:15
 **/
public interface MockInventoryMapper extends MPJBaseMapper<MockInventoryDO> {

    int deleteByShippingProjectIds(@Param("shippingProjectIds") List<String> shippingProjectIds);


}
