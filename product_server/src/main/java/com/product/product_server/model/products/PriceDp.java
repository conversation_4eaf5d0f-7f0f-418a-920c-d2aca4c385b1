package com.product.product_server.model.products;

import java.math.BigDecimal;
import java.math.RoundingMode;

public record PriceDp(Integer isTaxes, BigDecimal price, BigDecimal priceWithTax) {

    public PriceDp(Integer isTaxes, BigDecimal price, BigDecimal priceWithTax) {
        this.isTaxes = validIsTaxes(isTaxes);
        this.price = validPrice(price);
        this.priceWithTax = validPriceWithTax(priceWithTax, isTaxes);
    }

    private Integer validIsTaxes(Integer isTaxes) {
        if(isTaxes == null) {
            throw new IllegalArgumentException("是否含税不能为空");
        }
        return isTaxes;
    }

    private BigDecimal validPrice(BigDecimal price) {
        if(price == null) {
            throw new NumberFormatException("单价不能为空");
        }
        return price.setScale(2, RoundingMode.HALF_UP);
    }

    private BigDecimal validPriceWithTax(BigDecimal price, Integer isTaxes) {
        if(1 == isTaxes && price == null) {
            throw new NumberFormatException("当含税时，含税单价不能为空");
        } else if (0 == isTaxes && price != null){
            throw new NumberFormatException("当不含税时，含税单价必须为空");
        }
        return price;
    }
}
