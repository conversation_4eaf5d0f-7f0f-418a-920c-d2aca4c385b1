package com.crafts_mirror.utils.enums;


import lombok.Getter;

/**
 * 用户职位
 *
 * <AUTHOR>
 */

@Getter
public enum UserPostEnum {
    PRODUCT_MANAGER("1", "产品经理"),

    MERCHANDISER("2", "跟单人"),

    OPERATIONS_MANAGER("3", "运营"),

    PURCHASER("4", "采购员"),
    ;

    @Getter
    private final String code;

    private final String message;

    UserPostEnum(String code, String message) {
        this.code = code;
        this.message = message;
    }
}