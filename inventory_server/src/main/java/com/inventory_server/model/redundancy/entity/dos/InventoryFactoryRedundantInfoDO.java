package com.inventory_server.model.redundancy.entity.dos;

import com.baomidou.mybatisplus.annotation.TableName;
import com.inventory_server.infrastructures.entity.PhysicalBaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.time.LocalDate;


@EqualsAndHashCode(callSuper = true)
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@TableName("cm_inventory_factory_redundant_info")
public class InventoryFactoryRedundantInfoDO extends PhysicalBaseEntity {

  private Double redundantNum;
  private String watchBoardId;
  private String factoryPlanId;
  private String contractCode;
  private String virtualSku;
  private LocalDate factoryFinishedDate;
  private String remarks;
}
