package com.purchase.purchase_server.entity.excelObject.Lcl;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.purchase.purchase_server.entity.excelObject.BaseExcel;
import lombok.*;

import java.io.Serial;
import java.io.Serializable;

/**
 * @Description excel导出装柜拼柜数据 转换成的实体列
 * <AUTHOR>
 * @Date 2025/5/27 17:00
 **/
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LclContainerInfoExcel extends BaseExcel implements Serializable {

    @Serial
    private static final long serialVersionUID = -1L;
    @ExcelProperty("公司")
    private String contractCode;
    @ExcelProperty("虚拟sku")
    private String destinationSku;
    @ExcelProperty("产品图片")
    private String image;
    @ExcelProperty("产品名称")
    private String productName;
    @ExcelProperty("渠道来源")
    private String channel;
    @ExcelProperty("自定义SKU")
    private String selfSku;
    @ExcelProperty("产品标签")
    private String productTag;
    @ExcelProperty("FBA货件号（Shipment ID）")
    private String fbaShipmentCode;
    @ExcelProperty("每箱个数")
    private Integer containerLoad;
    @ExcelProperty("外箱尺寸长（cm）")
    private Double caseLength;
    @ExcelProperty("外箱尺寸宽（cm）")
    private Double caseWidth;
    @ExcelProperty("外箱尺寸高（cm）")
    private Double caseHeight;
    @ExcelProperty("省份")
    private String detailAddressCode;
    @ExcelProperty("单箱体积（m³）")
    private Double caseVolume;
    @ExcelProperty("单箱毛重（kg）")
    private Double singleCaseGrossWeight;
    @ExcelProperty("采购人员")
    private String purchaser;
    @ExcelProperty("跟单人员")
    private String orderTracker;
    @ExcelProperty("下单总数量")
    private Integer totalOrderQuantity;
    @ExcelProperty("对应下单总箱数")
    private Integer totalOrderCaseQuantity;
    @ExcelProperty("是否商检")
    private String commodityInspection;
    @ExcelProperty("产品经理")
    private String buyer;
    @ExcelProperty("运营")
    private String operator;
    @ExcelProperty("币种")
    private String currency;
    @ExcelProperty("采购金额")
    private Double  unitOrContractPrice;
    @ExcelProperty("工厂交期")
    private String  factoryFinishedDate;
    @ExcelProperty("件的类型")
    private String  pcsType;
    @ExcelProperty("备注")
    private String  remarks;
    @ExcelProperty("美西（洛杉矶）")
    private Integer  losAngeles;
    @ExcelProperty("美东南（萨凡纳）")
    private Integer  savannah;
    @ExcelProperty("美南（休斯顿）")
    private Integer  houston;
    @ExcelProperty("美东（新泽西）")
    private Integer  newJersey;
    @ExcelProperty("辅助列")
    private Integer  auxiliaryColumn;
    @ExcelProperty("未安排数量")
    private Integer  unassignedQuantity;
    @ExcelProperty("备注体积")
    private String  remarkVolume;
    @ExcelProperty("美西仓体积")
    private Double  westCoastWarehouseVolume;
    @ExcelProperty("美东南仓体积")
    private Double  southeastWarehouseVolume;
    @ExcelProperty("美南仓体积")
    private Double  southWarehouseVolume;
    @ExcelProperty("美东仓体积")
    private Double  eastWarehouseVolume;
    @ExcelProperty("辅助列体积")
    private Double  auxiliaryVolume;
    @ExcelProperty("未安排体积")
    private Double  unassignedVolume;
    @ExcelProperty("注意：货件超重显红")
    private String  isOverweight;

    @ExcelIgnore
    private String shipmentCode;
    @ExcelIgnore
    private String destinationWarehouse;
    /**
     * 运输方式
     */
    @ExcelIgnore
    private Integer transportationMode;

}
