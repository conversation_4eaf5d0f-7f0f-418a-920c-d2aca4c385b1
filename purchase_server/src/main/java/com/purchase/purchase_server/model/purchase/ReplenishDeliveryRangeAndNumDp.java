package com.purchase.purchase_server.model.purchase;

import cn.hutool.core.util.StrUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Optional;

import static com.crafts_mirror.utils.constant.DateFormatConstant.YYYY_MM_DD_DATE_FORMAT_SLASH;

/**
 * <AUTHOR>
 * @Date 2024/11/16 15:45
 **/
@Data
@Slf4j
public class ReplenishDeliveryRangeAndNumDp {

    private String range;

    private Double num;

    private static DateTimeFormatter formatter = DateTimeFormatter.ofPattern(YYYY_MM_DD_DATE_FORMAT_SLASH);


    public ReplenishDeliveryRangeAndNumDp(Double num) {
        this.num = Optional.ofNullable(num).orElse(0D);
    }

    public void addNum(double another) {
        num += another;
    }

    public void mergeAllDateRanges(String newRange) {
        if (StrUtil.isBlank(newRange)) {
            return;
        }

        // 解析 newRange，并调整结束日期 -1 天
        try {
            String[] split = newRange.split(";");
            for (String splitPart : split) {
                String[] splitParts = splitPart.split("~");
                LocalDate splitPartStart = LocalDate.parse(splitParts[0], formatter);
                // 记录时，最后一天不包括在补货周期内，后续展示只展示补货周期内的天数，因此，此处需要-1
                LocalDate newRangeEnd = LocalDate.parse(splitParts[1], formatter).minusDays(1);
                Range newRangeAdjusted = new Range(splitPartStart, newRangeEnd);
                if (StrUtil.isBlank(range)) {
                    range = newRangeAdjusted.convertToString();
                    continue;
                }
                // 将时间范围分割为列表
                List<Range> mergedRanges = getRangeList(newRangeAdjusted);

                // 构造合并后的字符串
                StringBuilder result = new StringBuilder();
                for (Range range : mergedRanges) {
                    if (!result.isEmpty()) {
                        result.append(";");
                    }
                    result.append(range.start.format(formatter)).append("~").append(range.end.format(formatter));
                }

                range = result.toString();
            }
        } catch (Exception e) {
            log.error("需要合并的日期为：{}", newRange);
            throw new RuntimeException(e);
        }
    }

    private List<Range> getRangeList(Range newRangeAdjusted) {
        List<Range> rangeList = new ArrayList<>();
        for (String rangeStr : range.split(";")) {
            String[] parts = rangeStr.split("~");
            rangeList.add(new Range(
                    LocalDate.parse(parts[0], formatter),
                    LocalDate.parse(parts[1], formatter)
            ));
        }

        rangeList.add(newRangeAdjusted);
        // 合并时间范围
        return getRanges(rangeList);
    }

    record Range(LocalDate start, LocalDate end){

        public String convertToString() {
            return start.format(formatter) + "~" + end.format(formatter);
        }
    }

    public static String addRange(String range, String anotherRange) {
        if (StrUtil.isBlank(range)) {
            return anotherRange;
        }

        if (StrUtil.isBlank(anotherRange)) {
            return range;
        }

        // 将输入字符串解析为 Range 对象列表
        List<Range> ranges = parseRanges(range, formatter);
        ranges.addAll(parseRanges(anotherRange, formatter));

        // 按起始日期排序并合并
        List<Range> mergedRanges = merge(ranges);

        // 转换回字符串形式
        return formatRanges(mergedRanges, formatter);
    }

    private static List<Range> parseRanges(String rangeStr, DateTimeFormatter formatter) {
        List<Range> ranges = new ArrayList<>();
        String[] parts = rangeStr.split(";");
        for (String part : parts) {
            String[] dates = part.split("~");
            LocalDate start = LocalDate.parse(dates[0], formatter);
            LocalDate end = LocalDate.parse(dates[1], formatter);
            ranges.add(new Range(start, end));
        }
        return ranges;
    }

    private static List<Range> merge(List<Range> ranges) {
        // 按起始日期排序
        ranges.sort(Comparator.comparing(r -> r.start));

        return getRanges(ranges);
    }

    private static List<Range> getRanges(List<Range> ranges) {
        List<Range> merged = new ArrayList<>();
        Range current = ranges.getFirst();

        for (int i = 1; i < ranges.size(); i++) {
            Range next = ranges.get(i);

            // 如果范围相差 1 天以内，合并
            if (!current.end.plusDays(1).isBefore(next.start)) {
                current = new Range(current.start, current.end.isAfter(next.end) ? current.end : next.end);
            } else {
                merged.add(current);
                current = next;
            }
        }

        merged.add(current);
        return merged;
    }

    private static String formatRanges(List<Range> ranges, DateTimeFormatter formatter) {
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < ranges.size(); i++) {
            Range range = ranges.get(i);
            sb.append(range.start.format(formatter))
                    .append("~")
                    .append(range.end.format(formatter));
            if (i < ranges.size() - 1) {
                sb.append(";");
            }
        }
        return sb.toString();
    }
}
