package com.purchase.purchase_server.entity.dto.Lcl;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.List;


@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class LclEditRemainNumDTO {

    private String id;

    /**
     * 可安排总量
     */
    private Integer totalRemainNum;

    /**
     * 到仓数据
     */
    private List<LclRemainNumDTO> lclRemainNumList;
}