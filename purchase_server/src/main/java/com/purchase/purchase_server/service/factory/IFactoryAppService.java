package com.purchase.purchase_server.service.factory;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.purchase.purchase_server.entity.dto.factory.*;
import com.purchase.purchase_server.entity.form.UserParams;
import com.purchase.purchase_server.entity.vo.UserSearchVo;

import java.io.InputStream;
import java.util.List;

public interface IFactoryAppService {
    void importFactoryApp(InputStream file, byte[] fileBytes, String fileName);

    FactoryDetailDataDto queryDetail(FactoryInfoQuery query);

    IPage<FactoryInfoIPageDto> pageList(FactoryDataPageQuery query);

    Boolean insertFactory(FactoryDataCommand factoryDataCommand);

    Boolean deleteInfo(String val);

    Boolean deleteFactoryContainer(String val);

    List<UserSearchVo> getUserSet(UserParams params);

    List<UserSearchVo> getBuyerSet();

    /**
     * 从SelfProductDO更新FactoryInfoDO的buyer字段
     * @return 更新是否成功
     */
    Boolean updateBuyerFromSelfProduct();

    //List<OrderTrackerSearchVo> getOrderTracker();
}
