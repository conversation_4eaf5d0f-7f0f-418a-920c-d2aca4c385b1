package com.purchase.purchase_server.service;

import cn.hutool.core.date.DateTime;
import com.purchase.purchase_server.entity.dto.FactoryFinishedInventoryDto;
import com.purchase.purchase_server.entity.dto.FactoryRemainInventoryDto;
import com.purchase.purchase_server.entity.dto.delivery.TrialCalReplenishmentDto;

import java.math.BigDecimal;
import java.util.*;

public interface ICalRemainInventoryService {
    /**
     * 计算模拟剩余库存
     *
     * @param shippingStartDate          运输开始时间
     * @param factoryRemainInventoryList 仓库剩余库存
     * @param maxCalDate                 最大计算日期
     * @param minShippingDate            最小运输日期
     * @param saleNumPerDayMap           日销
     * @param inventorySaleDefRatio      各仓库目标日销比例
     * @param everyDayWarehouseSaleMap   每天各仓库模拟销售记录
     * @return 剩余库存
     */
    Map<String, Map<String, Double>> calRemainInventoryMap(DateTime shippingStartDate, List<FactoryRemainInventoryDto> factoryRemainInventoryList,
                                                           int maxCalDate, int minShippingDate, Map<String, Double> saleNumPerDayMap,
                                                           Map<String, Double> inventorySaleDefRatio, Map<String, Map<String, Double>> everyDayWarehouseSaleMap,
                                                           List<String> sortedWarehouseList);

    TrialCalReplenishmentDto reCalShippingRemainInventory(Map<String, Double> inventorySaleDefRatio, int shippingFrequency, int safeDate,
                                                          List<String> sortedWarehouseList, List<FactoryRemainInventoryDto> factoryRemainInventoryList,
                                                          int containerLoad, Map<String, Integer> headShippingDateMap,
                                                          Map<String, Double> saleNumPerDayMap, DateTime projectStartDate,
                                                          List<FactoryFinishedInventoryDto> factoryFinishedInventoryList,
                                                          List<FactoryRemainInventoryDto> afterShippingEnableUsingInventoryList);

    Map<String, Map<String, Double>> getMockShippingMap(List<FactoryRemainInventoryDto> inventoryDtos,
                                                               Map<String, Integer> headShippingDateMap,
                                                               DateTime shippingStartDate, int calCircle,
                                                               List<FactoryRemainInventoryDto> remainInventoryListForCal);

    void implementShippingStartDateAndArrivingDate(List<FactoryRemainInventoryDto> inventoryDtos,
                                                   Map<String, Integer> headShippingDateMap,
                                                   Map<String, Map<String, Double>> mockShippingMap,
                                                   Map<String, Map<String, Double>> everyDayRemainInventoryMap);

    BigDecimal consumeZeroRatioWarehouseInventory(Map<String, Double> inventorySaleDefRatio,
                                                          Map<String, Double> warehouseInventoryMap,
                                                          Map<String, Double> warehouseDaySaleMap,
                                                          Map<String, Double> nextDayInventoryMap,
                                                          double everyDayTotalSalNum);

    int getRedundantDateNum();

    String getFirstSoldOut(Map<String, TreeMap<String, Double>> mockRemainInventoryMap,
                           String destinationWarehouse, DateTime expectedArrivingDate,
                           Map<String, TreeMap<String, Double>> destinationSale);
}
