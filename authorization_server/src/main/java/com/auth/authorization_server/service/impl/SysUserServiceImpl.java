package com.auth.authorization_server.service.impl;


import cn.hutool.core.codec.Base64;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.auth.authorization_server.assembler.UserAssembler;
import com.auth.authorization_server.domain.dto.RoleDTO;
import com.auth.authorization_server.domain.dto.UserDTO;
import com.auth.authorization_server.domain.vo.UserVO;
import com.auth.authorization_server.exception.BaseException;
import com.auth.authorization_server.model.dataObject.UserDO;
import com.auth.authorization_server.model.dataObject.UserDeptDO;
import com.auth.authorization_server.model.dataObject.UserRoleDO;
import com.auth.authorization_server.repository.UserDeptRepositoryImpl;
import com.auth.authorization_server.repository.UserRepositoryImpl;
import com.auth.authorization_server.repository.UserRoleRepositoryImpl;
import com.auth.authorization_server.req.UserParams;
import com.auth.authorization_server.service.ISysPermissionService;
import com.auth.authorization_server.service.ISysUserService;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.crafts_mirror.common.security.auth.AuthUtil;
import com.crafts_mirror.common.security.utils.SecurityUtils;
import com.crafts_mirror.utils.common.entity.LoginVo;
import com.crafts_mirror.utils.constant.RedisKeyConstant;
import com.crafts_mirror.utils.enums.ResponseCodeEnum;
import com.crafts_mirror.utils.utils.RsaUtils;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.UUID;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 角色 业务层处理
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class SysUserServiceImpl implements ISysUserService {

    @Resource
    private UserRepositoryImpl userRepository;

    @Resource
    private UserRoleRepositoryImpl userRoleRepository;

    @Resource
    private UserDeptRepositoryImpl userDeptRepository;

    @Resource
    private ISysPermissionService permissionService;

    @Resource
    private UserAssembler userAssembler;

    @Resource
    private RedisTemplate<String, LoginVo> redisTemplate;

    /**
     * 根据条件分页查询已分配用户角色列表
     *
     * @param user 用户信息
     * @return 用户信息集合信息
     */
    @Override
    public IPage<UserVO> selectAllocatedRoleList(UserParams user) {
        return userRepository.selectAllocatedRoleList(user);
    }

    /**
     * 根据角色ids查询已分配用户角色列表
     *
     * @param roleIds 角色id
     * @return 用户信息集合信息
     */
    @Override
    public List<UserDO> selectAllocatedListByRoleIds(Set<String> roleIds) {
        return userRepository.selectAllocatedListByRoleIds(roleIds);
    }

    /**
     * 根据条件分页查询未分配用户角色列表
     *
     * @param user 用户信息
     * @return 用户信息集合信息
     */
    @Override
    public List<UserVO> selectUnallocatedRoleList(UserParams user) {
        return userRepository.selectUnallocatedRoleList(user);
    }

    /**
     * 根据条件分页查询已分配用户部门列表
     *
     * @param user 用户信息
     * @return 用户信息集合信息
     */
    @Override
    public IPage<UserVO> selectAllocatedDeptList(UserParams user) {
        return userRepository.selectAllocatedDeptList(user);
    }

    @Override
    public List<UserVO> selectUnallocatedDeptList(UserParams user) {
        return userRepository.selectUnallocatedDeptList(user);
    }

    /**
     * 根据条件分页查询用户列表
     *
     * @param user 用户信息
     * @return 用户信息集合信息
     */
    @Override
    public IPage<UserVO> selectUserList(UserParams user) {
        IPage<UserDO> userDOIPage = userRepository.selectUserPageList(user);
        IPage<UserVO> userVoIPage = new Page<>(user.getCurrent(), user.getSize(), userDOIPage.getTotal());
        if (CollectionUtil.isEmpty(userDOIPage.getRecords())) {
            return userVoIPage;
        }
        List<UserVO> userVOS = userAssembler.userDoListToVo(userDOIPage.getRecords());
        List<UserVO> newUserList = new ArrayList<>();
        for (UserVO userVO : userVOS) {
            UserDTO userDTO = selectUserById(userVO.getId());
            userVO.setRoleNames(userDTO.getRoles().stream().map(RoleDTO::getRoleName).collect(Collectors.toList()));
            if (UserDO.isAdmin(userVO.getId())) {
                newUserList.addFirst(userVO);
            } else {
                newUserList.add(userVO);
            }

        }
        userVoIPage.setRecords(newUserList);
        return userVoIPage;
    }

    @Override
    public LoginVo info(String username) {
        UserDTO userDTO = selectUserByUserName(username);
        // 角色集合
        //Set<String> roles = permissionService.getRolePermission(sysUser);
        // 权限集合
        Set<String> permissions = permissionService.getMenuPermission(userDTO);
        LoginVo sysUserVo = new LoginVo();
        sysUserVo.setUserId(userDTO.getId());
        sysUserVo.setUserName(userDTO.getUserName());
        sysUserVo.setNickName(userDTO.getNickName());
        //sysUserVo.setRoles(roles);
        sysUserVo.setPermissions(permissions);
        return sysUserVo;
    }

    /**
     * 通过用户名查询用户
     *
     * @param userName 用户名
     * @return 用户对象信息
     */
    @Override
    public UserDTO selectUserByUserName(String userName) {
        return userRepository.selectUserByUserName(userName);
    }

    /**
     * 通过用户ID查询用户
     *
     * @param userId 用户ID
     * @return 用户对象信息
     */
    @Override
    public UserDTO selectUserById(String userId) {
        return userRepository.selectUserById(userId);
    }

    /**
     * 校验用户是否有数据权限
     *
     * @param userId 用户id
     */
    @Override
    public void checkUserDataScope(String userId) {
        if (!UserDO.isAdmin(SecurityUtils.getUserId())) {
            UserParams user = new UserParams();
            user.setId(userId);
            user.setCurrent(1);
            user.setSize(-1);
            IPage<UserVO> userVoIPage = selectUserList(user);
            if (CollectionUtil.isEmpty(userVoIPage.getRecords())) {
                throw new BaseException(ResponseCodeEnum.BAD_REQUEST, "没有权限访问用户数据！");
            }
        }
    }

    /**
     * 新增保存用户信息
     *
     * @param user 用户信息
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean insertUser(UserDTO user) throws Exception {
        UserDO userDO = userAssembler.userDtoToDo(user);
        userDO.setCreateBy(SecurityUtils.getUsername());
        byte[] decode = Base64.decode(user.getPassWord());
        String password = RsaUtils.getPassword(decode);
        String salt = UUID.randomUUID().toString();
        String passwordEncrypt = new BCryptPasswordEncoder().encode(password + salt);
        userDO.setPassWord(passwordEncrypt);
        userDO.setSalt(salt);
        // 新增用户信息
        userRepository.save(userDO);
        user.setId(userDO.getId());
        // 新增用户与角色管理
        insertUserRole(user);
        // 新增用户与部门管理
        insertUserDept(user);
        return true;
    }

    /**
     * 校验用户名称是否唯一
     *
     * @param user 用户信息
     * @return 结果
     */
    @Override
    public boolean checkUserNameUnique(UserDTO user) {
        String userId = StrUtil.isBlank(user.getId()) ? "-1" : user.getId();
        UserDO info = userRepository.checkUserNameUnique(user.getUserName());
        return ObjectUtil.isEmpty(info) || info.getId().equals(userId);
    }

    /**
     * 新增用户角色信息
     *
     * @param user 用户对象
     */
    public void insertUserRole(UserDTO user) {
        this.insertUserRole(user.getId(), user.getRoleIds());
    }

    /**
     * 新增用户部门信息
     *
     * @param user 用户对象
     */
    public void insertUserDept(UserDTO user) {
        this.insertUserDept(user.getId(), user.getDeptIds());
    }

    /**
     * 新增用户角色信息
     *
     * @param userId  用户ID
     * @param roleIds 角色组
     */
    public void insertUserRole(String userId, String[] roleIds) {
        if (roleIds != null && roleIds.length > 0) {
            // 新增用户与角色管理
            List<UserRoleDO> list = new ArrayList<>();
            for (String roleId : roleIds) {
                UserRoleDO ur = new UserRoleDO();
                ur.setUserId(userId);
                ur.setRoleId(roleId);
                list.add(ur);
            }
            userRoleRepository.saveBatch(list);
        }
    }

    /**
     * 新增用户部门信息
     *
     * @param userId  用户ID
     * @param deptIds 部门组
     */
    public void insertUserDept(String userId, String[] deptIds) {
        if (deptIds != null && deptIds.length > 0) {
            // 新增用户与角色管理
            List<UserDeptDO> list = new ArrayList<>();
            for (String deptId : deptIds) {
                UserDeptDO ur = new UserDeptDO();
                ur.setUserId(userId);
                ur.setDeptId(deptId);
                list.add(ur);
            }
            userDeptRepository.saveBatch(list);
        }
    }

    /**
     * 校验手机号码是否唯一
     *
     * @param user 用户信息
     * @return
     */
    @Override
    public boolean checkPhoneUnique(UserDTO user) {
        String userId = StrUtil.isBlank(user.getId()) ? "-1" : user.getId();
        UserDO info = userRepository.checkPhoneUnique(user.getPhoneNumber());
        return ObjectUtil.isEmpty(info) || info.getId().equals(userId);
    }

    /**
     * 校验用户是否允许操作
     *
     * @param user 用户信息
     */
    @Override
    public void checkUserAllowed(UserDTO user) {
        if (StrUtil.isNotBlank(user.getId()) && UserDO.isAdmin(user.getId())) {
            throw new BaseException(ResponseCodeEnum.BAD_REQUEST, "不允许操作超级管理员用户");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateUser(UserDTO user) {
        String userId = user.getId();
        // 删除用户与角色关联
        userRoleRepository.deleteUserRoleByUserId(userId);
        // 新增用户与角色管理
        insertUserRole(user);
        // 删除用户与部门关联
        userDeptRepository.deleteUserDeptByUserId(userId);
        // 新增用户与部门管理
        insertUserDept(user);
        UserDO build = UserDO.builder().id(user.getId())
                .phoneNumber(user.getPhoneNumber())
                .nickName(user.getNickName())
                .userStatus(user.getUserStatus())
                .postId(user.getPostId())
                .updateBy(SecurityUtils.getUsername())
                .build();
        userRepository.updateById(build);
        AuthUtil.delLoginUser(build.getId());
        return true;
    }


    /**
     * 重置用户密码
     *
     * @param user 用户信息
     * @return 结果
     */
    @Override
    public boolean resetPwd(UserDTO user) throws Exception {
        UserDO userDO = userAssembler.userDtoToDo(user);
        UserDO byId = userRepository.getById(userDO.getId());
        byte[] decode = Base64.decode(user.getPassWord());
        String password = RsaUtils.getPassword(decode);
        String salt = byId.getSalt();
        if (StrUtil.isBlank(salt)) {
            salt = UUID.randomUUID().toString();
            userDO.setSalt(salt);
        }
        userDO.setPassWord(new BCryptPasswordEncoder().encode(password + salt));
        userDO.setUpdateBy(SecurityUtils.getUsername());
        userRepository.updateById(userDO);
        AuthUtil.delLoginUser(userDO.getId());
        return true;
    }

    /**
     * 修改用户状态
     *
     * @param user 用户信息
     * @return 结果
     */
    @Override
    public boolean updateUserStatus(UserDTO user) {
        UserDO userDO = userAssembler.userDtoToDo(user);
        userDO.setUpdateBy(SecurityUtils.getUsername());
        userRepository.updateById(userDO);
        AuthUtil.delLoginUser(userDO.getId());
        return true;
    }

    /**
     * 用户授权角色
     *
     * @param userId  用户ID
     * @param roleIds 角色组
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void insertUserAuth(String userId, String[] roleIds) {
        userRoleRepository.deleteUserRoleByUserId(userId);
        insertUserRole(userId, roleIds);
    }

    /**
     * 修改用户基本信息
     *
     * @param user 用户信息
     * @return 结果
     */
    @Override
    public boolean updateUserProfile(UserDTO user) {
        UserDO userDO = userAssembler.userDtoToDo(user);
        return userRepository.updateById(userDO);
    }

    /**
     * 重置用户密码
     *
     * @param userName 用户名
     * @param password 密码
     * @return 结果
     */
    @Override
    public int resetUserPwd(String userName, String password) {
        return userRepository.resetUserPwd(userName, password);
    }


    /**
     * 更新用户缓存
     *
     * @param userIds 用户id
     */
    @Override
    public void updateCacheByUserIds(List<String> userIds) {
        if (CollectionUtil.isNotEmpty(userIds)) {
            userIds.forEach(i -> {
                // 更新缓存用户信息
                Set<String> keys = redisTemplate.keys(RedisKeyConstant.USER_TOKEN_KEY + i + ":*");
                if (CollectionUtil.isNotEmpty(keys)) {
                    List<LoginVo> loginVos = redisTemplate.opsForValue().multiGet(keys);
                    if (CollectionUtil.isNotEmpty(loginVos)) {
                        loginVos.forEach(loginVo -> {
                            LoginVo newLoginVo = info(loginVo.getUserName());
                            newLoginVo.setToken(loginVo.getToken());
                            redisTemplate.opsForValue().set(RedisKeyConstant.USER_TOKEN_KEY + i + ":" + loginVo.getToken(), newLoginVo, 6, TimeUnit.HOURS);
                        });
                    }
                }
            });
        }
    }

    @Override
    public List<UserVO> selectUserInfo(UserParams params) {
        List<UserDO> userDO = userRepository.selectUserList(params);
        return userAssembler.userDoListToVo(userDO);
    }
}
