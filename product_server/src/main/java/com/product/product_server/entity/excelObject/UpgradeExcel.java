package com.product.product_server.entity.excelObject;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.*;

import java.io.Serial;
import java.io.Serializable;

/**
 * @Description 导入升级款excel
 * <AUTHOR>
 * @Date 2024/8/8 11:17
 **/
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UpgradeExcel extends BaseExcel implements Serializable {

    @Serial
    private static final long serialVersionUID = -5292935127791304409L;

    @ExcelProperty("老款虚拟SKU")
    private String originalSku;

    @ExcelProperty("代替为升级款SKU")
    private String upgradeSku;
}
