package com.sales_server.interfaces.controller;

import com.crafts_mirror.utils.common.entity.OperatorSearchVo;
import com.crafts_mirror.utils.web.domain.ResultDTO;
import com.sales_server.model.system.service.ISysUserInteriorService;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @Date 2024/11/16 16:15
 **/
@RestController
@RequestMapping(value = "/user")
public class UserController {

    @Resource
    private ISysUserInteriorService sysUserInteriorService;
    @GetMapping ("/list/operator")
    @ResponseBody
    public ResultDTO<OperatorSearchVo> getOperator() {
        return ResultDTO.success(sysUserInteriorService.getOperator());
    }
}
