package com.inventory_server.model.redundancy.entity.excelObj;

import com.alibaba.excel.annotation.ExcelProperty;
import com.inventory_server.infrastructures.entity.BaseExcel;
import lombok.*;

import java.io.Serial;
import java.io.Serializable;

/**
 * @Description 导入excel 日销
 * <AUTHOR>
 * @Date 2024/5/8 11:39
 **/
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RedundantDestinationSalesExcel extends BaseExcel implements Serializable {

    @Serial
    private static final long serialVersionUID = -9033533515276909324L;

    @ExcelProperty("*虚拟SKU")
    private String virtualSku;

    @ExcelProperty("*7日销量")
    private String sevenDaySales;

    @ExcelProperty("*14日销量")
    private String fourteenDaySales;

    @ExcelProperty("*30日销量")
    private String thirtyDaySales;

    @ExcelProperty("*实际日均销量")
    private String actualDailySales;
}
