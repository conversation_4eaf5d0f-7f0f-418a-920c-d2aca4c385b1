package com.crafts_mirror.common.security.service;

import cn.hutool.core.util.StrUtil;
import com.crafts_mirror.utils.common.entity.LoginVo;
import com.crafts_mirror.utils.constant.RedisKeyConstant;
import jakarta.annotation.Resource;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import java.util.Set;

/**
 * token验证处理
 *
 * <AUTHOR>
 */
@Component
public class TokenService {

    @Resource
    private RedisTemplate<String, LoginVo> redisTemplate;

    /**
     * 获取用户身份信息
     *
     * @return 用户信息
     */
    public LoginVo getLoginUser(String userId, String token) {
        LoginVo user = null;
        try {
            if (StrUtil.isNotEmpty(token)) {
                user = redisTemplate.opsForValue().get(getTokenKey(userId) + token);
                return user;
            }
        } catch (Exception ignored) {
        }
        return user;
    }

    /**
     * 删除集合对象
     *
     * @param userId 用户Id
     * @return
     */
    public boolean deleteObject(String userId) {
        Set<String> keys = redisTemplate.keys(getTokenKey(userId) + "*");
        return redisTemplate.delete(keys) > 0;
    }

    private String getTokenKey(String userId) {
        return RedisKeyConstant.USER_TOKEN_KEY + userId + ":";
    }
}