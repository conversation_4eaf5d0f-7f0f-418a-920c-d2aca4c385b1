package com.product.product_server.utils.easyExcelUtil.listener;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.context.AnalysisContext;
import com.crafts_mirror.utils.enums.UserPostEnum;
import com.crafts_mirror.utils.provider.ApplicationContextProvider;
import com.crafts_mirror.utils.utils.StrUtils;
import com.product.product_server.entity.dataObject.SelfProductDO;
import com.product.product_server.entity.excelObject.VirtualProductInfoExcel;
import com.product.product_server.entity.vo.BuyerSearchVo;
import com.product.product_server.exception.FieldNotExistException;
import com.product.product_server.model.virtualProduct.VirtualProductSkuDp;
import com.product.product_server.model.virtualProduct.VirtualProductSkuStatusDp;
import com.product.product_server.repository.SelfProductRepositoryImpl;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;


/**
 * @Description 导入虚拟sku
 * <AUTHOR>
 * @Date 2023/12/21 11:33
 **/
@Slf4j
public class VirtualProductImportListener extends AbstractProductImportListener<VirtualProductInfoExcel> {

    private final SelfProductRepositoryImpl selfProductRepository = ApplicationContextProvider.getBean(SelfProductRepositoryImpl.class);

    private final Map<String, String> channelNameIdMap;

    public VirtualProductImportListener(String fileName, String importExcelId, Map<String, String> channelNameIdMap) {
        super(fileName, importExcelId);
        this.channelNameIdMap = channelNameIdMap;
    }

    @Override
    public void invoke(VirtualProductInfoExcel excel, AnalysisContext analysisContext) {
        log.warn("导入虚拟商品-------------导入excel进行中，成功解析excel");
        Integer approximateRowNumber = analysisContext.readSheetHolder().getApproximateTotalRowNumber();
        if (approximateRowNumber != null && approximateRowNumber > 50000) {
            throw new RuntimeException("导入数据超过50000行，请缩小导入数据量");
        }

        // 校验自定义sku是否存在
        SelfProductDO selfProduct = selfProductRepository.getOneBySelfSku(excel.getSku());
        if (ObjectUtil.isNull(selfProduct)) {
            throw new IllegalArgumentException(excel.getSku() + "该自定义sku不存在，请先导入自定义sku数据");
        }

        // 判断运营
        String operator = excel.getOperator();
        List<String> nickNameList = StrUtils.convertStringToList(operator);
        AtomicReference<String> operatorFlag = new AtomicReference<>();
        String operators = nickNameList.stream()
                .distinct()
                .map(nickName -> {
                    try {
                        List<BuyerSearchVo> userList = validNickNameExisted(nickName, UserPostEnum.OPERATIONS_MANAGER);
                        return CollectionUtil.isNotEmpty(userList) ? userList.getFirst().getUserName() : nickName;
                    } catch (Exception e) {
                        operatorFlag.set(e.getMessage());
                        return nickName;
                    }
                })
                .collect(Collectors.joining(","));
        // 校验待导入的虚拟sku相关信息
        validChannelExisted(excel.getChannel(), channelNameIdMap.keySet());
        String channelUpper = excel.getChannel().toUpperCase(Locale.ROOT);
        String channel = channelNameIdMap.get(channelUpper);
        VirtualProductSkuDp virtualProduct = new VirtualProductSkuDp(channel, excel.getSpu(), excel.getSpuProductName(),
                excel.getVirtualSku(), operators, new VirtualProductSkuStatusDp(true, excel.getSubType(),
                excel.getProductStatus(), excel.getProductType()));
        if (StrUtil.isNotBlank(operatorFlag.get())){
            throw new FieldNotExistException(operatorFlag.get());
        }
        if (StrUtil.isNotBlank(excel.getRemarks()) && excel.getRemarks().length() > 100) {
            throw new IllegalArgumentException("虚拟sku备注不能超过100字符");
        }

        checkVirtualSkuAndOldSkuConsistency(excel.getVirtualSku(), excel.getOldSku());

        productImportService.saveVirtualProductInfo(excel, virtualProduct, selfProduct.getId());
        successfulTimes++;
    }
}
