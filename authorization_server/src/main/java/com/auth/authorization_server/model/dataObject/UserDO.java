package com.auth.authorization_server.model.dataObject;

import com.auth.authorization_server.enums.UserStatusEnum;
import com.auth.authorization_server.model.PhysicalBaseEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.io.Serial;

/**
 * <AUTHOR>
 * @description: 单纯的和数据库表的映射关系，每个字段对应数据库表的一个column
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("cm_sys_user")
@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder
public class UserDO extends PhysicalBaseEntity {
    @Serial
    private static final long serialVersionUID = -1L;

    /**
     * 用户账号
     */
    private String userName;
    /**
     * 用户昵称
     */
    private String nickName;

    /**
     * 手机号码
     */
    private String phoneNumber;
    /**
     * 密码
     */
    private String passWord;
    /**
     * 盐
     */
    private String salt;
    /**
     * 帐号状态（0正常 1停用）
     *
     * @see UserStatusEnum
     */
    private String userStatus;

    /**
     * 职位
     */
    private String postId;


    public static boolean isAdmin(String userId) {
        return userId != null && userId.equals("1");
    }
}
