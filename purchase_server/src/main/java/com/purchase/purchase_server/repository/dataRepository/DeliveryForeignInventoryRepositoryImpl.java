package com.purchase.purchase_server.repository.dataRepository;

import cn.hutool.core.util.StrUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.purchase.purchase_server.entity.bo.DeliveryForeignInventoryBO;
import com.purchase.purchase_server.entity.dataObject.*;
import com.purchase.purchase_server.entity.dto.FactoryRemainInventoryDto;
import com.purchase.purchase_server.entity.form.InteriorInfoQuery;
import com.purchase.purchase_server.entity.form.LclConsolidationForm;
import com.purchase.purchase_server.mapper.DeliveryForeignInventoryMapper;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import static com.purchase.purchase_server.enums.DeliveryTypeEnum.NORMAL;
import static com.purchase.purchase_server.enums.DeliveryTypeEnum.UNDER_LOAD;

/**
 * <AUTHOR>
 * @Date 2024/10/28 15:00
 **/
@Service
public class DeliveryForeignInventoryRepositoryImpl extends ServiceImpl<DeliveryForeignInventoryMapper, DeliveryForeignInventoryDO> {

    public void batchSaveDeliveryForeignInventory(String shippingProjectId, List<FactoryRemainInventoryDto> remainInventoryList) {
        List<DeliveryForeignInventoryDO> deliveryForeignInventoryList = new ArrayList<>(remainInventoryList.size());
        for (var remains : remainInventoryList) {
            DeliveryForeignInventoryDO build = DeliveryForeignInventoryDO.builder()
                    .shipmentCode(remains.getShipmentCode())
                    .enableUsingDate(remains.getEnableUsingDate())
                    .virtualSku(remains.getVirtualSku())
                    .startShippingDate(remains.getStartShippingTime())
                    .warehouseId(remains.getWarehouse())
                    .shippingProjectId(shippingProjectId)
                    .storeNum(remains.getStoreNum().intValue())
                    .remarks(remains.getRemarks())
                    .deliveryType(StrUtil.isNotBlank(remains.getRemarks()) && remains.getRemarks().contains(UNDER_LOAD.getDesc()) ? UNDER_LOAD.getCode() : NORMAL.getCode())
                    .productSnapshotId(remains.getProductSnapshotId())
                    .isOldStatus(remains.getIsOldStatus())
                    .virtualSkuId(remains.getVirtualSkuId())
                    .build();
            deliveryForeignInventoryList.add(build);
        }
        this.baseMapper.insert(deliveryForeignInventoryList);
    }

    public List<DeliveryForeignInventoryDO> getByShippingProjectId(String shippingProjectId) {
        return baseMapper.selectList(Wrappers.<DeliveryForeignInventoryDO>lambdaQuery()
                .eq(DeliveryForeignInventoryDO::getShippingProjectId, shippingProjectId));
    }

    public List<DeliveryForeignInventoryBO> selectNeedToShipList(LclConsolidationForm form) {
        return this.baseMapper.selectJoinList(DeliveryForeignInventoryBO.class, new MPJLambdaWrapper<DeliveryForeignInventoryDO>()
                .selectAll(DeliveryForeignInventoryDO.class)
                .select("t1.self_data->>'$.containerLoad' as container_load")
                .select(PrepareProductsRulesDO::getHeadShippingDays)
                .select(PrepareProductsRulesDO::getShippingRatio)
                .leftJoin(ProductSnapshotDO.class, ProductSnapshotDO::getId, DeliveryForeignInventoryDO::getProductSnapshotId)
                .leftJoin(ShippingProjectDO.class, ShippingProjectDO::getId, DeliveryForeignInventoryDO::getShippingProjectId)
                .leftJoin(ShippingRecordDO.class, ShippingRecordDO::getId, ShippingProjectDO::getShippingRecordId)
                .leftJoin(PrepareProductsRulesDO.class, PrepareProductsRulesDO::getDeliveryProjectId, ShippingProjectDO::getId)
                .eq(ShippingRecordDO::getId, form.getShippingRecordId())
                .isNotNull(DeliveryForeignInventoryDO::getStartShippingDate)
                .ge(form.isLimit() && StrUtil.isNotBlank(form.getShippingStartDate()),
                        DeliveryForeignInventoryDO::getEnableUsingDate, form.getShippingStartDate())
                .orderByAsc(DeliveryForeignInventoryDO::getEnableUsingDate)
                .last(form.isLimit(), "limit 1")
        );
    }

    public Set<String> getSnapIds(InteriorInfoQuery infoQuery) {
        List<DeliveryForeignInventoryDO> list = baseMapper.selectList(Wrappers.<DeliveryForeignInventoryDO>lambdaQuery()
                .select(DeliveryForeignInventoryDO::getProductSnapshotId)
                .between(StrUtil.isNotBlank(infoQuery.getCreateStartDate()) && StrUtil.isNotBlank(infoQuery.getCreateEndDate()),
                        DeliveryForeignInventoryDO::getCreateDate, infoQuery.getCreateStartDate(), infoQuery.getCreateEndDate()));
        return CollectionUtil.isEmpty(list) ? Collections.emptySet() :
                list.stream()
                        .map(DeliveryForeignInventoryDO::getProductSnapshotId)
                        .collect(Collectors.toSet());
    }
}
