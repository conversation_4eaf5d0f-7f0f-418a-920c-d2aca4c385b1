package com.purchase.purchase_server.repository.dataRepository;

import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.purchase.purchase_server.entity.dataObject.PrepareProductsRulesDO;
import com.purchase.purchase_server.entity.dto.delivery.ShippingProjectBaseParamDto;
import com.purchase.purchase_server.mapper.PrepareProductsRulesMapper;
import org.springframework.stereotype.Service;

/**
 * @Description 发货规则关repository类
 * <AUTHOR>
 * @Date 2023/12/6 14:55
 **/
@Service
public class PrepareProductsRulesRepositoryImpl extends ServiceImpl<PrepareProductsRulesMapper, PrepareProductsRulesDO> {
    public PrepareProductsRulesDO getShippingProjectBaseParam(String shippingProjectId) {
        return baseMapper.selectJoinOne(PrepareProductsRulesDO.class, new MPJLambdaWrapper<PrepareProductsRulesDO>()
                .selectAll(PrepareProductsRulesDO.class)
                .eq(PrepareProductsRulesDO::getDeliveryProjectId, shippingProjectId)
        );
    }

    public PrepareProductsRulesDO getShippingProjectBaseParamByVirtualSku(String virtualSkuId) {
        return baseMapper.selectOne(Wrappers.<PrepareProductsRulesDO>lambdaQuery()
                .eq(PrepareProductsRulesDO::getVirtualSkuId, virtualSkuId)
                .orderByDesc(PrepareProductsRulesDO::getCreateDate), false);
    }

    public String insertPrepareProductRules(ShippingProjectBaseParamDto baseParamDto, String virtualSkuId,
                                            String deliveryProjectId, int redundantDays) {
        PrepareProductsRulesDO rulesDO = PrepareProductsRulesDO.builder()
                .virtualSkuId(virtualSkuId)
                .purchaseProjectDays(baseParamDto.getPurchaseProjectDays())
                .deliveryProjectId(deliveryProjectId)
                .changeableSafeDays(redundantDays)
                .produceDays(baseParamDto.getProduceDays())
                .transitDays(baseParamDto.getTransitDays())
                .safeDays(baseParamDto.getSafeDays())
                .replenishFrequency(baseParamDto.getShippingFrequency())
                .headShippingDays(JSON.toJSONString(baseParamDto.getHeadShippingDays()))
                .shippingRatio(JSON.toJSONString(baseParamDto.getShippingRatio()))
                .build();
        baseMapper.insert(rulesDO);
        return rulesDO.getId();
    }
}
