package com.purchase.purchase_server.service.shipping;

import cn.hutool.core.date.DateTime;
import com.purchase.purchase_server.entity.dataObject.SelfProductDO;
import com.purchase.purchase_server.entity.dataObject.VirtualProductDO;
import com.purchase.purchase_server.entity.dto.FactoryFinishedInventoryDto;
import com.purchase.purchase_server.entity.dto.FactoryRemainInventoryDto;
import com.purchase.purchase_server.entity.dto.SenboWarehouseDto;
import com.purchase.purchase_server.entity.dto.delivery.ShippingProjectBaseParamDto;
import com.purchase.purchase_server.entity.dto.delivery.TrialCalReplenishmentDto;
import com.purchase.purchase_server.utils.easyExcelUtil.manager.AbstractMapManager;

import java.util.List;
import java.util.Map;

public interface IShippingCalculationService {

    /**
     * 发货试算推演核心算法类
     *
     * @param shippingStartDate          新货最早发货时间
     * @param factoryRemainInventoryList 多次调用发货试算时，上个试算周期得到的发货结果
     * @return 每个工厂交期的发货信息
     */
    TrialCalReplenishmentDto trialShippingCalculation(
            DateTime shippingStartDate, int redundantDays, List<FactoryRemainInventoryDto> factoryRemainInventoryList,
            List<FactoryRemainInventoryDto> importFactoryRemainInventoryList, VirtualProductDO virtualProduct, int maxDeliveryDayGap,
            Map<String, Double> saleNumPerDayMap, DateTime projectStartDate, List<FactoryFinishedInventoryDto> factoryFinishedInventoryList,
            ShippingProjectBaseParamDto shippingRules, List<String> sortedWarehouseList, String productName, List<SenboWarehouseDto> warehouseList);

    Map<String, String> convertOldSkuInfoToVirtualSkuInfo(AbstractMapManager mapManager);
}
