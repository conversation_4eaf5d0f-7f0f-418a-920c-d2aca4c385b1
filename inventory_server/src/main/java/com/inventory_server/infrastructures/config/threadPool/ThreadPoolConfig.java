package com.inventory_server.infrastructures.config.threadPool;

import com.alibaba.ttl.threadpool.TtlExecutors;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.Executor;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * @Description 库存试算线程池
 * <AUTHOR>
 * @Date 2024/4/25 13:40
 **/
@Configuration
public class ThreadPoolConfig {

    @Bean(name = "inventoryCalThreadPool")
    public Executor inventoryCalThreadPool() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(3); // 设置核心线程数
        executor.setMaxPoolSize(5); // 设置最大线程数
        executor.setQueueCapacity(100); // 设置队列容量
        executor.setThreadNamePrefix("INVENTORY_CAL_THREAD_POOL"); // 设置线程名前缀
        executor.setAllowCoreThreadTimeOut(true); // 允许核心线程数回收
        executor.setKeepAliveSeconds(60 * 60); // 空闲线程的存活时间，这里设置为一小时
        executor.initialize(); // 初始化线程池
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        return TtlExecutors.getTtlExecutor(executor);
    }

    @Bean(name = "reCalUrgentPurchaseThreadPool1")
    public Executor reCalUrgentPurchaseThreadPool1() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(3); // 设置核心线程数
        executor.setMaxPoolSize(5); // 设置最大线程数
        executor.setQueueCapacity(10000); // 设置队列容量
        executor.setThreadNamePrefix("URGENT_PURCHASE_RE_CAL_THREAD_POOL1"); // 设置线程名前缀
        executor.setAllowCoreThreadTimeOut(true); // 允许核心线程数回收
        executor.setKeepAliveSeconds(10 * 60); // 空闲线程的存活时间，这里设置为10分钟
        executor.initialize(); // 初始化线程池
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        return TtlExecutors.getTtlExecutor(executor);
    }

    @Bean(name = "reCalUrgentPurchaseThreadPool2")
    public Executor reCalUrgentPurchaseThreadPool2() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(1); // 设置核心线程数
        executor.setMaxPoolSize(2); // 设置最大线程数
        executor.setQueueCapacity(5000); // 设置队列容量
        executor.setThreadNamePrefix("URGENT_PURCHASE_RE_CAL_THREAD_POOL2"); // 设置线程名前缀
        executor.setAllowCoreThreadTimeOut(true); // 允许核心线程数回收
        executor.setKeepAliveSeconds(10 * 60); // 空闲线程的存活时间，这里设置为10分钟
        executor.initialize(); // 初始化线程池
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        return TtlExecutors.getTtlExecutor(executor);
    }

    @Bean(name = "asyncRealTimeInventoryThreadPool")
    public Executor asyncRealTimeInventoryThreadPool() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(2); // 设置核心线程数
        executor.setMaxPoolSize(2); // 设置最大线程数
        executor.setQueueCapacity(10); // 设置队列容量
        executor.setThreadNamePrefix("REAL_TIME_INVENTORY_THREAD_POOL"); // 设置线程名前缀
        executor.setAllowCoreThreadTimeOut(true); // 允许核心线程数回收
        executor.setKeepAliveSeconds(60 * 60); // 空闲线程的存活时间，这里设置为一小时
        executor.initialize(); // 初始化线程池
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        return TtlExecutors.getTtlExecutor(executor);
    }

    @Bean(name = "importExcelThreadPool")
    public ThreadPoolTaskExecutor importExcelThreadPool() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(2); // 设置核心线程数
        executor.setMaxPoolSize(4); // 设置最大线程数
        executor.setQueueCapacity(10); // 设置队列容量
        executor.setThreadNamePrefix("IMPORT_EXCEL_THREAD_POOL"); // 设置线程名前缀
        executor.setAllowCoreThreadTimeOut(true); // 允许核心线程数回收
        executor.setKeepAliveSeconds(60 * 60); // 空闲线程的存活时间，这里设置为一小时
        executor.initialize(); // 初始化线程池
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        return executor;
    }

    /**
     * 一些无需获取返回数据的微服务调用可使用该线程池，避免网络阻塞
     * @return ThreadPoolTaskExecutor
     */
    @Bean(name = "fileUploadThreadPool")
    public Executor fileUploadThreadPool() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(1); // 设置核心线程数
        executor.setMaxPoolSize(2); // 设置最大线程数
        executor.setQueueCapacity(10); // 设置队列容量
        executor.setThreadNamePrefix("FILE_UPLOAD_THREAD_POOL"); // 设置线程名前缀
        executor.setAllowCoreThreadTimeOut(true); // 允许核心线程数回收
        executor.setKeepAliveSeconds(60); // 空闲线程的存活时间，这里设置为一分钟
        executor.initialize(); // 初始化线程池
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        return TtlExecutors.getTtlExecutor(executor);
    }

    @Bean(name = "visitLogThreadPool")
    public Executor visitLogThreadPool() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(3); // 设置核心线程数
        executor.setMaxPoolSize(3); // 设置最大线程数
        executor.setQueueCapacity(100); // 设置队列容量
        executor.setThreadNamePrefix("VISIT_LOG_THREAD_POOL"); // 设置线程名前缀
        executor.setAllowCoreThreadTimeOut(true); // 允许核心线程数回收
        executor.setKeepAliveSeconds(60 * 30); // 空闲线程的存活时间，这里设置为半小时
        executor.initialize(); // 初始化线程池
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        return TtlExecutors.getTtlExecutor(executor);
    }
}