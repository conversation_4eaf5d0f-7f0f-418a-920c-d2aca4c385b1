package com.product.product_server.exception;

import com.crafts_mirror.utils.enums.ResponseCodeEnum;
import lombok.Getter;
import org.slf4j.helpers.FormattingTuple;
import org.slf4j.helpers.MessageFormatter;

/**
 * 自定义业务异常
 *
 * @author: maodou
 * @date: 2024-05-10 18:28
 */
@Getter
public class BusinessException extends RuntimeException {

    private final Integer code;

    private final String msg;

    /**
     * 自定义回复信息
     *
     * @param code    返回状态
     * @param message 返回详细信息
     * @param params  消息占位替换元素
     */
    public BusinessException(Integer code, String message, Throwable e, Object... params) {
        super(message, e);
        this.code = code;
        this.msg = getMessage(message, params);
    }

    /**
     * 标准回复
     *
     * @param status 返回状态
     */
    public BusinessException(ResponseCodeEnum status, Object... params) {
        super(status.getMessage());
        this.code = status.getCode();
        this.msg = getMessage(status.getMessage(), params);
    }

    /**
     * 自定义回复信息
     *
     * @param status  返回状态
     * @param message 返回详细信息
     * @param message 返回详细信息
     */
    public BusinessException(ResponseCodeEnum status, String message, Object... params) {
        super(message);
        this.code = status.getCode();
        this.msg = getMessage(message, params);
    }

    /**
     * 自定义回复信息
     *
     * @param code    返回状态
     * @param message 返回详细信息
     * @param params  消息占位替换元素
     */
    public BusinessException(Integer code, String message, Object... params) {
        super(message);
        this.code = code;
        this.msg = getMessage(message, params);
    }

    /**
     * 利用slf4j的格式组装新的信息
     *
     * @param msg  信息模版
     * @param args 模版参数
     * @return
     */
    private String getMessage(String msg, Object... args) {
        FormattingTuple formattingTuple = MessageFormatter.arrayFormat(msg, args);
        return formattingTuple.getMessage();
    }
}
