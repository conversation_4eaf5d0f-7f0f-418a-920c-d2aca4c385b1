package com.purchase.purchase_server.controller.Interior;

import com.crafts_mirror.utils.web.domain.ResultDTO;
import com.purchase.purchase_server.entity.form.InteriorInfoQuery;
import com.purchase.purchase_server.service.lcl.InteriorLclConsolidationService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Set;

/**
 * @Description 装柜内部微服务调用接口
 * <AUTHOR>
 * @Date 2024/5/9 16:09
 **/
@RestController
@RequestMapping(value = "interior/lclConsolidation")
@Slf4j
public class LclInteriorController {

    @Resource
    private InteriorLclConsolidationService lclConsolidationService;


    @PostMapping("/selectSnapIds")
    public ResultDTO<Set<String>> getSnapIds(@RequestBody InteriorInfoQuery form) {
        Set<String> snapIds;
        try {
            snapIds = lclConsolidationService.getSnapIds(form);
        } catch (Exception e) {
            log.error(e.getMessage());
            return ResultDTO.error(e.getMessage());
        }
        return ResultDTO.success(snapIds);
    }
}
