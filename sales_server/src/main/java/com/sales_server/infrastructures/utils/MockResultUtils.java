package com.sales_server.infrastructures.utils;

import cn.hutool.core.collection.CollectionUtil;

import java.util.Map;
import java.util.Set;

/**
 * @Description 模拟表格结果处理工具类
 * <AUTHOR>
 * @Date 2024/7/12 10:50
 **/
public class MockResultUtils {
    public static <T extends Number> void checkEmptyWarehouse(Set<String> zeroRatioSet, Map<String, Map<String, T>> map) {
        for (Map.Entry<String, Map<String, T>> entry : map.entrySet()) {
            Map<String, T> warehouseShippingMap = entry.getValue();

            warehouseShippingMap.entrySet().stream()
                    .filter(e -> zeroRatioSet.contains(e.getKey()) && e.getValue().doubleValue() > 0)
                    .forEach(warehouseEntry -> zeroRatioSet.remove(warehouseEntry.getKey()));
        }
    }

    public static void removeUselessMockResult(Map<String, Map<String, Double>> remainInventoryMap,
                                               Map<String, Map<String, Double>> onShippingInventory,
                                               Map<String, Map<String, Double>> everydaySaleMap, Set<String> zeroRatioSet) {
        if (CollectionUtil.isEmpty(zeroRatioSet)) {
            return;
        }
        for (Map<String, Double> dayMap: remainInventoryMap.values()) {
            zeroRatioSet.forEach(dayMap::remove);
        }
        for (Map<String, Double> dayMap: onShippingInventory.values()) {
            zeroRatioSet.forEach(dayMap::remove);
        }
        for (Map<String, Double> dayMap: everydaySaleMap.values()) {
            zeroRatioSet.forEach(dayMap::remove);
        }
    }
}
