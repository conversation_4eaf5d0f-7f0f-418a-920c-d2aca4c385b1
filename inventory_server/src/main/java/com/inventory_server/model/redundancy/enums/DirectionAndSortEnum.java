package com.inventory_server.model.redundancy.enums;

import com.baomidou.mybatisplus.core.toolkit.support.SFunction;
import com.inventory_server.model.redundancy.entity.dos.RedundantInventoryDO;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Getter
@AllArgsConstructor
public enum DirectionAndSortEnum {

    DAYS_BEFORE_SOLD_OUT("soldOut", "售罄前断货天数", "days_before_sold_out"),
    FOREIGN_REDUNDANT_INVENTORY("foreign", "海外仓冗余库存", "foreign_redundant_inventory"),
    SHIPPING_REDUNDANT_INVENTORY("onShip", "在途仓冗余库存", "on_shipping_redundant_inventory"),
    FACTORY_REDUNDANT_INVENTORY("factory", "工厂冗余库存", "factory_redundant_inventory"),
    SOLD_OUT_DATE("soldOutDate", "海外仓理论售罄时间", "foreign_theoretical_sold_out_date"),
    FULL_LINK_SOLD_OUT_DATE("fullLinkSoldOutDate", "全链路海外仓理论售罄时间", "full_link_theoretical_sold_out_date"),
    FULL_LINK_DAYS_BEFORE_SOLD_OUT("fullLinkSoldOut", "全链售罄前断货天数", "full_link_days_before_sold_out"),
    FULL_LINK_SALABLE_DAYS("fullLinkSalableDays", "全链路海外仓可售天数", "full_link_salable_days"),
    SALABLE_DAYS("salableDays", "海外仓可售天数", "salable_days"),

    ASC("asc", "升序", null),
    DESC("desc", "降序", null);

    private final String code;
    private final String desc;
    private final String tableField;

    public static DirectionAndSortEnum ofCode(String code) {
        return Arrays.stream(DirectionAndSortEnum.values())
                .filter(it -> it.getCode().equals(code))
                .findFirst()
                .orElse(null);
    }

    public static List<String> getCodeList() {
        return Arrays.stream(DirectionAndSortEnum.values())
                .map(DirectionAndSortEnum::getCode).collect(Collectors.toList());
    }

    public static final Map<String, SFunction<RedundantInventoryDO, ?>> sortMap;

    static {
        sortMap = Map.of(
                DAYS_BEFORE_SOLD_OUT.getCode(), RedundantInventoryDO::getDaysBeforeSoldOut,
                FOREIGN_REDUNDANT_INVENTORY.getCode(), RedundantInventoryDO::getForeignRedundantInventory,
                SHIPPING_REDUNDANT_INVENTORY.getCode(), RedundantInventoryDO::getOnShippingRedundantInventory,
                FACTORY_REDUNDANT_INVENTORY.getCode(), RedundantInventoryDO::getFactoryRedundantInventory,
                SOLD_OUT_DATE.getCode(), RedundantInventoryDO::getForeignTheoreticalSoldOutDate,
                FULL_LINK_DAYS_BEFORE_SOLD_OUT.getCode(), RedundantInventoryDO::getFullLinkDaysBeforeSoldOut,
                FULL_LINK_SOLD_OUT_DATE.getCode(), RedundantInventoryDO::getFullLinkTheoreticalSoldOutDate,
                FULL_LINK_SALABLE_DAYS.getCode(), RedundantInventoryDO::getFullLinkSalableDays,
                SALABLE_DAYS.getCode(), RedundantInventoryDO::getSalableDays
        );
    }
}
