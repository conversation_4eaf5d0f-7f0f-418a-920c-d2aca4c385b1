package com.auth.authorization_server.model.dataObject;

import com.auth.authorization_server.enums.MenuFrameEnum;
import com.auth.authorization_server.enums.MenuStatusEnum;
import com.auth.authorization_server.enums.MenuTypeEnum;
import com.auth.authorization_server.model.PhysicalBaseEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;
import lombok.experimental.SuperBuilder;

import java.io.Serial;

/**
 * 菜单权限表 sys_menu
 *
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder
@TableName("cm_sys_menu")
public class MenuDO extends PhysicalBaseEntity {

    @Serial
    private static final long serialVersionUID = -4066174800775590430L;
    /**
     * 菜单名称
     */
    private String menuName;

    /**
     * 父菜单ID
     */
    private String parentId;

    /**
     * 显示顺序
     */
    private Integer orderNum;

    /**
     * 路由地址
     */
    private String path;

    /**
     * 路由名称
     */
    private String pathName;

    /**
     * 组件路径
     */
    private String component;

    /**
     * 是否为外链（0是 1否）
     * @see MenuFrameEnum
     */
    private String isFrame;

    /**
     * 类型（0目录 1菜单 2按钮）
     * @see MenuTypeEnum
     */
    private String menuType;

    /**
     * 菜单状态（0正常 1停用）
     * @see MenuStatusEnum
     */
    private String menuStatus;

    /**
     * 权限字符串
     */
    private String perms;

    /**
     * 菜单图标
     */
    private String icon;

    private String url;

}
