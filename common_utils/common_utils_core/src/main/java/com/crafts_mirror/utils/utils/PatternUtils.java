package com.crafts_mirror.utils.utils;

import java.util.regex.Pattern;

/**
 * <AUTHOR>
 */
public class PatternUtils {

    public static final Pattern UPPERCASE_LOWERCASE_NUMBER_DOT_PATTERN = Pattern.compile("^(?=.*[a-zA-Z0-9])[a-zA-Z0-9!@#$%^&*(),._\\-+]+$");

    public static final Pattern UPPERCASE_LOWERCASE_PATTERN = Pattern.compile("[a-zA-z]+");

    public static final Pattern PASSWORD_PATTERN = Pattern.compile("^(?=.*[a-zA-Z])(?=.*[^\\da-zA-Z\\s])([a-zA-Z]|[^\\da-zA-Z\\s]){6,20}$");

    public static final Pattern NUMBER_OR_WORD = Pattern.compile("^[a-zA-Z0-9]+$");

}