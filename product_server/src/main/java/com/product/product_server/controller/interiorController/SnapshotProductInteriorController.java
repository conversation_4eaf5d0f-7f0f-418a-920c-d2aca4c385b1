package com.product.product_server.controller.interiorController;

import com.product.product_server.entity.form.ProductSnapshotForm;
import com.product.product_server.entity.response.ResultDTO;
import com.product.product_server.entity.vo.ProductSnapshotVo;
import com.product.product_server.service.IProductSnapshotService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @Description 快照内部调用接口
 * <AUTHOR>
 * @Date 2024/7/29 11:25
 **/
@RestController
@RequestMapping(value = "interior/snapshot")
@Slf4j
public class SnapshotProductInteriorController {

    @Resource
    private IProductSnapshotService productSnapshotService;

    @PostMapping("/getList")
    public ResultDTO<ProductSnapshotVo> getProductSnapshotListBySnapshotIdList(@RequestBody ProductSnapshotForm form) {
        return ResultDTO.success(productSnapshotService.getProductSnapshotList(form.getSnapshotList()));
    }

    @PostMapping("/insert/saveProductSnapshotList")
    public ResultDTO saveProductSnapshotList(@RequestBody ProductSnapshotForm form) {
        try {
            productSnapshotService.saveProductSnapshotListBySelfList(form.getSelfSkuIds());
            return ResultDTO.success();
        } catch (Exception e) {
            return ResultDTO.error(e.getMessage());
        }

    }

    @PostMapping("/insert/saveProductSnapshotByFactory")
    public ResultDTO saveProductSnapshotByFactory(@RequestBody ProductSnapshotForm form) {
        try {
            productSnapshotService.saveProductSnapshotByFactory(form.getFactoryInfoIds());
            return ResultDTO.success();
        } catch (Exception e) {
            return ResultDTO.error(e.getMessage());
        }
    }
}
