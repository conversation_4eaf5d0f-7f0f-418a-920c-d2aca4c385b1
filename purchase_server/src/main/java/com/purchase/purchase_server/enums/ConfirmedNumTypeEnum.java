package com.purchase.purchase_server.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

@Getter
@AllArgsConstructor
public enum ConfirmedNumTypeEnum {
    ADVICE_PURCHASE("0", "建议补货量"),
    ACTUAL_REPLENISHMENT("1", "理论补货量"),
    NO_REPLENISHMENT("2", "不补货");

    private final String code;
    private final String desc;

    public static ConfirmedNumTypeEnum ofCode(String code) {
        if (code == null) {
            return null;
        }
        return Arrays.stream(ConfirmedNumTypeEnum.values())
                .filter(it -> it.getCode().equals(code))
                .findFirst()
                .orElse(null);
    }
}
