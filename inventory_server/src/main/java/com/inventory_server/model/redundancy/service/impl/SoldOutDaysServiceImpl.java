package com.inventory_server.model.redundancy.service.impl;

import com.inventory_server.model.redundancy.entity.dos.InventorySoldOutDaysDO;
import com.inventory_server.model.redundancy.entity.dto.SoldOutDateBeforeTheoryDto;
import com.inventory_server.model.redundancy.repository.dataRepository.SoldOutDaysRepository;
import com.inventory_server.model.redundancy.service.ISoldOutDaysService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static com.inventory_server.infrastructures.consts.InventoryServiceConstant.FULL_LINK_SOLD_OUT_DAYS;
import static com.inventory_server.infrastructures.consts.InventoryServiceConstant.SOLD_OUT_DAYS;

/**
 * @Description 售罄时间服务层
 * <AUTHOR>
 * @Date 2024/5/30 14:34
 **/
@Service
@Slf4j
public class SoldOutDaysServiceImpl implements ISoldOutDaysService {

    @Resource
    private SoldOutDaysRepository soldOutDaysRepository;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void insertSoldOutDays(List<SoldOutDateBeforeTheoryDto> soldOutDetailList,
                                  List<SoldOutDateBeforeTheoryDto> fullLinkSoldOutDateDtoList, String inventoryInfoId) {
        deleteByInventoryInfoId(inventoryInfoId);

        List<InventorySoldOutDaysDO> list = new ArrayList<>();
        // 售罄时间
        for (var dto : soldOutDetailList) {
            list.add(InventorySoldOutDaysDO.builder()
                    .daysBegin(dto.getDaysBegin())
                    .daysEnd(dto.getDaysEnd())
                    .lackNum(dto.getLackNum())
                    .soldOutDays(dto.getSoldOutDays())
                    .daysGap(dto.getDaysGap())
                    .inventoryInfoId(inventoryInfoId)
                    .type(SOLD_OUT_DAYS)
                    .build());
        }

        // 全链路售罄时间
        for (var dto : fullLinkSoldOutDateDtoList) {
            list.add(InventorySoldOutDaysDO.builder()
                    .daysBegin(dto.getDaysBegin())
                    .daysEnd(dto.getDaysEnd())
                    .lackNum(dto.getLackNum())
                    .soldOutDays(dto.getSoldOutDays())
                    .daysGap(dto.getDaysGap())
                    .inventoryInfoId(inventoryInfoId)
                    .type(FULL_LINK_SOLD_OUT_DAYS)
                    .build());
        }

        soldOutDaysRepository.saveBatch(list);
    }

    @Override
    public void batchSaveSoldOutDays(List<InventorySoldOutDaysDO> list) {
        soldOutDaysRepository.saveBatch(list);
    }

    @Override
    public List<InventorySoldOutDaysDO> getBeforeSoldOutList(List<String> inventoryInfoIds, String type) {
        List<InventorySoldOutDaysDO> beforeSoldOutList = soldOutDaysRepository.getBeforeSoldOutList(inventoryInfoIds, type);
        for (var soldOut : beforeSoldOutList) {
            soldOut.setLackNum(soldOut.getLackNum() == null ? null : Math.ceil(soldOut.getLackNum()));
        }
        return beforeSoldOutList;
    }

    @Override
    public void deleteByInventoryInfoId(String inventoryInfoId) {
        soldOutDaysRepository.deleteByInventoryInfoId(inventoryInfoId);
    }

    @Override
    public double sumLackNumByInventoryId(String inventoryInfoId, String type) {
        return soldOutDaysRepository.sumLackNumByInventoryId(inventoryInfoId, type);
    }

    @Override
    public Map<String, Double> sumLackNumByInventoryIdList(List<String> inventoryInfoIdList, String type) {
        return soldOutDaysRepository.sumLackNumByInventoryIdList(inventoryInfoIdList, type);
    }
}
