package com.inventory_server.model.redundancy.entity.dos;

import com.baomidou.mybatisplus.annotation.TableName;
import com.inventory_server.infrastructures.entity.PhysicalBaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * @Description 库存冗余列表页
 * <AUTHOR>
 * @Date 2024/5/7 17:52
 **/
@EqualsAndHashCode(callSuper = true)
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@TableName("cm_inventory_sale_rules")
public class InventorySaleRulesDO extends PhysicalBaseEntity {

    private String inventoryInfoId;

    private String headShippingDate;

    private Integer safeDays;

    private Integer urgentShipDate;

    private Integer urgentShipDateEdited;

    private Integer changeableSafeDays;

    private String saleRatio;

    private String saleDestination;

    private Integer purchaseDays;

    private Integer purchaseCircle;

    private Integer shippingCircle;

    private String produceDays;

    private Integer normalProduceDays;

    private Integer urgentProduceDays;

    private Integer urgentProduceDaysEdited;

    private Integer transitDays;

    /**
     * 7日销量
     */
    private Double sevenDaySales;
    /**
     * 14日销量
     */
    private Double fourteenDaySales;
    /**
     * 30日销量
     */
    private Double thirtyDaySales;
    /**
     * 实际日均销量
     */
    private Double actualDailySales;
    /**
     * 目标日销
     */
    private Double targetSalesNum;
    /**
     * 子体达成率
     */
    private Double subEntityRate;
    /**
     * 父体达成率
     */
    private Double parentEntityRate;
}