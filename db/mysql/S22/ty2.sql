ALTER TABLE `crafts_mirror`.`cm_warehouse_senbo_info`
    MODIFY COLUMN `create_date` datetime NULL DEFAULT NULL COMMENT '创建时间' AFTER `remarks`,
    MODIFY COLUMN `update_date` datetime NULL DEFAULT NULL AFTER `create_date`;

ALTER TABLE `crafts_mirror`.`cm_warehouse_relationship`
    MODIFY COLUMN `create_date` datetime NULL DEFAULT NULL COMMENT '创建时间' AFTER `senbo_warehouse_id`,
    MODIFY COLUMN `update_date` datetime NULL DEFAULT NULL AFTER `create_date`;

RENAME TABLE `crafts_mirror`.`cm_factory_finished_inventory` TO `crafts_mirror`.`cm_delivery_factory_inventory`;

ALTER TABLE `crafts_mirror`.`cm_delivery_foreign_inventory`
    ADD COLUMN `delivery_type` int          NULL COMMENT '发货类型' AFTER `store_num`,
    ADD COLUMN `remarks`       text NULL COMMENT '备注' AFTER `delivery_type`;

ALTER TABLE `crafts_mirror`.`cm_delivery_factory_inventory`
    ADD COLUMN `delivery_type` int NULL COMMENT '发货类型' AFTER `source_type`;

CREATE TABLE `cm_warehouse_senbo_info_snapshot`
(
    `id`                   varchar(50)                                              NOT NULL,
    `create_date`          datetime                                                          DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_date`          datetime                                                          DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
    `create_by`            varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci      DEFAULT NULL,
    `update_by`            varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci      DEFAULT NULL,
    `status`               char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '0',
    `warehouse_senbo_info` json                                                     NOT NULL COMMENT '仓库信息',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci;

ALTER TABLE `crafts_mirror`.`cm_lcl_container_info`
    ADD INDEX `shipment_code_index`(`shipment_code`) USING BTREE;

ALTER TABLE `crafts_mirror`.`cm_shipment_plan`
    ADD INDEX `shipment_code_index`(`shipment_code`) USING BTREE;

ALTER TABLE `crafts_mirror`.`cm_product_snapshot`
    ADD INDEX `self_sku_index`(`self_sku`) USING BTREE,
    ADD INDEX `virtual_sku_index`(`virtual_sku`) USING BTREE;