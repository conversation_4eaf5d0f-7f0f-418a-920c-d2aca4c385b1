package com.inventory_server.model.redundancy.entity.dp;


import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.inventory_server.applications.dto.FactoryRemainInventoryDto;
import com.inventory_server.model.redundancy.entity.excelObj.InventoryStockQuantityInfoExcel;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

import static com.crafts_mirror.utils.constant.DateFormatConstant.YYYY_M_D_DATE_FORMAT_SLASH;

public record StockQuantityDp(String destinationSku, List<FactoryRemainInventoryDto> list) {

    public StockQuantityDp(String destinationSku, List<FactoryRemainInventoryDto> list) {
        this.destinationSku = destinationSku;
        this.list = Optional.ofNullable(list).orElse(new ArrayList<>());
    }

    public void addStockQuantity(InventoryStockQuantityInfoExcel infoExcel) {
        if (!(Optional.ofNullable(infoExcel.getStoreHouse()).isPresent() &&
                Optional.ofNullable(infoExcel.getStockQuantity()).isPresent() &&
                Optional.ofNullable(infoExcel.getVirtualSku()).isPresent() &&
                Optional.ofNullable(infoExcel.getInventoryTime()).isPresent()) &&
                StrUtil.isNotBlank(infoExcel.getStartShippingTime())
        ) {
            throw new IllegalArgumentException("请填写内容");
        }

        double quantity = convertToDouble(infoExcel.getStockQuantity());
        // 如果数量为0的话，该行数据过滤掉，在试算以及后续结果展示中都不存在
        if (quantity == 0) {
            return;
        }
        list.stream()
                .filter(item -> item.getWarehouse().equals(infoExcel.getStoreHouse())
                        && item.getEnableUsingDate().equals(convertToDate(infoExcel.getInventoryTime()))
                        && item.getShipmentCode().equals(infoExcel.getShipmentCode())
                        && item.getVirtualSku().equals(infoExcel.getVirtualSku())
                        && item.getStartShippingTime().equals(convertToStartShippingDate(infoExcel.getStartShippingTime()))
                        && Objects.equals(item.getRemarks(), infoExcel.getRemarks())
                )
                .findFirst()
                .ifPresentOrElse(
                        item -> item.setStoreNum(item.getStoreNum() + quantity),
                        () -> list.add(FactoryRemainInventoryDto.builder()
                                .virtualSku(infoExcel.getVirtualSku())
                                .shipmentCode(infoExcel.getShipmentCode())
                                .warehouse(infoExcel.getStoreHouse())
                                .enableUsingDate(convertToDate(infoExcel.getInventoryTime()))
                                .startShippingTime(convertToStartShippingDate(infoExcel.getStartShippingTime()))
                                .storeNum(quantity)
                                .remarks(infoExcel.getRemarks())
                                .build())
                );

    }

    public void addStockQuantityAll(List<FactoryRemainInventoryDto> infoExcel) {
        list.addAll(infoExcel);
    }

    private double convertToDouble(String stockQuantity) {
        if (stockQuantity == null) {
            throw new IllegalArgumentException("数字不能为空");
        }
        if (!(NumberUtil.isInteger(stockQuantity) && Integer.parseInt(stockQuantity) >= 0)) {
            throw new IllegalArgumentException("请正确填写数字");
        }

        return Integer.parseInt(stockQuantity);
    }

    //判断日期格式是否正确
    private DateTime convertToDate(String inputDate) {
        DateTimeFormatter inputFormatter = DateTimeFormatter.ofPattern(YYYY_M_D_DATE_FORMAT_SLASH);

        try {
            // 尝试解析日期字符串
            LocalDate date = LocalDate.parse(inputDate, inputFormatter);
            //判断日期是否大于当前日期，使用java8的日期格式
            if (!date.isAfter(LocalDate.now())) {
                throw new IllegalArgumentException("请填写大于当前日期");
            }
            // 将日期格式化为所需的格式
            return DateUtil.parse(inputDate);
        } catch (DateTimeParseException e) {
            throw new IllegalArgumentException("请正确填写日期");
        }
    }

    private DateTime convertToStartShippingDate(String inputDate) {
        if (StrUtil.isBlank(inputDate)) {
            throw new IllegalArgumentException("出货时间为空");
        }
        DateTimeFormatter inputFormatter = DateTimeFormatter.ofPattern(YYYY_M_D_DATE_FORMAT_SLASH);

        try {
            // 尝试解析日期字符串
            LocalDate.parse(inputDate, inputFormatter);
            // 将日期格式化为所需的格式
            return DateUtil.parse(inputDate);
        } catch (DateTimeParseException e) {
            throw new IllegalArgumentException("出货时间格式必须是YYYY/MM/DD");
        }
    }
}
