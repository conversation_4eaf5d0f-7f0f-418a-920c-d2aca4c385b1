package com.purchase.purchase_server.utils.easyExcelUtil.listener.Replenishment;


import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.metadata.data.ReadCellData;
import com.alibaba.excel.util.ConverterUtils;
import com.crafts_mirror.utils.provider.ApplicationContextProvider;
import com.purchase.purchase_server.entity.dataObject.VirtualProductDO;
import com.purchase.purchase_server.entity.dto.SenboWarehouseDto;
import com.purchase.purchase_server.entity.vo.SenboWarehouseVo;
import com.purchase.purchase_server.entity.vo.UpgradeInfoInteriorVo;
import com.purchase.purchase_server.enums.RepStockingRulesImportHeadEnum;
import com.purchase.purchase_server.model.purchase.RepStockingRulesInfoDp;
import com.purchase.purchase_server.repository.interiorRepository.ShippingRatioRepository;
import com.purchase.purchase_server.utils.easyExcelUtil.manager.ReplenishmentManager;
import com.purchase.purchase_server.utils.easyExcelUtil.manager.ReplenishmentMapManager;
import lombok.extern.slf4j.Slf4j;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description 导入补货计划-备货规则
 * <AUTHOR>
 * @Date 2024/1/15 15:49
 **/
@Slf4j
public class RepStockingRulesImportListener extends AbstractRepImportListener<Map<Integer, String>> {

    private final ShippingRatioRepository shippingRatioRepository = ApplicationContextProvider.getBean(ShippingRatioRepository.class);

    private final Set<String> warehouseSet;

    private final Map<String, String> nameIdMap;

    public RepStockingRulesImportListener(List<SenboWarehouseDto> senboWarehouseList) {
        super(senboWarehouseList);
        this.warehouseSet = senboWarehouseList.stream().map(SenboWarehouseDto::getSenboWarehouseId).collect(Collectors.toSet());
        this.nameIdMap = senboWarehouseList.stream().collect(Collectors.toMap(SenboWarehouseDto::getSenboWarehouse, SenboWarehouseDto::getSenboWarehouseId));
    }

    @Override
    public void invoke(Map<Integer, String> data, AnalysisContext analysisContext) {
//        log.warn("导入补货计划-备货规则-------------导入excel进行中，成功解析excel");
        Integer approximateRowNumber = analysisContext.readSheetHolder().getApproximateTotalRowNumber();
        if (approximateRowNumber != null && approximateRowNumber > TOTAL_ROW_NUMBER) {
            throw new RuntimeException(String.format("导入数据超过%d行，请缩小导入数据量", TOTAL_ROW_NUMBER));
        }

        // 替换数据格式
        HashMap<String, String> keyReplacementMap = new HashMap<>();
        ReplenishmentMapManager replenishmentMapManager = ReplenishmentMapManager.getInstance();
        for (Map.Entry<Integer, String> entry : new HashMap<>(data).entrySet()) {
            Integer key = entry.getKey();
            String value = entry.getValue();
            // 根据第二个HashMap中的映射关系，将key替换为对应的value
            if (replenishmentMapManager.getStockingRulesHeadMap().containsKey(key)) {
                data.remove(key);
                keyReplacementMap.put(replenishmentMapManager.getStockingRulesHeadMap().get(key), value);
            }
        }

        String virtualSku = keyReplacementMap.get("*虚拟SKU");

        VirtualProductDO virtualProductDO = replenishmentMapManager.getSkuVirtualProductMap().get(virtualSku);
        if (virtualProductDO == null) {
            virtualProductDO = replenishmentMapManager.getOldSkuVirtualProductMap().get(virtualSku);
            if (virtualProductDO != null && virtualSku.equals(virtualProductDO.getOldSku())) {
                throw new IllegalArgumentException("不可以填写老sku");
            }
            throw new IllegalArgumentException("虚拟sku不存在");
        }

        if (StrUtil.isNotBlank(virtualProductDO.getUpgradeId())) {
            UpgradeInfoInteriorVo upgradeInfoInteriorVo = selectUpgradeInfo(virtualProductDO.getUpgradeId());
            if (ObjectUtil.isNotEmpty(upgradeInfoInteriorVo)) {
                if (upgradeInfoInteriorVo.getOriginalSkuId().equals(virtualProductDO.getId())) {
                    replenishmentMapManager.putStockingRulesUpgradeMap(upgradeInfoInteriorVo.getOriginalSku(), upgradeInfoInteriorVo.getUpgradeSku());
                    return;
                }
            }
        }

        new RepStockingRulesInfoDp(keyReplacementMap, warehouseSet);
        if (!replenishmentMapManager.addStockingRulesSet(virtualSku)) {
            throw new IllegalArgumentException("虚拟sku重复");
        }

        SenboWarehouseVo virtualShippingRatio = shippingRatioRepository.getVirtualShippingRatio(virtualProductDO.getId());

        if (ObjectUtil.isEmpty(virtualShippingRatio) || CollectionUtil.isEmpty(virtualShippingRatio.getVirtualShippingRatioDtoList())) {
            throw new IllegalArgumentException("虚拟sku:" + virtualProductDO.getVirtualSku() + "发货比例不存在");
        }

        List<SenboWarehouseDto> warehouseList = virtualShippingRatio.getSenboWarehouseList();
        warehouseList.forEach(warehouse -> {
            keyReplacementMap.put(String.valueOf(warehouse.getSenboWarehouseId()), warehouse.getShippingRatio()+"%");
        });
        replenishmentMapManager.putStockingRulesMap(keyReplacementMap.get(RepStockingRulesImportHeadEnum.VIRTUAL_SKU.getCode()), keyReplacementMap);

        ReplenishmentManager manager = ReplenishmentManager.getInstance();
        manager.incrementSuccessfulTimes();
    }

    /**
     * 这里会一行行的返回头
     *
     * @param headMap 表头map
     * @param context 上下文
     */
    @Override
    public void invokeHead(Map<Integer, ReadCellData<?>> headMap, AnalysisContext context) {

        ReplenishmentMapManager replenishmentMapManager = ReplenishmentMapManager.getInstance();
        Map<Integer, String> map = ConverterUtils.convertToStringMap(headMap, context);
        //删除map中的空值
        map.values().removeIf(Objects::isNull);
        List<String> headList = RepStockingRulesImportHeadEnum.getCodeList();
        List<String> headMapList = new ArrayList<>(map.values());

        //比较两个list集合值是否相等
        if (!headList.equals(headMapList)) {
            throw new RuntimeException("备货规则表头错误，请检查表头是否正确");
        }
        for (Map.Entry<Integer, String> entry : map.entrySet()) {
            if (RepStockingRulesImportHeadEnum.isExist(entry.getValue())){
                Integer key = entry.getKey();
                String value = entry.getValue();
                if (RepStockingRulesImportHeadEnum.ofCode(value) != null) {
                    replenishmentMapManager.putStockingRulesHeadMap(key,RepStockingRulesImportHeadEnum.ofCode(value).getCode());
                } else {
                    replenishmentMapManager.putStockingRulesHeadMap(key, nameIdMap.get(value));
                }
            } else {
                throw new RuntimeException("备货规则表头错误，请检查表头是否正确");
            }
        }

    }
}
