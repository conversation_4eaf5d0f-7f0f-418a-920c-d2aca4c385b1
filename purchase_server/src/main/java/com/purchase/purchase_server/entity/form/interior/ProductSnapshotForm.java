package com.purchase.purchase_server.entity.form.interior;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/7/29 13:25
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProductSnapshotForm implements Serializable {
    @Serial
    private static final long serialVersionUID = 6654438617293679053L;

    private List<String> snapshotList;

    private List<String> virtualSkuId;

    private List<String> selfSkuIds;

    private List<String> factoryInfoIds;
}
