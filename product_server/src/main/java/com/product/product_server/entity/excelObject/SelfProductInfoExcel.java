package com.product.product_server.entity.excelObject;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.product.product_server.convert.CommodityInspectionConverter;
import com.product.product_server.convert.ContainerLoaderNumberConverter;
import com.product.product_server.convert.CustomerStringIntegerConverter;
import lombok.*;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @Description excel导入产品转换成的实体列
 * <AUTHOR>
 * @Date 2023/12/7 17:00
 **/
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SelfProductInfoExcel extends BaseExcel implements Serializable {
    @Serial
    private static final long serialVersionUID = -1439495840202434L;

    @ExcelProperty("* 大类")
    private String category;

    @ExcelProperty("* SKU")
    private String sku;

    @ExcelProperty("图片链接")
    private String image;

    @ExcelProperty("* 产品名称")
    private String productName;

    @ExcelProperty("* 产品经理")
    private String buyer;

    @ExcelProperty("* 供应商代码")
    private String factoryCode;

    @ExcelProperty("渠道")
    private String channel;

    @ExcelProperty("父SPU ID")
    private String spu;

    @ExcelProperty("父SPU品名")
    private String spuProductName;

    @ExcelProperty("老SKU")
    private String oldSku;

    @ExcelProperty("虚拟SKU")
    private String virtualSku;

    @ExcelProperty("运营")
    private String operator;

    @ExcelProperty("产品状态")
    private String productStatus;

    @ExcelProperty("产品类型")
    private String productType;

    @ExcelProperty("子体类型")
    private String subType;

    @ExcelProperty("* 外箱规格长")
    private Double caseLength;

    @ExcelProperty("* 外箱规格宽")
    private Double caseWidth;

    @ExcelProperty("* 外箱规格高")
    private Double caseHeight;

    @ExcelProperty("* 重量单位")
    private String unitOfWeight;

    @ExcelProperty("* 单箱毛重")
    private Double singleCaseGrossWeight;

    @ExcelProperty(value = "* 单箱数量(pcs)", converter = ContainerLoaderNumberConverter.class)
    private Integer containerLoad;

    @ExcelProperty(value = "* 是否商检", converter = CommodityInspectionConverter.class)
    private Integer commodityInspection;

    @ExcelProperty("* 生产交期")
    private String purchaseDate;

    @ExcelProperty(value = "* 含税", converter = CustomerStringIntegerConverter.class)
    private Integer taxes;

    @ExcelProperty("* 单价")
    private BigDecimal price;

    @ExcelProperty("含税单价")
    private BigDecimal priceWithTaxes;

    @ExcelProperty("自定义SKU备注")
    private String selfRemarks;

    @ExcelProperty("虚拟SKU备注")
    private String virtualRemarks;

    @ExcelIgnore
    private String currency;
}
