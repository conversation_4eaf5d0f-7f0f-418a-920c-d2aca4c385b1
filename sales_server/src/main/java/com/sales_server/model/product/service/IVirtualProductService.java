package com.sales_server.model.product.service;

import com.sales_server.model.product.entity.dp.VirtualProductInfoDp;
import com.sales_server.model.targetSales.entity.form.TargetMonthSalesPageForm;

import java.util.List;

public interface IVirtualProductService {

    List<String> getVirtualSkuIdListBySearchInfo(TargetMonthSalesPageForm form);

    List<VirtualProductInfoDp> getVirtualProductInfoListByVirtualIdList(List<String> virtualIdList);
}
