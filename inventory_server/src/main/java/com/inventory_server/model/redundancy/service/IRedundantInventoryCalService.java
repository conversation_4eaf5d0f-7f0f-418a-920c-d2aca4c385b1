package com.inventory_server.model.redundancy.service;

import com.inventory_server.model.redundancy.entity.dp.InventoryCalDp;
import com.inventory_server.model.redundancy.entity.dto.DeliveryCalResultDto;
import com.inventory_server.model.redundancy.entity.dto.RedundancyResultDto;
import com.inventory_server.model.redundancy.entity.dto.SalableDateDto;
import com.inventory_server.model.redundancy.entity.dto.SoldOutDateBeforeTheoryDto;
import com.inventory_server.model.redundancy.entity.vo.DeliveryCalculationVo;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;

public interface IRedundantInventoryCalService {

    /**
     * 计算工厂冗余
     *
     * @param inventoryCalDpList 导入的商品数据（日销、发货比例之类的）
     * @param shippingResult     发货结果（包括库存、计划的所有数量的发货）
     */
    void calFactoryRedundantInventory(List<InventoryCalDp> inventoryCalDpList, DeliveryCalculationVo shippingResult,
                                      RedundancyResultDto redundantInventoryResult, Map<String, Integer> headShippingDateMap,
                                      int solidDays);

    /**
     * 计算海外仓冗余
     *
     * @param inventoryCalDpList                    导入的商品数据（日销、发货比例之类的）
     * @param resultDto                             总冗余（里面只包含工厂冗余部分，需要将海外仓冗余计算完成后塞值进去）
     * @param resultWithoutFactoryPlanAndOnShipping 不包括发货以及在途数据的发货计划
     */
    void calForeignRedundantInventory(List<InventoryCalDp> inventoryCalDpList, RedundancyResultDto resultDto,
                                      DeliveryCalculationVo resultWithoutFactoryPlanAndOnShipping,
                                      Map<String, Integer> headShippingDateMap, int solidDays);

    /**
     * 计算理论售罄实践
     *
     * @param shippingResult  发货结果（只包含在途，没有任何计划里的发货数据）
     * @param inventoryCalDps 导入的商品数据（日销、发货比例之类的）
     * @return 虚拟sku-理论断货日
     */
    Map<String, LocalDate> calTheoreticalSoldOutDate(DeliveryCalculationVo shippingResult, List<InventoryCalDp> inventoryCalDps);

    /**
     * 计算售罄前断货天数
     *
     * @param deliveryCalResultList 发货结果（只包含在途，没有任何计划里的发货数据）
     * @param inventoryCalDps       导入的商品数据（日销、发货比例之类的）
     * @param soldOutDateMap        虚拟sku-理论断货日
     * @return 虚拟sku-断货天数
     */
    Map<String, List<SoldOutDateBeforeTheoryDto>> calAllVirtualSkuRealSoldOutDate(List<DeliveryCalResultDto> deliveryCalResultList,
                                                                                  List<InventoryCalDp> inventoryCalDps,
                                                                                  Map<String, LocalDate> soldOutDateMap);

    Map<String, SalableDateDto> calAllVirtualSkuRealSoldOutDate(List<DeliveryCalResultDto> deliveryResultList);

    List<SoldOutDateBeforeTheoryDto> calRealSoldOutDate(Map<String, Map<String, Double>> everydayRemainMap,
                                                        Map<String, BigDecimal> targetSalesMap, LocalDate soldOutDate);
}
