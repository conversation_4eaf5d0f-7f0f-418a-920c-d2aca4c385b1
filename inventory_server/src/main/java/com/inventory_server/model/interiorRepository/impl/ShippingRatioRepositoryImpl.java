package com.inventory_server.model.interiorRepository.impl;

import com.alibaba.fastjson2.JSON;
import com.crafts_mirror.common.security.utils.SecurityUtils;
import com.crafts_mirror.utils.utils.RestTemplateUtils;
import com.inventory_server.applications.vo.response.ResultDTO;
import com.inventory_server.model.interiorRepository.ShippingRatioRepository;
import com.inventory_server.model.warehouse.entity.vo.SenboWarehouseVo;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import static com.crafts_mirror.utils.constant.SystemConstant.PRODUCTS_SHIPPING_RATIO_URL;

/**
 * @Description 获取森帛仓库信息
 * <AUTHOR>
 * @Date 2024/7/16 17:56
 **/
@Service
public class ShippingRatioRepositoryImpl implements ShippingRatioRepository {

    @Resource
    protected RestTemplate restTemplate;

    @Override
    public SenboWarehouseVo getVirtualShippingRatio(String virtualSkuId) {
        RestTemplateUtils restTemplateUtils = new RestTemplateUtils(restTemplate, SecurityUtils.getToken());
        ResultDTO senboWarehouseVo = restTemplateUtils.get(PRODUCTS_SHIPPING_RATIO_URL + virtualSkuId, ResultDTO.class);
        return JSON.to(SenboWarehouseVo.class, senboWarehouseVo.getData());
    }
}
