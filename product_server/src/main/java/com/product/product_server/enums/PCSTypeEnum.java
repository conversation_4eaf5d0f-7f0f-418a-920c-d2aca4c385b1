package com.product.product_server.enums;

import cn.hutool.core.util.StrUtil;
import com.product.product_server.model.products.CalPCSTypeDp;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;

import static java.math.RoundingMode.HALF_UP;

@Getter
@AllArgsConstructor
public enum PCSTypeEnum {
    ACCESSORY(1, "配件"),
    SUPER_OVERSIZE(2, "超大件"),
    SMALL_OVERSIZE_AHS(3, "小号大件（AHS）"),
    SMALL_OVERSIZE(4, "小号大件"),
    OVERSIZE_STANDARD(5, "大号标准");

    private final Integer code;
    private final String desc;

    public static PCSTypeEnum getOfCodeNullable(Integer code) {
        return Arrays.stream(PCSTypeEnum.values())
                .filter(it -> it.getCode().equals(code))
                .findFirst()
                .orElse(null);
    }

    public static PCSTypeEnum getOfCodeNoneNull(Integer code) {
        return Arrays.stream(PCSTypeEnum.values())
                .filter(it -> it.getCode().equals(code))
                .findFirst()
                .orElseThrow(() -> new IllegalArgumentException("没有找到对应的件的类型"));
    }

    /**
     * 若产品名称包含配件，则件的类型为“配件”
     * 若ups限长在
     * @param typeDp
     * @return
     */
    public static PCSTypeEnum getByProductInfo(CalPCSTypeDp typeDp) {
        if (typeDp.pcsType() != null) return getOfCodeNoneNull(typeDp.pcsType());

        String productName = typeDp.productName();
        if (StrUtil.isNotBlank(productName) && productName.contains("配件")) return ACCESSORY;

        List<Double> sortSpecification = typeDp.specificationDp().sort();
        BigDecimal max = BigDecimal.valueOf(sortSpecification.getFirst());
        BigDecimal second = BigDecimal.valueOf(sortSpecification.get(1));
        BigDecimal third = BigDecimal.valueOf(sortSpecification.get(2));

        // 计算ups限长
        BigDecimal upsBigDecimal = second.add(third);
        double ups = upsBigDecimal.multiply(BigDecimal.TWO).add(max).setScale(6, HALF_UP).doubleValue();

        if (ups >= 330) return SUPER_OVERSIZE;
        if (ups >= 266) return SMALL_OVERSIZE_AHS;

        // 单个产品毛重
        Double grossWeight = typeDp.caseWeightInfoDp().caseGrossWeight();
        BigDecimal divideResult = BigDecimal.valueOf(grossWeight).divide(BigDecimal.valueOf(0.453), HALF_UP);
        if (divideResult.compareTo(BigDecimal.valueOf(50)) >= 0) return SMALL_OVERSIZE_AHS;

        if (max.compareTo(BigDecimal.valueOf(45.7)) > 0) return SMALL_OVERSIZE;

        if (second.compareTo(BigDecimal.valueOf(35.5)) > 0) return SMALL_OVERSIZE;

        if (third.compareTo(BigDecimal.valueOf(20.3)) > 0) return SMALL_OVERSIZE;

        if (grossWeight >= 9.7) return SMALL_OVERSIZE;

        return OVERSIZE_STANDARD;
    }
}
