package com.purchase.purchase_server.entity.dto.delivery;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * @Description 试算看板工厂信息
 * <AUTHOR>
 * @Date 2024/1/10 13:55
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ShippingProjectWatchBoardFactoryDto implements Serializable {
    private Date finishDate;

    private Integer restAmount;

    private String factoryFinishedId;

    private List<ShippingProjectWatchBoardWarehouseDto> smallRowData;
}
