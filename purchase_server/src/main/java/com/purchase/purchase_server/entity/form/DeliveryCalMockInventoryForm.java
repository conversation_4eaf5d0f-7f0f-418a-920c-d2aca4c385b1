package com.purchase.purchase_server.entity.form;

import com.purchase.purchase_server.entity.dto.FactoryRemainInventoryDto;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @Description 试算的入参
 * <AUTHOR>
 * @Date 2025/1/6 16:39
 **/
@Data
public class DeliveryCalMockInventoryForm implements Serializable {
    private String shippingProjectId;

    private String virtualSku;

    private List<FactoryRemainInventoryDto> remainInventoryList;
}
