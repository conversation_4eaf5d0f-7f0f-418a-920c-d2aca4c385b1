package com.purchase.purchase_server.mapper.Lcl;

import com.github.yulichang.base.MPJBaseMapper;
import com.purchase.purchase_server.entity.bo.Lcl.LclContainerLoadingBO;
import com.purchase.purchase_server.entity.dataObject.Lcl.LclConsolidationFinishedInventoryDO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【cm_lcl_consolidation_finished_inventory(拼柜装柜计划清理数据表)】的数据库操作Mapper
* @createDate 2024-12-04 16:47:06
* @Entity generator.domain.CmLclConsolidationFinishedInventory
*/
public interface LclConsolidationFinishedInventoryMapper extends MPJBaseMapper<LclConsolidationFinishedInventoryDO> {

    int delete(@Param("factoryFinishedId") String factoryFinishedId);

    int deleteByLclRecordId(@Param("lclRecordId") String lclRecordId);

    List<LclContainerLoadingBO> listWithTrial(@Param("lclRecordId") String lclRecordId,
                                              @Param("shippingStartDate") String shippingStartDate);
}




