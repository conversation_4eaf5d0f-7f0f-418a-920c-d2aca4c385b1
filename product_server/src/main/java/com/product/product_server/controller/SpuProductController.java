package com.product.product_server.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.crafts_mirror.common.security.annotation.RequiresPermissions;
import com.crafts_mirror.utils.dp.LogTrackNumDto;
import com.product.product_server.entity.LogTrackNumMapDto;
import com.product.product_server.entity.form.RemarksSaveForm;
import com.product.product_server.entity.form.SpuInfoSaveForm;
import com.product.product_server.entity.form.VirtualProductSearchForm;
import com.product.product_server.entity.response.ResultDTO;
import com.product.product_server.entity.vo.SpuDetailVo;
import com.product.product_server.entity.vo.SpuProductListVo;
import com.product.product_server.service.ISpuProductService;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.*;

/**
 * @Description Spu相关商品controller层
 * <AUTHOR>
 * @Date 2023/12/14 20:24
 **/
@RestController
@RequestMapping("spu")
public class SpuProductController {

    @Resource
    private ISpuProductService spuProductService;

    @PostMapping("save/spuInfo")
    @ResponseBody
    public ResultDTO<Boolean> saveSpuInfo(@RequestBody SpuInfoSaveForm form) {
        return ResultDTO.success(spuProductService.saveSpuProductInfo(form));
    }

    @RequiresPermissions("product:virtualProduct:list")
    @PostMapping("pageList")
    @ResponseBody
    public ResultDTO<IPage<SpuProductListVo>> spuInfoPage(@RequestBody VirtualProductSearchForm form) {
        return ResultDTO.success(spuProductService.getSpuInfoPage(form));
    }

    @RequiresPermissions("product:virtualProduct:list")
    @PostMapping ("save/remarks")
    @ResponseBody
    public ResultDTO<Boolean> saveRemarks(@RequestBody RemarksSaveForm form) {
        return ResultDTO.success(spuProductService.saveSpuRemarks(form, new LogTrackNumDto(form.getId())));
    }

    @RequiresPermissions("product:virtualProduct:delete")
    @GetMapping("delete/spuProduct")
    @ResponseBody
    public ResultDTO<Boolean> deleteSpuProduct(@RequestParam("spuProductId") String spuProductId) {
        return ResultDTO.success(spuProductService.deleteSpuProduct(spuProductId, new LogTrackNumDto(spuProductId)));
    }

    @RequiresPermissions("product:virtualProduct:list")
    @GetMapping("delete/spuProductBatch")
    @ResponseBody
    public ResultDTO<Boolean> deleteSpuProductBatch(@RequestBody VirtualProductSearchForm form) {
        return ResultDTO.success(spuProductService.deleteSpuProductBatch(form.getSpuIdList(), new LogTrackNumMapDto()));
    }

    @RequiresPermissions("product:virtualProduct:list")
    @GetMapping("detail")
    @ResponseBody
    public ResultDTO<SpuDetailVo> spuProductDetail(@RequestParam("spuProductId") String spuProductId) {
        return ResultDTO.success(spuProductService.getSpuDetailInfo(spuProductId));
    }
}
