package com.purchase.purchase_server.mapper;

import com.github.yulichang.base.MPJBaseMapper;
import com.purchase.purchase_server.entity.dataObject.YicangPurchaseOrderVirtualDO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【cm_yicang_purchase_order_virtual(易仓采购单虚拟sku表)】的数据库操作Mapper
* @createDate 2024-11-01 14:30:43
*/
public interface YicangPurchaseOrderVirtualMapper extends MPJBaseMapper<YicangPurchaseOrderVirtualDO> {

    int removeBySbPoDetailIds(@Param("sbPoDetailIds") List<String> sbPoDetailIds);
}




