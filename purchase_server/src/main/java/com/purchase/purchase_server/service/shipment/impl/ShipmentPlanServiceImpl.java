package com.purchase.purchase_server.service.shipment.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelReader;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.read.metadata.ReadSheet;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.crafts_mirror.common.security.utils.SecurityUtils;
import com.crafts_mirror.utils.aop.OperationLog;
import com.crafts_mirror.utils.enums.ResponseCodeEnum;
import com.crafts_mirror.utils.utils.RestTemplateUtils;
import com.crafts_mirror.utils.utils.SnowflakeIdWorker;
import com.purchase.purchase_server.entity.LogTrackNumDto;
import com.purchase.purchase_server.entity.dataObject.ShipmentDetailDO;
import com.purchase.purchase_server.entity.dataObject.ShipmentPlanDO;
import com.purchase.purchase_server.entity.dto.SenboWarehouseDto;
import com.purchase.purchase_server.entity.dto.Shipment.ShipmentDetailDto;
import com.purchase.purchase_server.entity.excelObject.ShipmentDetailExcel;
import com.purchase.purchase_server.entity.excelObject.ShipmentPlanExcel;
import com.purchase.purchase_server.entity.form.FileMissionForm;
import com.purchase.purchase_server.entity.form.ShipmentPlanForm;
import com.purchase.purchase_server.entity.form.VirtualProductSearchForm;
import com.purchase.purchase_server.entity.response.ResultDTO;
import com.purchase.purchase_server.entity.vo.ShipmentPlan.ShipmentDetailVO;
import com.purchase.purchase_server.entity.vo.ShipmentPlan.ShipmentPlanVO;
import com.purchase.purchase_server.entity.vo.UserInteriorVO;
import com.purchase.purchase_server.entity.vo.VirtualProductListVo;
import com.purchase.purchase_server.enums.IsOldStatusEnum;
import com.purchase.purchase_server.repository.dataRepository.Shipment.ShipmentDetailRepositoryImpl;
import com.purchase.purchase_server.repository.dataRepository.Shipment.ShipmentPlanRepositoryImpl;
import com.purchase.purchase_server.repository.interiorRepository.WarehouseRepository;
import com.purchase.purchase_server.service.FileCenterService;
import com.purchase.purchase_server.service.channel.IChannelInfoService;
import com.purchase.purchase_server.service.replenishment.ISysUserInteriorService;
import com.purchase.purchase_server.service.shipment.IShipmentPlanService;
import com.purchase.purchase_server.utils.easyExcelUtil.listener.Shipment.ShipmentDetailImportListener;
import com.purchase.purchase_server.utils.easyExcelUtil.listener.Shipment.ShipmentPlanImportListener;
import com.purchase.purchase_server.utils.easyExcelUtil.template.ShipmentPlan.ExcelTemplateGenerator;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import java.io.InputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.stream.Collectors;

import static com.crafts_mirror.utils.constant.SystemConstant.*;
import static com.purchase.purchase_server.entity.consts.FilePathConstant.FILE_PATH_PURCHASE_SHIPMENT;

/**
 * <AUTHOR>
 * @description 针对表【cm_shipment_plan(货件计划表)】的数据库操作Service实现
 * @createDate 2024-11-18 13:59:01
 */
@Service
@Slf4j
public class ShipmentPlanServiceImpl implements IShipmentPlanService {

    @Resource
    private ShipmentPlanRepositoryImpl shipmentPlanRepository;
    @Resource
    private RestTemplate restTemplate;
    @Resource
    private SnowflakeIdWorker snowflakeIdWorker;
    @Resource
    private FileCenterService fileCenterService;
    @Resource
    private WarehouseRepository warehouseRepository;

    @Resource
    private ShipmentDetailRepositoryImpl shipmentDetailRepository;

    @Resource
    private ISysUserInteriorService sysUserInteriorService;

    @Resource
    private IChannelInfoService channelInfoService;

    @Override
    @Async
    public void importShipmentPlanInfo(InputStream file, byte[] fileBytes, String fileName) {
        RestTemplateUtils restTemplateUtil = new RestTemplateUtils(restTemplate, SecurityUtils.getToken());
        //上传oss
        String snowId = String.valueOf(snowflakeIdWorker.nextId());
        String key = snowId + "." + Objects.requireNonNull(fileName).substring(fileName.lastIndexOf(".") + 1);
        MultiValueMap<String, Object> httpEntity = fileCenterService.putFile(fileBytes, fileName, FILE_PATH_PURCHASE_SHIPMENT + key, DateUtil.offsetDay(new Date(), 730).toString());
        ResultDTO<String> resultDTO = restTemplateUtil.post(httpEntity, ResultDTO.class, FILE_SYSTEM_PUT_OBJECT_BY_FILE);
        if (!Objects.equals(resultDTO.getStatus(), ResponseCodeEnum.OK.getCode())) {
            log.error("导入货件计划时上传oss失败，异常原因：{}", resultDTO.getMessage());
            throw new RuntimeException("导入货件计划时上传oss失败，异常原因：" + resultDTO.getMessage());
        }
        ResultDTO<String> restResult = restTemplateUtil.post(
                FileMissionForm.builder().fileName(fileName)
                        .importStatus("处理中").type("货件计划导入记录")
                        .filePath(key)
                        .build(),
                ResultDTO.class, FILE_SYSTEM_MISSION_CENTER_URL
        );
        if (!Objects.equals(restResult.getStatus(), ResponseCodeEnum.OK.getCode())) {
            log.error("导入货件计划时插入文件中心失败，异常原因：{}", restResult.getMessage());
            throw new RuntimeException("导入货件计划时插入文件中心失败，异常原因：" + restResult.getMessage());
        }

        List<String> errorList = new CopyOnWriteArrayList<>();
        List<ShipmentPlanDO> shipmentPlanDOList = new ArrayList<>();
        ExcelReader excelReader = null;
        try {
            // 从数据库中获取现有的仓库列表
            List<SenboWarehouseDto> senboWarehouseList = warehouseRepository.getSenboWarehouseList();
            ReadSheet readSheet =
                    EasyExcel.readSheet(0).headRowNumber(1).head(ShipmentPlanExcel.class)
                            .registerReadListener(new ShipmentPlanImportListener(shipmentPlanDOList, senboWarehouseList, errorList)).autoTrim(true).build();
            excelReader = EasyExcel.read(file).build();
            excelReader.read(readSheet);

            restTemplateUtil.post(
                    FileMissionForm.builder()
                            .missionId(restResult.getData())
                            .importStatus("导入完成")
                            .type("货件计划导入记录")
                            .failedResultList(errorList)
                            .importResult(String.format(String.format("成功：%s,失败：%s",
                                    shipmentPlanDOList.size(), errorList.size())))
                            .finishDate(new Date())
                            .build(),
                    ResultDTO.class, FILE_SYSTEM_MISSION_CENTER_URL);
        } catch (Exception e) {
            log.error("导入货件计划异常" + e);
            restTemplateUtil.post(
                    FileMissionForm.builder()
                            .missionId(restResult.getData())
                            .importStatus("导入失败")
                            .type("货件计划导入记录")
                            .failedResultList(Collections.singletonList(e.getMessage()))
                            .finishDate(new Date())
                            .build(),
                    ResultDTO.class, FILE_SYSTEM_MISSION_CENTER_URL);
        } finally {

            if (excelReader != null) {
                log.info("导入货件计划关闭");
                excelReader.finish();
            }
        }
    }

    @Override
    @Async
    public void importShipmentDetailInfo(InputStream file, byte[] fileBytes, String fileName) {
        RestTemplateUtils restTemplateUtil = new RestTemplateUtils(restTemplate, SecurityUtils.getToken());
        //上传oss
        String snowId = String.valueOf(snowflakeIdWorker.nextId());
        String key = snowId + "." + Objects.requireNonNull(fileName).substring(fileName.lastIndexOf(".") + 1);
        MultiValueMap<String, Object> httpEntity = fileCenterService.putFile(fileBytes, fileName, FILE_PATH_PURCHASE_SHIPMENT + key, DateUtil.offsetDay(new Date(), 730).toString());
        ResultDTO<String> resultDTO = restTemplateUtil.post(httpEntity, ResultDTO.class, FILE_SYSTEM_PUT_OBJECT_BY_FILE);
        if (!Objects.equals(resultDTO.getStatus(), ResponseCodeEnum.OK.getCode())) {
            log.error("导入货件明细时上传oss失败，异常原因：{}", resultDTO.getMessage());
            throw new RuntimeException("导入货件明细时上传oss失败，异常原因：" + resultDTO.getMessage());
        }
        ResultDTO<String> restResult = restTemplateUtil.post(
                FileMissionForm.builder().fileName(fileName)
                        .importStatus("处理中").type("货件明细导入记录")
                        .filePath(key)
                        .build(),
                ResultDTO.class, FILE_SYSTEM_MISSION_CENTER_URL
        );
        if (!Objects.equals(restResult.getStatus(), ResponseCodeEnum.OK.getCode())) {
            log.error("导入货件明细时插入文件中心失败，异常原因：{}", restResult.getMessage());
            throw new RuntimeException("导入货件明细时插入文件中心失败，异常原因：" + restResult.getMessage());
        }

        List<String> errorList = new CopyOnWriteArrayList<>();
        List<ShipmentDetailDto> detailDtoList = new ArrayList<>();
        Map<String, String> channelNameIdMap = channelInfoService.getChannelNameIdMap();
        int countSum;
        ExcelReader excelReader = null;
        try {
            ReadSheet readSheet = EasyExcel.readSheet(0).headRowNumber(1).head(ShipmentDetailExcel.class)
                            .registerReadListener(new ShipmentDetailImportListener(errorList, detailDtoList)).autoTrim(true).build();
            excelReader = EasyExcel.read(file).build();
            excelReader.read(readSheet);

            countSum = detailDtoList.size();
            List<ShipmentDetailDto> resultDetailDtoList = aggregateShipmentQuantityByDetails(detailDtoList);

            List<ShipmentDetailDO> resultDetailDOList = new ArrayList<>();
            for (ShipmentDetailDto shipmentDetail : resultDetailDtoList) {
                var searchForm = VirtualProductSearchForm.builder()
                        .destinationSku(shipmentDetail.getDestinationSku())
                        .build();

                var virtualProductListVo = restTemplateUtil.post(searchForm, ResultDTO.class, PRODUCTS_VIRTUAL_INFO_URL);
                var list = JSON.to(List.class, virtualProductListVo.getData());
                var virtualInfo = JSON.to(VirtualProductListVo.class, list.getFirst());
                ShipmentDetailDO shipmentDetailDO = new ShipmentDetailDO();
                shipmentDetailDO.setShipmentPlanId(shipmentDetail.getShipmentPlanId());
                shipmentDetailDO.setContractNo(shipmentDetail.getContractNo());
                shipmentDetailDO.setVirtualSkuId(virtualInfo.getVirtualSkuId());
                shipmentDetailDO.setDestinationSku(shipmentDetail.getDestinationSku());
                shipmentDetailDO.setIsOldStatus(shipmentDetail.getDestinationSku().equals(virtualInfo.getVirtualSku()) ?
                        IsOldStatusEnum.VIRTUAL_SKU.getCode() : IsOldStatusEnum.OLD_SKU.getCode());
                shipmentDetailDO.setOperator(virtualInfo.getOperatorList());
                shipmentDetailDO.setChannel(channelNameIdMap.getOrDefault(virtualInfo.getChannel().toUpperCase(Locale.ROOT), virtualInfo.getChannel()));
                shipmentDetailDO.setSelfProductId(virtualInfo.getSelfProductId());
                shipmentDetailDO.setSelfSku(virtualInfo.getSku());
                shipmentDetailDO.setSelfProductName(virtualInfo.getProductName());
                shipmentDetailDO.setImage(virtualInfo.getImage());
                shipmentDetailDO.setFactoryId(virtualInfo.getFactoryId());
                shipmentDetailDO.setFactoryCode(virtualInfo.getFactoryCode());
                shipmentDetailDO.setFactoryDeliveryDate(DateUtil.parse(shipmentDetail.getFactoryDeliveryDate()));
                shipmentDetailDO.setShippingQuantity(shipmentDetail.getShippingQuantity());
                List<ShipmentDetailDO> detailDOS = shipmentDetailRepository.list(shipmentDetail);

                if (CollectionUtil.isNotEmpty(detailDOS)) {
                    shipmentDetailDO.setId(detailDOS.getFirst().getId());
                }
                resultDetailDOList.add(shipmentDetailDO);
            }
            if (CollectionUtil.isNotEmpty(resultDetailDOList)) {
                shipmentDetailRepository.saveOrUpdateDetail(resultDetailDOList, new LogTrackNumDto());
                List<String> planIds = resultDetailDOList.stream().map(ShipmentDetailDO::getShipmentPlanId).toList();
                shipmentPlanRepository.updatePlanUpdateDate(planIds);
            }
            restTemplateUtil.post(
                    FileMissionForm.builder()
                            .missionId(restResult.getData())
                            .importStatus("导入完成")
                            .type("货件明细导入记录")
                            .failedResultList(errorList)
                            .importResult(String.format(String.format("成功：%s,失败：%s",
                                    countSum, errorList.size())))
                            .finishDate(new Date())
                            .build(),
                    ResultDTO.class, FILE_SYSTEM_MISSION_CENTER_URL);
        } catch (Exception e) {
            log.error("导入货件明细异常" + e);
            restTemplateUtil.post(
                    FileMissionForm.builder()
                            .missionId(restResult.getData())
                            .importStatus("导入失败")
                            .type("货件明细导入记录")
                            .failedResultList(Collections.singletonList(e.getMessage()))
                            .finishDate(new Date())
                            .build(),
                    ResultDTO.class, FILE_SYSTEM_MISSION_CENTER_URL);
        } finally {
            if (excelReader != null) {
                log.info("导入货件明细关闭");
                excelReader.finish();
            }
        }
    }

    private List<ShipmentDetailDto> aggregateShipmentQuantityByDetails(List<ShipmentDetailDto> detailDtoList) {
        Map<GroupKey, Integer> result = detailDtoList.stream()
                .collect(Collectors.groupingBy(
                        dto -> new GroupKey(
                                dto.getShipmentPlanId(),
                                dto.getShipmentCode(),
                                dto.getContractNo(),
                                dto.getDestinationSku(),
                                dto.getFactoryDeliveryDate()
                        ),
                        Collectors.summingInt(ShipmentDetailDto::getShippingQuantity)
                ));

        return result.entrySet().stream()
                .map(entry -> {
                    ShipmentDetailDto dto = new ShipmentDetailDto();
                    dto.setShipmentPlanId(entry.getKey().shipmentPlanId());
                    dto.setShipmentCode(entry.getKey().shipmentCode());
                    dto.setContractNo(entry.getKey().contractNo());
                    dto.setDestinationSku(entry.getKey().destinationSku());
                    dto.setFactoryDeliveryDate(entry.getKey().factoryDeliveryDate());
                    dto.setShippingQuantity(entry.getValue());
                    return dto;
                })
                .collect(Collectors.toList());
    }

    record GroupKey(
            String shipmentPlanId,
            String shipmentCode,
            String contractNo,
            String destinationSku,
            String factoryDeliveryDate
    ) {
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void save(ShipmentPlanDO val) {
        shipmentPlanRepository.save(val);
    }

    @Override
    public IPage<ShipmentPlanVO> pageList(ShipmentPlanForm form) {
        IPage<ShipmentPlanVO> page = shipmentPlanRepository.page(form);
        if (CollectionUtil.isEmpty(page.getRecords())) {
            return page;
        }
        List<UserInteriorVO> userList = sysUserInteriorService.getUserList();
        Map<String, String> collect = userList.stream().collect(Collectors.toMap(UserInteriorVO::getUserName, UserInteriorVO::getNickName));

        for (ShipmentPlanVO record : page.getRecords()) {
            String updateBy = collect.get(record.getUpdateBy());
            if (StrUtil.isBlank(updateBy)) {
                updateBy = record.getUpdateBy();
            }
            record.setUpdateBy(updateBy);
        }
        return page;
    }

    @Override
    public List<ShipmentDetailVO> getShipmentDetail(ShipmentPlanForm form) {
        List<ShipmentDetailVO> shipmentDetail = shipmentDetailRepository.getShipmentDetail(form);
        Map<String, String> channelIdNameMap = channelInfoService.getChannelIdNameMap();
        shipmentDetail.forEach(p -> p.setChannel(channelIdNameMap.getOrDefault(p.getChannel(), p.getChannel())));
        return shipmentDetail;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @OperationLog(content = "编辑计划时间", operationType = "货件计划管理")
    public boolean editShipmentPlan(ShipmentPlanForm form, LogTrackNumDto logTrackNumDto) {
        shipmentPlanRepository.updateById(ShipmentPlanDO.builder()
                .id(form.getPlanId())
                .actualLoadingTime(DateUtil.parse(form.getActualLoadingTime()))
                .shippingDate(DateUtil.parse(form.getShippingDate()))
                .estimatedArrivalTime(DateUtil.parse(form.getEstimatedArrivalTime()))
                .actualArrivalTime(DateUtil.parse(form.getActualArrivalTime()))
                .actualSignTime(DateUtil.parse(form.getActualSignTime()))
                .estimatedSignTime(DateUtil.parse(form.getEstimatedSignTime()))
                .warehouseShelfTime(DateUtil.parse(form.getWarehouseShelfTime()))
                .actualSign(DateUtil.parse(form.getActualSign()))
                .build());
        Map<String, String> logMap = new HashMap<>();
        logMap.put(form.getPlanId(), "编辑计划时间");
        logTrackNumDto.setLogMap(logMap);
        logTrackNumDto.setAuthorization(SecurityUtils.getToken());
        return Boolean.TRUE;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @OperationLog(content = "删除货件计划", operationType = "货件计划管理")
    public boolean deletePlan(String planId, LogTrackNumDto logTrackNumDto) {
        shipmentPlanRepository.deleteById(planId);
        shipmentDetailRepository.deleteByPlanId(planId);
        Map<String, String> logMap = new HashMap<>();
        logMap.put(planId, "删除货件计划");
        logTrackNumDto.setLogMap(logMap);
        logTrackNumDto.setAuthorization(SecurityUtils.getToken());
        return Boolean.TRUE;
    }

    @Override
    public void exportTemplate(HttpServletResponse response) {
        ExcelWriter excelWriter = null;
        try {
            response.setContentType("application/vnd.ms-excel");
            response.setCharacterEncoding("utf-8");
            String fileName = URLEncoder.encode("货件计划模版", StandardCharsets.UTF_8).replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
            excelWriter = EasyExcel.write(response.getOutputStream()).build();

            List<SenboWarehouseDto> senboWarehouseList = warehouseRepository.getSenboWarehouseList();
            ExcelTemplateGenerator excelTemplateGenerator = new ExcelTemplateGenerator();
            excelTemplateGenerator.generateTemplate(excelWriter, senboWarehouseList);

        } catch (Exception e) {
            log.error("获取输出流异常", e);
            throw new RuntimeException("获取输出流异常", e);
        } finally {
            if (excelWriter != null) {
                excelWriter.finish();
            }
        }
    }

    @Override
    public List<SenboWarehouseDto> getSenboWarehouseVo() {
        return warehouseRepository.getSenboWarehouseList();
    }
}




