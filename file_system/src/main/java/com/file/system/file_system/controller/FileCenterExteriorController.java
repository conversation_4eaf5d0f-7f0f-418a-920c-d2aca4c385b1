package com.file.system.file_system.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.file.system.file_system.entity.form.FileMissionPageForm;
import com.file.system.file_system.entity.response.ResultDTO;
import com.file.system.file_system.entity.vo.MissionCenterVo;
import com.file.system.file_system.service.IFileMissionCenterService;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.*;

/**
 * @Description 文档中心外部调用接口
 * <AUTHOR>
 * @Date 2023/12/12 14:04
 **/
@RestController
@RequestMapping("/center/exterior")
public class FileCenterExteriorController {

    @Resource
    private IFileMissionCenterService fileMissionCenterService;

    @PostMapping("pageList")
    @ResponseBody
    public ResultDTO<IPage<MissionCenterVo>> getMissionPage(@RequestBody FileMissionPageForm form) {
        return ResultDTO.success(fileMissionCenterService.getMissionPage(form));
    }
}
