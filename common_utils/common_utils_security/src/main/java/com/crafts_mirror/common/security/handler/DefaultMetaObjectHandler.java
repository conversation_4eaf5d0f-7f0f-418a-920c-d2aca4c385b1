package com.crafts_mirror.common.security.handler;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import com.crafts_mirror.common.security.utils.SecurityUtils;
import org.apache.ibatis.reflection.MetaObject;

import java.util.Date;

/**
 * <AUTHOR>
 */
public class DefaultMetaObjectHandler implements MetaObjectHandler {

    private static final String CREATE_DATE = "createDate";
    private static final String UPDATE_DATE = "updateDate";

    private static final String UPDATE_BY = "updateBy";

    private static final String CREATE_BY = "createBy";
    private static final String STATUS = "status";

    @Override
    public void insertFill(MetaObject metaObject) {
        if (null == getFieldValByName(STATUS, metaObject)) {
            setFieldValByName(STATUS, "0", metaObject);
        }
        if (null == getFieldValByName(CREATE_DATE, metaObject)) {
            setFieldValByName(CREATE_DATE, new Date(), metaObject);
        }
        if (null == getFieldValByName(UPDATE_DATE, metaObject)) {
            setFieldValByName(UPDATE_DATE, new Date(), metaObject);
        }

        String username = SecurityUtils.getUsername();
        String userName = StrUtil.isBlank(username) ? "系统" : username;

        if (null == getFieldValByName(UPDATE_BY, metaObject)) {
            setFieldValByName(UPDATE_BY, userName, metaObject);
        }
        if (null == getFieldValByName(CREATE_BY, metaObject)) {
            setFieldValByName(CREATE_BY, userName, metaObject);
        }
    }

    @Override
    public void updateFill(MetaObject metaObject) {
        if (null == getFieldValByName(UPDATE_DATE, metaObject)) {
            setFieldValByName(UPDATE_DATE, new Date(), metaObject);
        }

        String username = SecurityUtils.getUsername();
        username = StrUtil.isBlank(username) ? "系统" : username;
        if (null == getFieldValByName(UPDATE_BY, metaObject)) {
            setFieldValByName(UPDATE_BY, username, metaObject);
        }
    }
}
