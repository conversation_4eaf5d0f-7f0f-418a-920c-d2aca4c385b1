package com.sales_server.infrastructures.listener;

import cn.hutool.core.date.DateException;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.exception.ExcelDataConvertException;
import com.sales_server.infrastructures.exception.FieldNotExistException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.dao.DataIntegrityViolationException;

import java.time.format.DateTimeParseException;
import java.util.List;

/**
 * @Description 发货补货导入处理父类
 * <AUTHOR>
 * @Date 2024/3/20 14:44
 **/
@Slf4j
public abstract class AbstractImportListener<T> extends AnalysisEventListener<T> {
    public AbstractImportListener(List<String> errorList) {
        this.errorList = errorList;
    }

    private final List<String> errorList;

    @Override
    public void onException(Exception exception, AnalysisContext context) {
        switch (exception) {
            case ExcelDataConvertException excelException ->
                    errorList.add(String.format(" %s 第 %d 行，第 %d 列数据异常，请核对数据格式是否正确，该单元格是否多了前后空格",
                            context.readSheetHolder().getSheetName(),excelException.getRowIndex() + 1, excelException.getColumnIndex() + 1));
            case IllegalArgumentException illegalArgumentException ->
                    errorList.add(String.format(" %s 第 %d 行数据异常，异常原因 %s，请确认数据有无问题，无法确认的可联系开发人员",
                            context.readSheetHolder().getSheetName(),context.readSheetHolder().getRowIndex() + 1, illegalArgumentException.getMessage()));
            case DataIntegrityViolationException dataIntegrityViolationException ->
                    errorList.add(String.format(" %s 第 %d 行数据异常，异常原因 %s，请确认数据有无问题，无法确认的可联系开发人员",
                            context.readSheetHolder().getSheetName(),context.readSheetHolder().getRowIndex() + 1, dataIntegrityViolationException.getMessage()));
            case NullPointerException nullPointerException ->
                    errorList.add(String.format(" %s 第 %d 行数据异常，异常原因 %s，请确认数据有无问题，无法确认的可联系开发人员",
                            context.readSheetHolder().getSheetName(),context.readSheetHolder().getRowIndex() + 1, nullPointerException.getMessage()));
            case FieldNotExistException fieldNotExistException ->
                    errorList.add(String.format(" %s 第 %d 行数据异常，异常原因 %s，请联系开发配置",
                            context.readSheetHolder().getSheetName(),context.readSheetHolder().getRowIndex() + 1, fieldNotExistException.getMessage()));
            case DateTimeParseException ignored ->
                errorList.add(String.format(" %s 第 %d 行数据异常，异常原因 日期格式异常，请修改日期格式后重新上传",
                        context.readSheetHolder().getSheetName(),context.readSheetHolder().getRowIndex() + 1));
            case DateException ignored ->
                    errorList.add(String.format(" %s 第 %d 行数据异常，异常原因 日期格式异常，请修改日期格式后重新上传",
                            context.readSheetHolder().getSheetName(),context.readSheetHolder().getRowIndex() + 1));
            case RuntimeException runtimeException -> {
                errorList.add(String.format(" %s 第 %d 行数据异常，异常原因 %s，该异常未被正常捕获，造成后续数据都无法继续导入，请联系开发人员，修复异常数据后再次导入",
                        context.readSheetHolder().getSheetName(),context.readSheetHolder().getRowIndex() + 1, runtimeException.getMessage()));
                throw new RuntimeException(runtimeException.getMessage());
            }
            default ->
                    errorList.add(String.format(" %s 第 %d 行数据异常，异常原因 %s，请确认数据有无问题，无法确认的可联系开发人员",
                            context.readSheetHolder().getSheetName(),context.readSheetHolder().getRowIndex() + 1, exception.getMessage()));
        }
        log.warn("excel转实体类异常-{}，第 {} 行数据异常，异常原因：{}",
                context.readSheetHolder().getSheetName(),context.readSheetHolder().getRowIndex() + 1, exception.getMessage());
    }
}
