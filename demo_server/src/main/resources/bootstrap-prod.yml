spring:
  cloud:
    nacos:
      config:
        server-addr: http://172.20.89.190:18848
        file-extension: yml
        group: DEFAULT_GROUP
        namespace: public
        username: nacos
        password: zhXdlNacos230
        prefix: ${spring.application.name}
        refresh-enabled: true
        shared-configs:
          - data-id: shared-redis.yml
            group: DEFAULT_GROUP
            refresh: true #动态刷新配置开启
          - data-id: shared-datasource.yml
            group: DEFAULT_GROUP
            refresh: true #动态刷新配置开启
      discovery:
        server-addr: http://172.20.89.190:18848
        group: DEFAULT_GROUP
        namespace: public
        username: nacos
        password: zhXdlNacos230