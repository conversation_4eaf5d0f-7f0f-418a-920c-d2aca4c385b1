package com.inventory_server.model.warehouse.service;

import com.inventory_server.model.warehouse.entity.dto.WarehouseDetailDto;
import com.inventory_server.model.warehouse.entity.dto.WarehouseSummaryDto;

import java.util.List;

public interface IFetchWarehouseFromECService {

    List<WarehouseSummaryDto> fetchWarehouseSummaryList();

    void asyncFetchAndSaveWarehouseDetailList(List<String> yiCangWarehouseCodeList);
}
