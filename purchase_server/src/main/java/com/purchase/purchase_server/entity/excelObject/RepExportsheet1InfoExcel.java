package com.purchase.purchase_server.entity.excelObject;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.alibaba.excel.enums.poi.HorizontalAlignmentEnum;
import com.alibaba.excel.enums.poi.VerticalAlignmentEnum;
import lombok.*;

import java.io.Serial;
import java.io.Serializable;

/**
 * @Description excel导出补货计划-建议采购量 转换成的实体列
 * <AUTHOR>
 * @Date 2024/1/19 17:00
 **/
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
//excel内容居中
@ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER, verticalAlignment = VerticalAlignmentEnum.CENTER)
//excel字体大小
//@ContentFontStyle(fontHeightInPoints = 15)
public class RepExportsheet1InfoExcel extends BaseExcel implements Serializable {

    @Serial
    private static final long serialVersionUID = -5286081041104863426L;
    @ExcelProperty("自定义SKU")
    private String selfSku;
    @ExcelProperty("供应商代码")
    private String factoryCode;
    @ExcelProperty("产品名称")
    private String productName;
    @ExcelProperty("渠道")
    private String channel;
    @ExcelProperty("虚拟sku")
    private String virtualSku;
    @ExcelProperty("采购人员")
    private String purchaser;
    @ExcelProperty("产品经理")
    private String buyer;
    @ExcelProperty("运营")
    private String operator;
    @ExcelProperty("子体类型")
    private String subType;
    @ExcelProperty("产品状态")
    private String productStatus;
    @ExcelProperty("产品类型")
    private String productType;
    @ExcelProperty("实际日销")
    private Double actualDailySalesNum;
    @ExcelProperty("目标日销")
    private Double targetDailySalesNum;
    @ExcelProperty("日销达成率")
    private String subEntityRate;
    @ExcelProperty("父体日销达成率")
    private String parentEntityRate;
    @ExcelProperty("全链路理论售罄时间")
    private String fullLinkTheoreticalSoldOutDate;
    @ExcelProperty("全链路售罄前断货天数")
    private Integer fullLinkDaysBeforeSoldOut;
    @ExcelProperty("建议采购日")
    private String advicePurchaseDate;
    @ExcelProperty("交货日")
    private String expectedFactoryFinishedDate;
    @ExcelProperty("预计到仓时间")
    private String expectedArrivingDate;
    @ExcelProperty("目标仓")
    private String destinationWarehouse;
    @ExcelProperty("理论补货量")
    private Integer advicePurchaseNum;
    @ExcelProperty("理论补货量汇总")
    private Integer advicePurchaseNumSum;
    @ExcelProperty("建议补货量")
    private Double actualReplenishmentNum;
    @ExcelProperty("建议补货量汇总")
    private Double actualReplenishmentNumSum;
    @ExcelProperty("建议原因")
    private String reason;
    @ExcelProperty("运营确认量")
    private Double operationConfirmedNum;
    @ExcelProperty("运营确认量汇总")
    private Double operationConfirmedNumSum;
    @ExcelProperty("理由")
    private String operationReason;
    @ExcelProperty("说明")
    private String operatorRemark;
    @ExcelProperty("最终下单数量")
    private String finalOrderQuantity;
    @ExcelProperty("原因")
    private String finalReason;
    @ExcelProperty("海外仓冗余详情")
    private String foreignRedundantDetails;
    @ExcelProperty("在途冗余详情")
    private String onShippingRedundantDetails;
    @ExcelProperty("本地仓冗余详情")
    private String productDetails;
    @ExcelProperty("补货结果备注")
    private String remarks;
}
