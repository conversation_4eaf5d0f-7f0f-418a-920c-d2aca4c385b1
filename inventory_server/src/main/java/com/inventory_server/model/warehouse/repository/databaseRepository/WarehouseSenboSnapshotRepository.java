package com.inventory_server.model.warehouse.repository.databaseRepository;

import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.inventory_server.model.warehouse.entity.dos.WarehouseSenboInfoDO;
import com.inventory_server.model.warehouse.entity.dos.WarehouseSenboInfoSnapshotDO;
import com.inventory_server.model.warehouse.entity.dto.SenboWarehouseDto;
import com.inventory_server.model.warehouse.mapper.WarehouseSenboInfoSnapshotMapper;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/5/13 15:33
 **/
@Service
public class WarehouseSenboSnapshotRepository extends ServiceImpl<WarehouseSenboInfoSnapshotMapper, WarehouseSenboInfoSnapshotDO> {

    @Resource
    private WarehouseSenboInfoRepositoryImpl warehouseSenboInfoRepository;

    public void insertWarehouseSenboSnapshot(WarehouseSenboInfoDO warehouseSenboInfoDO) {
        List<SenboWarehouseDto> senboWarehouseList = warehouseSenboInfoRepository.getSenboWarehouseList(new SenboWarehouseDto());
        List<WarehouseSenboInfoDO> list = senboWarehouseList.stream()
                .map(m -> (WarehouseSenboInfoDO) WarehouseSenboInfoDO.builder()
                        .id(String.valueOf(m.getSenboWarehouseId()))
                        .warehouse(m.getSenboWarehouse())
                        .headShippingDate(m.getHeadShippingDate())
                        .urgentHeadShipDate(m.getUrgentHeadShipDate())
                        .sort(m.getSort())
                        .remarks(m.getRemarks())
                        .build())
                .toList();
        var build = WarehouseSenboInfoSnapshotDO.builder().warehouseSenboInfo(JSON.toJSONString(list)).build();
        baseMapper.insert(build);
    }

    public WarehouseSenboInfoSnapshotDO getNewestWarehouseSenboInfo() {
        WarehouseSenboInfoSnapshotDO snapshot = baseMapper.selectOne(Wrappers.<WarehouseSenboInfoSnapshotDO>lambdaQuery()
                .orderByDesc(WarehouseSenboInfoSnapshotDO::getCreateDate)
                .last("limit 1")
        );
        return snapshot;
    }
}
