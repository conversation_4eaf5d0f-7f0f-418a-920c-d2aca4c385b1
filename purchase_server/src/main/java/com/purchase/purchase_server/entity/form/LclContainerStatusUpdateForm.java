package com.purchase.purchase_server.entity.form;

import jakarta.validation.constraints.NotEmpty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * @Description 拼柜信息状态更新表单
 * <AUTHOR>
 * @Date 2025/6/10
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class LclContainerStatusUpdateForm implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 拼柜信息ID列表（支持批量更新）
     */
    @NotEmpty(message = "拼柜信息ID不能为空")
    private List<String> containerIds;

    /**
     * 货件状态
     * @see com.purchase.purchase_server.enums.lcl.ShipmentStatusEnum
     */
    private Integer shipmentStatus;

    /**
     * 装载状态
     * @see com.purchase.purchase_server.enums.shipment.LoadingStatusEnum
     */
    private Integer loadingStatus;

    /**
     * 运输方式
     * @see com.purchase.purchase_server.enums.shipment.TransportationModeEnum
     */
    private Integer transportationMode;
}
