package com.purchase.purchase_server.entity.vo.Lcl;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 装柜列表vo
 *
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class LclConsolidationTrialNumVO implements Serializable {


    @Serial
    private static final long serialVersionUID = 4827643424681442438L;

    private String id;

    /**
     * 发货数量
     */
    private Integer shippingNum;

    /**
     * 发货装柜时间
     */
    private String shippingStartDate;

    /**
     * 单仓体积(箱)
     */
    private BigDecimal volume;

    /**
     * 仓库
     */
    private String warehouse;

    /**
     * 是否变动（0-未变动，1-变动）
     */
    private String isChange;

}