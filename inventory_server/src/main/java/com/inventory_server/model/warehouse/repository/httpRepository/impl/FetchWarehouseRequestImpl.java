package com.inventory_server.model.warehouse.repository.httpRepository.impl;

import com.alibaba.fastjson2.JSON;
import com.crafts_mirror.utils.common.entity.HttpRequestDetail;
import com.crafts_mirror.utils.listener.HttpResponseListener;
import com.crafts_mirror.utils.utils.HttpClientPool;
import com.google.common.util.concurrent.RateLimiter;
import com.inventory_server.model.warehouse.entity.dos.YCResponseEntity;
import com.inventory_server.model.warehouse.entity.dto.WarehouseSummaryDto;
import com.inventory_server.model.warehouse.entity.dto.WarehouseSummaryBizContent;
import com.inventory_server.model.warehouse.repository.httpRepository.FetchWarehouseHttpRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * @Description 从易仓获取仓库信息远程链接层
 * <AUTHOR>
 * @Date 2024/6/28 14:08
 **/
@Service
@Slf4j
public class FetchWarehouseRequestImpl implements FetchWarehouseHttpRepository {
    @Override
    public List<WarehouseSummaryDto> fetchAllWarehouseSummaryList(HttpRequestDetail<YCResponseEntity> requestDetail) {
        HttpClientPool<YCResponseEntity> httpClientPool = new HttpClientPool<>(Collections.singletonList(requestDetail));
        YCResponseEntity entity = httpClientPool.sendRequest(requestDetail);
        if (!Objects.equals(entity.getCode(), "200")) {
            log.error("从易仓处获取仓库详情异常：{}", entity.getMessage());
            throw new RuntimeException("从易仓处获取仓库详情异常" + entity.getMessage());
        }
        String bizContent = entity.getBizContent();
        WarehouseSummaryBizContent warehouseSummaryBizContent = JSON.parseObject(bizContent, WarehouseSummaryBizContent.class);
        return warehouseSummaryBizContent.getData();
    }

    @Override
    public void fetchWarehouseDetailList(List<HttpRequestDetail<YCResponseEntity>> requestDetailList,
                                         HttpResponseListener<YCResponseEntity> httpResponseListener) {
        startAsyncRequests(requestDetailList, httpResponseListener);
    }

    private void startAsyncRequests(List<HttpRequestDetail<YCResponseEntity>> requestDetailList,
                                    HttpResponseListener<YCResponseEntity> httpResponseListener) {
        HttpClientPool<YCResponseEntity> httpClientPool = new HttpClientPool<>(requestDetailList, httpResponseListener, RateLimiter.create(1000));
        try {
            httpClientPool.startAsyncRequests();
        } finally {
            Runtime.getRuntime().addShutdownHook(new Thread(httpClientPool::shutdown));
        }
    }
}
