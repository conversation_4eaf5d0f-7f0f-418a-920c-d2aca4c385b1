package com.purchase.purchase_server.entity.vo;

import com.purchase.purchase_server.entity.dto.ForeignInventoryDto;
import com.purchase.purchase_server.entity.dto.RepProjectBaseParamDto;
import com.purchase.purchase_server.entity.dto.delivery.ShippingDetailPlainDto;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;
import java.util.TreeMap;

/**
 * @Description 补货列表详情页导入数据信息
 * <AUTHOR>
 * @Date 2024/1/19 11:55
 **/
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class ReplenishmentInfoDetailVo implements Serializable {

    @Serial
    private static final long serialVersionUID = -1279437133302470699L;
    /**
     * 虚拟sku
     */
    private String virtualSku;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 基本参数
     */
    private RepProjectBaseParamDto baseParamDto;

    /**
     * 目标日销
     */
    private TreeMap<String, Double> destinationEverydaySale;

    /**
     * 海外仓库存
     */
    private List<ForeignInventoryDto> enableUseInventoryList;

    /**
     * 海外仓在途
     */
    private List<ForeignInventoryDto> onShippingInventoryList;

    private List<ShippingDetailPlainDto> shippingDetailPlainDtoList;

    /**
     * 发货计划创建时间
     */
    private String projectCreateDate;

    /**
     * 是否可编辑
     */
    private String replenishmentStatus;

    /**
     * 仓库顺序数组
     */
    private List<String> warehouseSortList;

}
