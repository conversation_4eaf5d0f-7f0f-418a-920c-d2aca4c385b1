package com.purchase.purchase_server.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.yulichang.base.MPJBaseMapper;
import com.purchase.purchase_server.entity.bo.RepExcelProjectSkuBO;
import com.purchase.purchase_server.entity.bo.RepProjectSkuBO;
import com.purchase.purchase_server.entity.dataObject.ReplenishmentProjectDO;
import com.purchase.purchase_server.entity.form.AdvicePurchaseForm;
import com.purchase.purchase_server.entity.form.ReplenishmentProjectForm;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【cm_replenishment_project】的数据库操作Mapper
 * @createDate 2024-01-17 10:45:51
 * @Entity generator.domain.CmReplenishmentProject
 */
public interface ReplenishmentProjectMapper extends MPJBaseMapper<ReplenishmentProjectDO> {
    int deleteByIds(List<String> ids);
}




