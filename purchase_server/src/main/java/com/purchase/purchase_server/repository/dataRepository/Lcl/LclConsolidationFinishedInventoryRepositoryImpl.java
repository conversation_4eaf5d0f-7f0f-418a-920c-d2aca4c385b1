package com.purchase.purchase_server.repository.dataRepository.Lcl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.purchase.purchase_server.entity.bo.Lcl.LclContainerLoadingBO;
import com.purchase.purchase_server.entity.bo.Lcl.LclFinishedInventoryBO;
import com.purchase.purchase_server.entity.dataObject.Lcl.LclConsolidationFinishedInventoryDO;
import com.purchase.purchase_server.entity.dataObject.Lcl.LclConsolidationTrialShippingInventoryDO;
import com.purchase.purchase_server.entity.dataObject.Lcl.LclFinishedInventoryDO;
import com.purchase.purchase_server.entity.dataObject.ProductSnapshotDO;
import com.purchase.purchase_server.entity.dto.Lcl.LclConsolidationNonFinishedInventoryDTO;
import com.purchase.purchase_server.entity.form.InteriorInfoQuery;
import com.purchase.purchase_server.entity.form.LclConsolidationForm;
import com.purchase.purchase_server.entity.form.LclConsolidationRecordForm;
import com.purchase.purchase_server.entity.vo.Lcl.LclConsolidationFinishedInventoryVO;
import com.purchase.purchase_server.mapper.Lcl.LclConsolidationFinishedInventoryMapper;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/12/9
 **/
@Service
public class LclConsolidationFinishedInventoryRepositoryImpl extends ServiceImpl<LclConsolidationFinishedInventoryMapper, LclConsolidationFinishedInventoryDO> {


    public List<LclConsolidationFinishedInventoryVO> pageList(LclConsolidationRecordForm form) {
        assert form.getOldSkuList() != null;
        return this.baseMapper.selectJoinList(LclConsolidationFinishedInventoryVO.class, new MPJLambdaWrapper<LclConsolidationFinishedInventoryDO>()
                        .select(LclConsolidationFinishedInventoryDO::getId)
                        .selectAs(LclConsolidationTrialShippingInventoryDO::getId, "lclConsolidationTrialId")
                        .select("t2.factory_data->>'$.factoryCode' as factory_code")
                        .select("t2.factory_data->>'$.addressCode' as address_code")
                        .select(LclConsolidationFinishedInventoryDO::getContractCode)
                        .select(LclConsolidationFinishedInventoryDO::getShipmentCode)
                        .select(LclConsolidationFinishedInventoryDO::getDeliveryType)

                        .select("t2.self_data->>'$.image' as image")
                        .select("t2.self_data->>'$.productName' as product_name")
                        .select("t2.self_data->>'$.sku' as self_sku")
                        .select("t2.self_data->>'$.commodityInspection' as commodity_inspection")
                        .select("t2.self_data->>'$.pcsType' as pcs_type")
                        .select("t2.self_data->>'$.categoryId' as category")

                        .select(ProductSnapshotDO::getSelfSkuId)
                        .select("t2.self_data->>'$.caseLength' as case_length")
                        .select("t2.self_data->>'$.caseWidth' as case_width")
                        .select("t2.self_data->>'$.caseHeight' as case_height")
                        .select("t2.self_data->>'$.singleCaseGrossWeight' as single_case_gross_weight")
                        .select("CASE t.is_old_status WHEN '1' THEN t2.virtual_data->>'$.oldSku' WHEN '0' THEN t2.virtual_sku END as destination_sku")
                        .select("t2.virtual_data->>'$.channel' as channel")
                        .select("t2.virtual_data->>'$.productStatus' as product_status")

                        .select("t2.self_data->>'$.buyer' as buyer")

                        .select(LclConsolidationTrialShippingInventoryDO::getShippingStartDate)
                        .select(LclConsolidationTrialShippingInventoryDO::getIsPackageFull)
                        .select(LclConsolidationFinishedInventoryDO::getRemarks)
                        .select(LclConsolidationFinishedInventoryDO::getFactoryFinishedDate)
                        .select(LclConsolidationFinishedInventoryDO::getFactoryRemainNum)
                        .select(LclConsolidationFinishedInventoryDO::getFactoryShippingPackageNum)
                        .select(LclConsolidationFinishedInventoryDO::getOriginalShippingNum)
                        .select(LclConsolidationFinishedInventoryDO::getIsForeignFlag)
                        .select("t2.self_data->>'$.containerLoad' as container_load")
                        .select(LclConsolidationTrialShippingInventoryDO::getDestinationWarehouse)
                        .select(LclConsolidationTrialShippingInventoryDO::getShippingNum)
                        .select(LclConsolidationTrialShippingInventoryDO::getLclShippingNum)
                        .select(LclConsolidationTrialShippingInventoryDO::getIsChange)
                        .select(LclConsolidationTrialShippingInventoryDO::getIsPackageFull)
                        .leftJoin(LclConsolidationTrialShippingInventoryDO.class, LclConsolidationTrialShippingInventoryDO::getConsolidationFactoryFinishedId, LclConsolidationFinishedInventoryDO::getId)
                        .leftJoin(ProductSnapshotDO.class, ProductSnapshotDO::getId, LclConsolidationFinishedInventoryDO::getProductSnapshotId)
                        .eq(LclConsolidationFinishedInventoryDO::getLclRecordId, form.getLclRecordId())
                        .like(StrUtil.isNotBlank(form.getContractCode()), LclConsolidationFinishedInventoryDO::getContractCode, form.getContractCode())
                        .like(StrUtil.isNotBlank(form.getShipmentCode()), LclConsolidationFinishedInventoryDO::getShipmentCode, form.getShipmentCode())
                        .in(CollectionUtil.isNotEmpty(form.getSelfSkuList()), ProductSnapshotDO::getSelfSku, form.getSelfSkuList())
                        .in(CollectionUtil.isNotEmpty(form.getVirtualSkuList()), ProductSnapshotDO::getVirtualSku, form.getVirtualSkuList())
                        .between(StrUtil.isNotBlank(form.getShippingStartDate()) && StrUtil.isNotBlank(form.getShippingEndDate()),
                                LclConsolidationTrialShippingInventoryDO::getShippingStartDate, form.getShippingStartDate(), form.getShippingEndDate())
                        .between(StrUtil.isNotBlank(form.getFactoryFinishedStartDate()) && StrUtil.isNotBlank(form.getFactoryFinishedEndDate()),
                                LclConsolidationFinishedInventoryDO::getFactoryFinishedDate, form.getFactoryFinishedStartDate(), form.getFactoryFinishedEndDate())
                        .apply(StrUtil.isNotBlank(form.getProductName()), "t2.self_data->>'$.productName' like {0}", StrUtil.isBlank(form.getProductName()) ? form.getProductName() : "%" + form.getProductName().strip() + "%")
                        .apply(StrUtil.isNotBlank(form.getFactoryCode()), "t2.factory_data->>'$.factoryCode' like {0}", StrUtil.isBlank(form.getFactoryCode()) ? form.getFactoryCode() : "%" + form.getFactoryCode().strip() + "%")
                        .apply(CollectionUtil.isNotEmpty(form.getProductStatusList()), "t2.virtual_data->>'$.productStatus' in (" + String.join(",",
                                form.getProductStatusList().stream()
                                        .map(s -> "'" + s + "'")
                                        .toList()) + ")")
                        .apply(CollectionUtil.isNotEmpty(form.getOldSkuList()), "t2.virtual_data->>'$.oldSku' in (" + String.join(",",
                                Optional.ofNullable(form.getOldSkuList()).orElse(new ArrayList<>()).stream()
                                        .map(s -> "'" + s + "'")
                                        .toList()) + ")")
                        .apply(StrUtil.isNotBlank(form.getAddressCode()), "t2.factory_data->>'$.addressCode' like {0}", StrUtil.isBlank(form.getAddressCode()) ? form.getAddressCode() : "%" + form.getAddressCode().strip() + "%")
                //.inSql(StrUtil.isNotBlank(form.getIsPackageFull()) && form.getIsPackageFull().equals(IsPackageFullEnum.FULL.getCode()) , LclConsolidationFinishedInventoryDO::getId,
                //        "SELECT t3.consolidation_factory_finished_id" +
                //                "    FROM cm_lcl_consolidation_trial_shipping_inventory t3" +
                //                "    GROUP BY t3.consolidation_factory_finished_id" +
                //                "    HAVING MIN(t3.is_package_full) = '" + form.getIsPackageFull() + "' AND MAX(t3.is_package_full) = '" + form.getIsPackageFull() + "'")
                //.inSql(StrUtil.isNotBlank(form.getIsPackageFull()) && form.getIsPackageFull().equals(IsPackageFullEnum.NOT_FULL.getCode()), LclConsolidationFinishedInventoryDO::getId,
                //        "SELECT t3.consolidation_factory_finished_id" +
                //                "    FROM cm_lcl_consolidation_trial_shipping_inventory t3" +
                //                "    GROUP BY t3.consolidation_factory_finished_id" +
                //                "    HAVING MIN(t3.is_package_full) = '" + form.getIsPackageFull() + "'")
                //
                //
                //.exists(StrUtil.isNotBlank(form.getIsChange()),
                //        " SELECT 1 " +
                //                "    FROM cm_lcl_consolidation_trial_shipping_inventory t4 " +
                //                "    WHERE t4.consolidation_factory_finished_id = t.id " +
                //                "    AND t4.is_change = '" + form.getIsChange() + "'")
        );
    }

    public LclConsolidationFinishedInventoryVO selectContainerLoad(String finishedId) {

        return baseMapper.selectJoinOne(LclConsolidationFinishedInventoryVO.class, new MPJLambdaWrapper<LclConsolidationFinishedInventoryDO>()
                .select(LclConsolidationFinishedInventoryDO::getId)
                .select("t1.self_data->>'$.containerLoad' as container_load")
                .select(LclConsolidationFinishedInventoryDO::getFactoryRemainNum)
                .leftJoin(ProductSnapshotDO.class, ProductSnapshotDO::getId, LclConsolidationFinishedInventoryDO::getProductSnapshotId)
                .eq(LclConsolidationFinishedInventoryDO::getId, finishedId)
        );
    }

    public int delete(String factoryFinishedId) {
        return baseMapper.delete(factoryFinishedId);
    }

    public int deleteByLclRecordId(String lclRecordId) {
        return baseMapper.deleteByLclRecordId(lclRecordId);
    }

    public Set<String> getSnapIds(InteriorInfoQuery infoQuery) {
        List<LclConsolidationFinishedInventoryDO> list = baseMapper.selectList(Wrappers.<LclConsolidationFinishedInventoryDO>lambdaQuery()
                .select(LclConsolidationFinishedInventoryDO::getProductSnapshotId)
                .between(StrUtil.isNotBlank(infoQuery.getCreateStartDate()) && StrUtil.isNotBlank(infoQuery.getCreateEndDate()),
                        LclConsolidationFinishedInventoryDO::getCreateDate, infoQuery.getCreateStartDate(), infoQuery.getCreateEndDate()));
        return CollectionUtil.isEmpty(list) ? Collections.emptySet() :
                list.stream()
                        .map(LclConsolidationFinishedInventoryDO::getProductSnapshotId)
                        .collect(Collectors.toSet());
    }

    public List<LclFinishedInventoryBO> selectListById(LclConsolidationForm form) {
        return this.baseMapper.selectJoinList(LclFinishedInventoryBO.class, new MPJLambdaWrapper<LclConsolidationFinishedInventoryDO>()
                .selectAll(LclConsolidationFinishedInventoryDO.class)
                .selectCollection(LclConsolidationTrialShippingInventoryDO.class, LclFinishedInventoryBO::getLclTrialDTOList)
                .leftJoin(ProductSnapshotDO.class, ProductSnapshotDO::getId, LclConsolidationFinishedInventoryDO::getProductSnapshotId)
                .leftJoin(LclConsolidationTrialShippingInventoryDO.class, LclConsolidationTrialShippingInventoryDO::getConsolidationFactoryFinishedId, LclConsolidationFinishedInventoryDO::getId)
                .eq(LclConsolidationFinishedInventoryDO::getId, form.getId())
        );
    }

    public List<LclContainerLoadingBO> listWithTrial(LclConsolidationForm form) {

        return baseMapper.listWithTrial(form.getLclRecordId(), form.getShippingStartDate());
    }

    public List<LclConsolidationNonFinishedInventoryDTO> listByLclRecordId(String lclRecordId) {
        return baseMapper.selectJoinList(LclConsolidationNonFinishedInventoryDTO.class, new MPJLambdaWrapper<LclConsolidationFinishedInventoryDO>()
                .selectAll(LclConsolidationFinishedInventoryDO.class)
                .select("t1.self_data->>'$.productName' as product_name")
                .select("t1.self_data->>'$.sku' as self_sku")
                .select("t1.self_data->>'$.containerLoad' as container_load")
                .select("t1.self_data->>'$.caseLength' as case_length")
                .select("t1.self_data->>'$.caseWidth' as case_width")
                .select("t1.self_data->>'$.caseHeight' as case_height")
                .select("t1.self_data->>'$.singleCaseGrossWeight' as single_case_gross_weight")
                .select("t1.self_data->>'$.commodityInspection' as commodity_inspection")
                .select("t1.self_data->>'$.buyer' as buyer")
                .select("CASE t1.self_data->>'$.taxes' WHEN '1' THEN t1.self_data->>'$.priceWithTaxes' WHEN '0' THEN t1.self_data->>'$.price' END as unit_or_contract_price")
                .select("t1.self_data->>'$.pcsType' as pcs_type")

                .select("t1.virtual_data->>'$.channel' as channel")
                .select("t1.virtual_data->>'$.operator' as operator")
                .select("CASE t.is_old_status WHEN '1' THEN t1.virtual_data->>'$.oldSku' WHEN '0' THEN t1.virtual_sku END as destination_sku")

                .select("t1.factory_data->>'$.addressCode' as address_code")
                .select("t1.factory_data->>'$.purchaser' as purchaser")
                .select("t1.factory_data->>'$.orderTracker' as order_tracker")
                .select("t1.factory_data->>'$.currency' as currency")

                .leftJoin(ProductSnapshotDO.class, ProductSnapshotDO::getId, LclConsolidationFinishedInventoryDO::getProductSnapshotId)
                .isNotNull(LclConsolidationFinishedInventoryDO::getLclForeignId)
                .eq(LclFinishedInventoryDO::getLclRecordId, lclRecordId)
        );
    }
}
