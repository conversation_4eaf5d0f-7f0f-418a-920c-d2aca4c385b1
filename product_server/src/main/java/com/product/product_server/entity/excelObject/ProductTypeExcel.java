package com.product.product_server.entity.excelObject;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.*;

import java.io.Serial;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2024/8/8 11:17
 **/
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProductTypeExcel extends BaseExcel implements Serializable {
    @Serial
    private static final long serialVersionUID = -14395325840202434L;

    @ExcelProperty("* 虚拟SKU")
    private String virtualSku;

    @ExcelProperty("* 产品类型")
    private String productType;
}
