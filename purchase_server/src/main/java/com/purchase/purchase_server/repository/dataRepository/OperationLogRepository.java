package com.purchase.purchase_server.repository.dataRepository;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.purchase.purchase_server.entity.dataObject.OperationLogDO;
import com.purchase.purchase_server.mapper.IOperationMapper;
import org.springframework.stereotype.Service;

/**
 * @Description 日志持久操作层
 * <AUTHOR>
 * @Date 2024/1/26 14:36
 **/
@Service
public class OperationLogRepository extends ServiceImpl<IOperationMapper, OperationLogDO> {

}
