package com.purchase.purchase_server.repository.dataRepository;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.purchase.purchase_server.entity.dataObject.ReplenishmentMockInventoryDO;
import com.purchase.purchase_server.mapper.ReplenishmentMockInventoryMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * @Description 补货试算表格
 * <AUTHOR>
 * @Date 2024/1/22 16:42
 **/
@Service
@Slf4j
public class ReplenishmentMockInventoryRepositoryImpl extends ServiceImpl<ReplenishmentMockInventoryMapper, ReplenishmentMockInventoryDO> {

    public ReplenishmentMockInventoryDO getMockInventoryDetail(String shippingProjectId) {
        return baseMapper.selectOne(Wrappers.<ReplenishmentMockInventoryDO>lambdaQuery()
                .eq(ReplenishmentMockInventoryDO::getVirtualSkuPurchaseInventoryId, shippingProjectId));
    }

    public IPage<ReplenishmentMockInventoryDO> getAllMockInventory(IPage<ReplenishmentMockInventoryDO> page) {
        return baseMapper.selectPage(page, Wrappers.lambdaQuery());
    }
}
