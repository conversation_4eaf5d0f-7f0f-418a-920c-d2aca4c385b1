package com.purchase.purchase_server.enums.shipment;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Optional;
import java.util.function.Predicate;

@Getter
@AllArgsConstructor
public enum LoadingStatusEnum {

    UNDER_LOAD(0, "未装满"),
    WHOLE_CONTAINER(1, "整柜"),
    BULK_PARTS(2, "散件"),
    NULL_STATUS(9, ""),// 无装载状态
    ;

    private final Integer code;
    private final String desc;

    public static Optional<LoadingStatusEnum> ofCode(Integer code) {
        if (code == null){
            return Optional.empty();
        }
        Predicate<LoadingStatusEnum> predicate = it -> it.getCode().equals(code);
        return ofCode(predicate);
    }

    private static Optional<LoadingStatusEnum> ofCode(Predicate<LoadingStatusEnum> predicate) {
        return Arrays.stream(LoadingStatusEnum.values())
                .filter(predicate)
                .findFirst();
    }
}
