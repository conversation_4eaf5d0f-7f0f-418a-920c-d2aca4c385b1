package com.purchase.purchase_server.utils.easyExcelUtil.listener.Deliver;


import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.metadata.data.ReadCellData;
import com.alibaba.excel.util.ConverterUtils;
import com.purchase.purchase_server.entity.dataObject.VirtualProductDO;
import com.purchase.purchase_server.entity.dto.SenboWarehouseDto;
import com.purchase.purchase_server.entity.vo.UpgradeInfoInteriorVo;
import com.purchase.purchase_server.model.purchase.TargetSalesInfoDp;
import com.purchase.purchase_server.utils.easyExcelUtil.manager.DeliveryManager;
import com.purchase.purchase_server.utils.easyExcelUtil.manager.DeliveryMapManager;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.*;
import java.util.stream.IntStream;

import static com.crafts_mirror.utils.constant.DateFormatConstant.YYYY_MM_DD_DATE_FORMAT_SLASH;
import static com.crafts_mirror.utils.constant.DateFormatConstant.YYYY_M_D_DATE_FORMAT_SLASH;

/**
 * @Description 导入发货计划-目标日销
 * <AUTHOR>
 * @Date 2023/12/28 15:49
 **/
@Slf4j
public class TargetSalesImportListener extends AbstractPurchaseImportListener<HashMap<Integer, String>> {

    public TargetSalesImportListener(List<SenboWarehouseDto> senboWarehouseList) {
        super(senboWarehouseList);
    }

    @Override
    public void invoke(HashMap<Integer, String> data, AnalysisContext analysisContext) {
//        log.warn("导入发货计划-目标日销-------------导入excel进行中，成功解析excel");
        Integer approximateRowNumber = analysisContext.readSheetHolder().getApproximateTotalRowNumber();
        if (approximateRowNumber != null && approximateRowNumber > TOTAL_ROW_NUMBER) {
            throw new RuntimeException(String.format("导入数据超过%d行，请缩小导入数据量", TOTAL_ROW_NUMBER));
        }

        DeliveryMapManager deliveryMapManager = DeliveryMapManager.getInstance();

        //动态读取 excel逻辑删除造成数据null问题
        ArrayList<String> targetSalesHeadList = deliveryMapManager.getTargetSalesHeadList();
        data.entrySet().removeIf(entry -> entry.getKey() > targetSalesHeadList.size());
        String sku = data.get(0);
        VirtualProductDO virtualProductDO = deliveryMapManager.getSkuVirtualProductMap().get(sku);
        if (virtualProductDO == null){
            virtualProductDO = deliveryMapManager.getOldSkuVirtualProductMap().get(sku);
            if (virtualProductDO != null && virtualProductDO.getOldSku().equals(sku)){
                throw new IllegalArgumentException("不可以填写老sku");
            }
            throw new IllegalArgumentException("虚拟sku不存在");
        }

        if (StrUtil.isNotBlank(virtualProductDO.getUpgradeId())) {
            UpgradeInfoInteriorVo upgradeInfoInteriorVo = selectUpgradeInfo(virtualProductDO.getUpgradeId());
            if (ObjectUtil.isNotEmpty(upgradeInfoInteriorVo)) {
                if (upgradeInfoInteriorVo.getOriginalSkuId().equals(virtualProductDO.getId())) {
                    deliveryMapManager.putTargetSalesUpgradeMap(upgradeInfoInteriorVo.getOriginalSku(), upgradeInfoInteriorVo.getUpgradeSku());
                    return;
                }
            }
        }
        new TargetSalesInfoDp(data);

        if (!deliveryMapManager.addTargetSalesSet(sku)) {
            throw new IllegalArgumentException("虚拟sku重复");
        }
        //数据组装
        HashMap<String, Double> sumDaysMap = IntStream.range(0, targetSalesHeadList.size())
                .boxed()
                .collect(HashMap::new,
                        (map, i) -> map.put(targetSalesHeadList.get(i), Double.parseDouble(data.getOrDefault((i + 1), "0.0"))),
                        HashMap::putAll);

        deliveryMapManager.putTargetSalesMap(sku, sumDaysMap);

        DeliveryManager manager = DeliveryManager.getInstance();
        manager.incrementSuccessfulTimes();
    }

    @Override
    public void invokeHead(Map<Integer, ReadCellData<?>> headMap, AnalysisContext context) {
        DeliveryMapManager deliveryMapManager = DeliveryMapManager.getInstance();
        Map<Integer, String> map = ConverterUtils.convertToStringMap(headMap, context);
        //删除map中的空值
        map.values().removeIf(Objects::isNull);
        for (Map.Entry<Integer, String> entry : map.entrySet()) {

            if (!"*虚拟SKU".equals(entry.getValue())) {
                if (entry.getValue() != null) {
                    deliveryMapManager.addTargetSalesHeadList(getTime(entry.getValue()));
                } else {
                    throw new RuntimeException("目标日销表头错误，请检查表头是否正确");
                }
            }
        }
    }

    /**
     * 将日期数字转为时间格式
     * daysDuration = 44745
     *
     * @param daysDuration String
     * @return String
     */
    public static String getTime(String daysDuration) {
        DateTimeFormatter inputFormatter = DateTimeFormatter.ofPattern(YYYY_M_D_DATE_FORMAT_SLASH);
        DateTimeFormatter outputFormatter = DateTimeFormatter.ofPattern(YYYY_MM_DD_DATE_FORMAT_SLASH);
        try {
            // 尝试解析日期字符串
            LocalDate date = LocalDate.parse(daysDuration, inputFormatter);
            // 将日期格式化为所需的格式
            return date.format(outputFormatter);
        } catch (DateTimeParseException e) {
            throw new RuntimeException("目标日销表头错误，请检查表头是否正确");
        }
    }
}
