package com.crafts_mirror.utils.constant;

/**
 * @Description 项目所用的基础常量类
 * <AUTHOR>
 * @Date 2023/12/26 11:45
 **/
public class SystemConstant {
    /*****************************************************************************************
     **************************************** 文档中心 ****************************************
     *****************************************************************************************/
    public static final String FILE_SYSTEM_MISSION_CENTER_URL = "http://file-system/center/interior/mission";

    public static final String FILE_SYSTEM_DELETE_MISSION_CENTER_URL = "http://file-system/center/interior/delete";

    public static final String FILE_SYSTEM_EXIST_MISSION_CENTER_URL = "http://file-system/center/interior/doesObjectExist";

    public static final String FILE_SYSTEM_PUT_OBJECT_BY_URL_URL = "http://file-system/center/interior/putObject/url";

    public static final String FILE_SYSTEM_PUT_OBJECT_BY_FILE = "http://file-system/center/interior/putObject/file";

    public static final String FILE_SYSTEM_MISSION_CENTER_MISSION_GET_URL = "http://file-system/center/interior/getUrl";

    public static final String FILE_SYSTEM_MISSION_CENTER_MISSION_PAGE_LIST = "http://file-system/center/interior/pageList";

    public static final String FILE_SYSTEM_MISSION_CENTER_MISSION_LIST = "http://file-system/center/interior/list";

    /*****************************************************************************************
     **************************************** 产品中心 ****************************************
     *****************************************************************************************/
    public static final String SNAPSHOT_PRODUCTS_INFO_URL = "http://product-server/interior/snapshot/getList";

    public static final String SNAPSHOT_SAVE_PRODUCTS_INSERT_URL = "http://product-server/interior/snapshot/insert/saveProductSnapshotList";

    public static final String SNAPSHOT_SAVE_PRODUCTS_INSERT_FACTORY_URL = "http://product-server/interior/snapshot/insert/saveProductSnapshotByFactory";

    public static final String VIRTUAL_PRODUCTS_SKU_URL_LIST = "http://product-server/interior/virtualProduct/getList";

    public static final String VIRTUAL_PRODUCTS_SKU_INFO_LIST_URL = "http://product-server/interior/virtualProduct/list/info";

    public static final String PRODUCTS_SHIPPING_RATIO_URL = "http://product-server/interior/shippingRatio/";

    public static final String PRODUCTS_SELF_CATEGORY_URL = "http://product-server/interior/selfProduct/category";

    public static final String PRODUCTS_SELF_CATEGORY_LEAF_TREE_URL = "http://product-server/interior/selfProduct/category/leafTree";

    public static final String PRODUCTS_VIRTUAL_INFO_URL = "http://product-server/interior/virtualProduct/selectListByForm";

    public static final String PRODUCTS_VIRTUAL_UPGRADE_INFO_URL = "http://product-server/interior/virtualProduct/selectUpgradeInfo";

    public static final String NEWEST_EXCHANGE_RATE_URL = "http://product-server/interior/currency/latest/rate/usd";

    /*****************************************************************************************
     **************************************** 权限中心 ****************************************
     *****************************************************************************************/
    public static final String SYS_USER_DATA_PERMISSION = "http://authorization-server/interior/user/dataPermissionUser/";

    public static final String SYS_USER_LIST_URL = "http://authorization-server/interior/user/list";

    public static final String SYS_USER_INFO = "http://authorization-server/interior/user/info";

    public static final String SYS_MENU_TITLE_LIST_URL = "http://authorization-server/interior/menu/title/info";

    /*****************************************************************************************
     **************************************** 文件中心 ****************************************
     *****************************************************************************************/
    public static final String OPERATION_LOGS_SYSTEM_SAVE_BATCH_LOG_URL = "http://operation-logs/interior/operation/saveBatch";

    public static final String OPERATION_LOGS_SYSTEM_SAVE_LOG_URL = "http://operation-logs/interior/operation/save";

    public static final String OPERATION_LOGS_SYSTEM_DELETE_LOG_URL = "http://operation-logs/interior/operation/delete";

    public static final String VISIT_LOGS_SYSTEM_LIST_LOG_URL = "http://operation-logs/interior/operation/list";

    public static final String VISIT_LOGS_SYSTEM_SAVE_LOG_URL = "http://operation-logs/interior/visitLog/save";

    /*****************************************************************************************
     **************************************** 库存中心 ****************************************
     *****************************************************************************************/
    public static final String INVENTORY_SYSTEM_FOREIGN_AND_FACTORY = "http://inventory-server/interior/redundant/foreignAndFactoryList";

    public static final String INVENTORY_SYSTEM_HAS_REDUNDANCY_VIRTUAL_SKU_ID = "http://inventory-server/interior/redundant/hasRedundancy";

    public static final String INVENTORY_SYSTEM_HAS_REDUNDANCY_VIRTUAL_SKU_ID_LIST = "http://inventory-server/interior/redundant/hasRedundancyList";

    public static final String INVENTORY_SYSTEM_SELECT_SNAP_IDS = "http://inventory-server/interior/redundant/selectSnapIds";

    public static final String SEN_BO_WAREHOUSE_INFO_GET_URL = "http://inventory-server/interior/warehouse/senbo/warehouse";

    public static final String INVENTORY_SYSTEM_SELECT_CAL_THEORETICAL = "http://inventory-server/interior/redundant/calTheoreticalSoldOutDate";

    public static final String INVENTORY_SYSTEM_SELECT_CAL_ALL_THEORETICAL = "http://inventory-server/interior/redundant/calAllVirtualSkuRealSoldOutDate";

    public static final String SEN_BO_WAREHOUSE_WITHOUT_MT_INFO_GET_URL = "http://inventory-server/interior/warehouse/senbo/warehouseWithoutMiddleTransit";

    /*****************************************************************************************
     **************************************** 备货中心 ****************************************
     *****************************************************************************************/
    public static final String DELIVERY_CALCULATION_URL = "http://purchase-server/interior/delivery/calculation";

    public static final String DELIVERY_NORMAL_CALCULATION_URL = "http://purchase-server/interior/delivery/normal/calculation";

    public static final String DELIVERY_CALCULATE_MOCK_TABLE_URL = "http://purchase-server/interior/delivery/calculate/mockTable";

    public static final String DELIVERY_NORMAL_DELIVERY_INVENTORY_SAVE_TABLE_URL = "http://purchase-server/interior/delivery/save/deliveryInventory";

    public static final String DELIVERY_NORMAL_DELIVERY_INVENTORY_FETCH_TABLE_URL = "http://purchase-server/interior/delivery/get/deliveryInventory";

    public static final String PURCHASE_SYSTEM_DELIVERY_SELECT_SNAP_IDS = "http://purchase-server/interior/delivery/selectSnapIds";

    public static final String PURCHASE_SYSTEM_REPLENISHMENT_SELECT_SNAP_IDS = "http://purchase-server/interior/replenishment/selectSnapIds";

    public static final String PURCHASE_SYSTEM_LCL_CONSOLIDATION_SELECT_SNAP_IDS = "http://purchase-server/interior/lclConsolidation/selectSnapIds";
    /*****************************************************************************************
     **************************************** 销售管理 ****************************************
     *****************************************************************************************/
    public static final String EDIT_DAY_TARGET_SALES = "http://sales-server/interior/targetSales/edit/daySales";

    public static final String PREPARE_VIRTUAL_SKU_TARGET_SALES = "http://sales-server/interior/targetSales/cal/prepare";

    public static final String DELETE_TARGET_SALES = "http://sales-server/interior/targetSales/delete";

    public static final String ALL_NICHE_CHANNEL_URL = "http://sales-server/interior/channel/list/niche";



}
