package com.purchase.purchase_server.entity.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.io.Serial;
import java.io.Serializable;

/**
 * 产品分类
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class ProductCategoryDTO implements Serializable {

    @Serial
    private static final long serialVersionUID = -1L;

    private Integer id;

    /**
     * 品类名称
     */
    private String categoryName;

    /**
     * 父级id
     */
    private Integer parentId;

    /**
     * 祖籍列表
     */
    private String ancestors;

    /**
     * '是否为叶子节点：0-否，1-是'
     */
    private String isLeaf;

    /**
     * 排序
     */
    private Integer categorySort;

}