package com.purchase.purchase_server.entity.bo;

import com.purchase.purchase_server.entity.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.io.Serial;

/**
 * @Description 导出-到仓明细
 * <AUTHOR>
 * @Date 2024/1/10 17:16
 **/
@EqualsAndHashCode(callSuper = true)
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class ArrivalDetailsBO extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1878805408022256981L;

    private String deliveryProjectId;
    /**
     * 合同号
     */
    private String contractCode;
    /**
     * 供应商
     */
    private String factoryCode;
    //虚拟sku或老sku
    private String destinationSku;
    //产品名称
    private String productName;
    //渠道
    private String channel;
    //自定义sku
    private String selfProductSku;
    //每箱个数
    private Integer containerLoad;
    //外箱尺寸长
    private Double caseLength;
    //外箱尺寸宽
    private Double caseWidth;
    //外箱尺寸高
    private Double caseHeight;
    //单箱体积
    private Double caseVolume;
    //单箱毛重
    private Double singleCaseGrossWeight;
    //采购人员
    private String buyer;
    //发货装柜时间
    private String shippingStartDate;
    //实际到货时间
    private String realShippingStartDate;
    //工厂交期
    private String factoryFinishedDate;
    //备注
    private String remarks;
    //仓库
    private String destinationWarehouse;
    //仓库发货数量
    private Integer shippingNum;
    //仓体积
    private Double factoryShippingVolume;
    //发货重量
    private Double factoryGrossWeight;

    /**
     * 模拟在途库存
     */
    private String onshippingInventory;
    /**
     * 模拟剩余库存
     */
    private String remainInventory;
    /**
     * 模拟日销
     */
    private String everydaySale;
}
