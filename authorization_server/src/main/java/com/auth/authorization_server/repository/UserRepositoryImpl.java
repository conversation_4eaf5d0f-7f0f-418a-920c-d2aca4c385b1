package com.auth.authorization_server.repository;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.auth.authorization_server.mapper.UserMapper;
import com.auth.authorization_server.model.dataObject.*;
import com.auth.authorization_server.req.UserParams;
import com.auth.authorization_server.domain.vo.UserVO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.crafts_mirror.common.security.utils.SecurityUtils;
import com.auth.authorization_server.domain.dto.UserDTO;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 */
@Service
public class UserRepositoryImpl extends ServiceImpl<UserMapper, UserDO> {
    public static MPJLambdaWrapper<UserDO> buildQueryWrapper() {
        MPJLambdaWrapper<UserDO> LambdaWrapper = new MPJLambdaWrapper<>();
        LambdaWrapper
                .selectAll(UserDO.class)
                .selectCollection(RoleDO.class, UserDTO::getRoles)
                .selectCollection(DeptDO.class, UserDTO::getDepts)
                .leftJoin(UserRoleDO.class, UserRoleDO::getUserId, UserDO::getId)
                .leftJoin(RoleDO.class, RoleDO::getId, UserRoleDO::getRoleId)
                .leftJoin(UserDeptDO.class, UserDeptDO::getUserId, UserDO::getId)
                .leftJoin(DeptDO.class, DeptDO::getId, UserDeptDO::getDeptId);

        return LambdaWrapper;
    }

    /**
     * 根据条件分页查询已配用户角色列表
     *
     * @param user 用户信息
     * @return 用户信息集合信息
     */
    public IPage<UserVO> selectAllocatedRoleList(UserParams user) {
        IPage<UserVO> iPage = new Page<>(user.getCurrent(), user.getSize());
        return baseMapper.selectJoinPage(iPage, UserVO.class, new MPJLambdaWrapper<UserDO>()
                .distinct()
                .selectAll(UserDO.class)
                .leftJoin(UserRoleDO.class, UserRoleDO::getUserId, UserDO::getId)
                .leftJoin(RoleDO.class, RoleDO::getId, UserRoleDO::getRoleId)
                .eq(RoleDO::getId, user.getRoleId())
                .in(CollectionUtil.isNotEmpty(user.getNickNameList()), UserDO::getNickName, user.getNickNameList())
                .in(CollectionUtil.isNotEmpty(user.getPhoneNumberList()), UserDO::getPhoneNumber, user.getPhoneNumberList())
                .orderByAsc(UserDO::getUserStatus)
                .orderByDesc(UserDO::getCreateDate)
                .orderByAsc(UserDO::getNickName)
        );
    }

    /**
     * 根据角色ids查询已分配用户角色列表
     *
     * @param roleIds 角色id
     * @return 用户信息集合信息
     */
    public List<UserDO> selectAllocatedListByRoleIds(Set<String> roleIds) {
        return baseMapper.selectJoinList(UserDO.class, new MPJLambdaWrapper<UserDO>()
                .distinct()
                .selectAll(UserDO.class)
                .leftJoin(UserRoleDO.class, UserRoleDO::getUserId, UserDO::getId)
                .leftJoin(RoleDO.class, RoleDO::getId, UserRoleDO::getRoleId)
                .in(RoleDO::getId, roleIds));
    }

    /**
     * 根据条件分页查询未分配用户角色列表
     *
     * @param user 用户信息
     * @return 用户信息集合信息
     */
    public List<UserVO> selectUnallocatedRoleList(UserParams user) {
        return baseMapper.selectJoinList(UserVO.class, new MPJLambdaWrapper<UserDO>()
                .distinct()
                .selectAll(UserDO.class)
                .leftJoin(UserRoleDO.class, UserRoleDO::getUserId, UserDO::getId)
                .leftJoin(RoleDO.class, RoleDO::getId, UserRoleDO::getRoleId)
                .and(i -> i.ne(RoleDO::getId, user.getRoleId()).or().isNull(RoleDO::getId))
                .apply(" t.id not in (select u.id from cm_sys_user u inner join cm_sys_user_role ur on u.id = ur.user_id and ur.role_id = {0})", user.getRoleId())
                .ne(UserDO::getId, "1")
                .in(CollectionUtil.isNotEmpty(user.getNickNameList()), UserDO::getNickName, user.getNickNameList())
                .in(CollectionUtil.isNotEmpty(user.getPhoneNumberList()), UserDO::getPhoneNumber, user.getPhoneNumberList())
                .orderByAsc(UserDO::getUserStatus)
                .orderByDesc(UserDO::getCreateDate)
                .orderByAsc(UserDO::getNickName)
        );
    }

    /**
     * 根据条件分页查询已配用户部门列表
     *
     * @param user 用户信息
     * @return 用户信息集合信息
     */
    public IPage<UserVO> selectAllocatedDeptList(UserParams user) {
        IPage<UserVO> iPage = new Page<>(user.getCurrent(), user.getSize());
        return baseMapper.selectJoinPage(iPage, UserVO.class, new MPJLambdaWrapper<UserDO>()
                .distinct()
                .selectAll(UserDO.class)
                .leftJoin(UserDeptDO.class, UserDeptDO::getUserId, UserDO::getId)
                .leftJoin(DeptDO.class, DeptDO::getId, UserDeptDO::getDeptId)
                .eq(DeptDO::getId, user.getDeptId())
                .in(CollectionUtil.isNotEmpty(user.getNickNameList()), UserDO::getNickName, user.getNickNameList())
                .in(CollectionUtil.isNotEmpty(user.getPhoneNumberList()), UserDO::getPhoneNumber, user.getPhoneNumberList())
                .orderByAsc(UserDO::getUserStatus)
                .orderByDesc(UserDO::getCreateDate)
                .orderByAsc(UserDO::getNickName)
        );
    }

    /**
     * 根据条件分页查询未分配用户部门列表
     *
     * @param user 用户信息
     * @return 用户信息集合信息
     */
    public List<UserVO> selectUnallocatedDeptList(UserParams user) {
        return baseMapper.selectJoinList(UserVO.class, new MPJLambdaWrapper<UserDO>()
                .distinct()
                .selectAll(UserDO.class)
                .leftJoin(UserDeptDO.class, UserDeptDO::getUserId, UserDO::getId)
                .leftJoin(DeptDO.class, DeptDO::getId, UserDeptDO::getDeptId)
                .and(i -> {
                    i.ne(DeptDO::getId, user.getDeptId()).or().isNull(DeptDO::getId);
                })
                .apply(" t.id not in (select u.id from cm_sys_user u inner join cm_sys_user_dept ud on u.id = ud.user_id and ud.dept_id = {0})", user.getDeptId())
                .ne(UserDO::getId, "1")
                .in(CollectionUtil.isNotEmpty(user.getNickNameList()), UserDO::getNickName, user.getNickNameList())
                .in(CollectionUtil.isNotEmpty(user.getPhoneNumberList()), UserDO::getPhoneNumber, user.getPhoneNumberList())
                .orderByAsc(UserDO::getUserStatus)
                .orderByDesc(UserDO::getCreateDate)
                .orderByAsc(UserDO::getNickName)
        );
    }

    /**
     * 根据条件分页查询用户列表
     *
     * @param sysUser 用户信息
     * @return 用户信息集合信息
     */
    public IPage<UserDO> selectUserPageList(UserParams sysUser) {
        IPage<UserDO> iPage = new Page<>(sysUser.getCurrent(), sysUser.getSize());
        return baseMapper.selectPage(iPage, Wrappers.<UserDO>lambdaQuery()
                .eq(StrUtil.isNotBlank(sysUser.getId()), UserDO::getId, sysUser.getId())
                .like(StrUtil.isNotBlank(sysUser.getUserName()), UserDO::getUserName, sysUser.getUserName())
                .like(StrUtil.isNotBlank(sysUser.getNickName()), UserDO::getNickName, sysUser.getNickName())
                .in(CollectionUtil.isNotEmpty(sysUser.getNickNameList()), UserDO::getNickName, sysUser.getNickNameList())
                .eq(StrUtil.isNotBlank(sysUser.getUserStatus()), UserDO::getUserStatus, sysUser.getUserStatus())
                .like(StrUtil.isNotBlank(sysUser.getPhoneNumber()), UserDO::getPhoneNumber, sysUser.getPhoneNumber())
                .in(CollectionUtil.isNotEmpty(sysUser.getPhoneNumberList()), UserDO::getPhoneNumber, sysUser.getPhoneNumberList())
                .orderByAsc(UserDO::getUserStatus)
                .orderByDesc(UserDO::getCreateDate)
                .orderByAsc(UserDO::getNickName)
        );
    }

    /**
     * 通过用户名查询用户
     *
     * @param userName 用户名
     * @return 用户对象信息
     */
    public UserDTO selectUserByUserName(String userName) {
        return baseMapper.selectJoinOne(UserDTO.class, buildQueryWrapper()
                .eq(UserDO::getUserName, userName));
    }

    /**
     * 通过用户ID查询用户
     *
     * @param userId 用户ID
     * @return 用户对象信息
     */
    public UserDTO selectUserById(String userId) {
        return baseMapper.selectJoinOne(UserDTO.class, buildQueryWrapper()
                .eq(UserDO::getId, userId));
    }

    /**
     * 通过用户ID查询用户
     *
     * @param userIds 用户ID
     * @return 用户对象信息
     */
    public List<UserDTO> selectUserByIds(List<String> userIds) {
        return baseMapper.selectJoinList(UserDTO.class, buildQueryWrapper()
                .in(UserDO::getId, userIds));
    }

    /**
     * 校验用户名称是否唯一
     *
     * @param userName 用户名称
     * @return 结果
     */
    public UserDO checkUserNameUnique(String userName) {
        return baseMapper.selectOne(Wrappers.<UserDO>lambdaQuery()
                .eq(UserDO::getUserName, userName)
                .last("limit 1"));
    }

    /**
     * 校验手机号码是否唯一
     *
     * @param phonenumber 手机号码
     * @return 结果
     */
    public UserDO checkPhoneUnique(String phonenumber) {
        return baseMapper.selectOne(Wrappers.<UserDO>lambdaQuery()
                .eq(UserDO::getPhoneNumber, phonenumber)
                .last("limit 1"));
    }


    /**
     * 批量删除用户信息
     *
     * @param userIds 需要删除的用户ID
     */
    public void deleteUserByIds(List<String> userIds) {
        baseMapper.update(Wrappers.<UserDO>lambdaUpdate()
                .set(UserDO::getUpdateBy, SecurityUtils.getUsername())
                .in(UserDO::getId, userIds));
    }


    /**
     * 重置用户密码
     *
     * @param userName 用户名
     * @param password 密码
     */
    public int resetUserPwd(String userName, String password) {
        return baseMapper.update(Wrappers.<UserDO>lambdaUpdate()
                .set(UserDO::getPassWord, password)
                .eq(UserDO::getUserName, userName));
    }

    /**
     * 根据条件查询用户列表
     *
     * @param user 用户信息
     * @return 用户信息集合信息
     */
    public List<UserDO> selectUserList(UserParams user) {
        return baseMapper.selectList(Wrappers.<UserDO>lambdaQuery()
                .eq(StrUtil.isNotBlank(user.getId()), UserDO::getId, user.getId())
                .eq(StrUtil.isNotBlank(user.getUserName()), UserDO::getUserName, user.getUserName())
                .eq(StrUtil.isNotBlank(user.getNickName()), UserDO::getNickName, user.getNickName())
                .eq(StrUtil.isNotBlank(user.getPostId()), UserDO::getPostId, user.getPostId())
                .eq(StrUtil.isNotBlank(user.getUserStatus()), UserDO::getUserStatus, user.getUserStatus())
                .in(CollectionUtil.isNotEmpty(user.getNickNameList()), UserDO::getNickName, user.getNickNameList())
                .in(CollectionUtil.isNotEmpty(user.getPhoneNumberList()), UserDO::getPhoneNumber, user.getPhoneNumberList())
        );
    }

    /**
     * 根据部门ids查询已配用户部门列表
     *
     * @param deptIds 部门列表
     * @return 用户信息集合信息
     */
    public List<UserVO> selectAllocatedDeptList(List<String> deptIds) {
        return baseMapper.selectJoinList(UserVO.class, new MPJLambdaWrapper<UserDO>()
                .distinct()
                .selectAll(UserDO.class)
                .leftJoin(UserDeptDO.class, UserDeptDO::getUserId, UserDO::getId)
                .leftJoin(DeptDO.class, DeptDO::getId, UserDeptDO::getDeptId)
                .in(DeptDO::getId, deptIds)
                .orderByAsc(UserDO::getUserStatus)
                .orderByDesc(UserDO::getCreateDate)
                .orderByAsc(UserDO::getNickName)
        );
    }
}