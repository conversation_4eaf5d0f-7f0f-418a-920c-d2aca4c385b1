package com.inventory_server.model.redundancy.entity.excelObj;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * <AUTHOR>
 * @Date 2024/10/14 11:07
 **/
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public abstract class RedundancyInfoBaseExcel {
    @ExcelProperty(value = "虚拟SKU", index = 0)
    private String virtualSku;

    @ExcelProperty(value = "自定义SKU", index = 1)
    private String selfSku;

    @ExcelProperty(value = "品名", index = 2)
    private String productName;

    @ExcelProperty(value = "渠道", index = 3)
    private String channel;

    @ExcelProperty(value = "产品状态", index = 4)
    private String productStatus;

    @ExcelProperty(value = "子体类型", index = 5)
    private String subType;

    @ExcelProperty(value = "产品类型", index = 6)
    private String productType;

}
