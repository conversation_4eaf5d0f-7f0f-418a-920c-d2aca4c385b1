package com.auth.authorization_server.service;


import com.auth.authorization_server.domain.dto.UserDTO;

import java.util.Set;

/**
 * 权限信息 服务层
 * 
 * <AUTHOR>
 */
public interface ISysPermissionService
{
    /**
     * 获取角色数据权限
     * 
     * @param userId 用户Id
     * @return 角色权限信息
     */
    //Set<String> getRolePermission(SysUser user);

    /**
     * 获取菜单数据权限
     * 
     * @param user 用户Id
     * @return 菜单权限信息
     */
    Set<String> getMenuPermission(UserDTO user);
}
