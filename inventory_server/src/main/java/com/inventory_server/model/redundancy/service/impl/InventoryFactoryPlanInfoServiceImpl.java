package com.inventory_server.model.redundancy.service.impl;

import cn.hutool.core.util.StrUtil;
import com.inventory_server.model.product.entity.dos.VirtualProductDO;
import com.inventory_server.model.product.repository.databaseRepository.VirtualProductRepositoryImpl;
import com.inventory_server.model.redundancy.entity.aggregate.InventoryFactoryPlanInfoA;
import com.inventory_server.model.redundancy.entity.dos.InventoryFactoryPlanInfoDO;
import com.inventory_server.model.redundancy.entity.dto.DeliveryCalResultFactoryFinishedDto;
import com.inventory_server.model.redundancy.entity.dto.FactoryFinishedInventoryDto;
import com.inventory_server.model.redundancy.enums.DeliveryTypeEnum;
import com.inventory_server.model.redundancy.repository.dataRepository.InventoryFactoryPlanInfoRepository;
import com.inventory_server.model.redundancy.service.IInventoryFactoryPlanInfoService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/5/10
 **/
@Service
@Slf4j
public class InventoryFactoryPlanInfoServiceImpl implements IInventoryFactoryPlanInfoService {

    @Resource
    private InventoryFactoryPlanInfoRepository inventoryFactoryPlanInfoRepository;

    @Resource
    private VirtualProductRepositoryImpl vpRepository;

    @Override
    public List<InventoryFactoryPlanInfoA> getPlanInfoListByInfoIds(List<String> infoIds) {
        return inventoryFactoryPlanInfoRepository.getPlanInfoListByInfoIds(infoIds);
    }

    @Override
    public List<InventoryFactoryPlanInfoA> getPlanInfoListByInfoId(String infoId) {
        return inventoryFactoryPlanInfoRepository.getPlanInfoListByInfoId(infoId);
    }

    @Override
    public List<InventoryFactoryPlanInfoDO> getPlanInfoByIdList(List<String> idList) {
        return inventoryFactoryPlanInfoRepository.listByIds(idList);
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW, rollbackFor = Exception.class)
    @Override
    public List<FactoryFinishedInventoryDto> saveFactoryPlanInfo(List<DeliveryCalResultFactoryFinishedDto> finishedDtoList,
                                                                 String inventoryInfoId, String productName) {
        deleteByInventoryInfoId(inventoryInfoId);
        List<FactoryFinishedInventoryDto> factoryFinishedInventoryList = new ArrayList<>();
        for (var i : finishedDtoList) {
            VirtualProductDO virtualProduct1 = vpRepository.getOneByVirtualSkuOrOldSku(i.getDestinationSku());
            int isOld = 0;
            if (i.getDestinationSku().equals(virtualProduct1.getOldSku())) {
                isOld = 1;
            }

            InventoryFactoryPlanInfoDO buildDO = InventoryFactoryPlanInfoDO.builder()
                    .inventoryInfoId(inventoryInfoId)
                    .contractCode(i.getContractCode())
                    .factoryFinishedDate(i.getFactoryFinishedDate())
                    .storeNum(i.getFactoryShippingPackageNum())
                    .shippingNum(i.getFactoryShippingPackageNum() - i.getFactoryRemainNum())
                    .virtualSkuId(virtualProduct1.getId())
                    .isOld(isOld)
                    .remarks(i.getRemarks())
                    .build();
            inventoryFactoryPlanInfoRepository.save(buildDO);

            // 保存正常发货的发货数据
            FactoryFinishedInventoryDto build = FactoryFinishedInventoryDto.builder()
                    .id(buildDO.getId())
                    .factoryFinishedDate(i.getFactoryFinishedDate())
                    .factoryShippingPackageNum(i.getFactoryShippingPackageNum())
                    .factoryRemainNum(i.getFactoryRemainNum())
                    .contractCode(i.getContractCode())
                    .virtualSku(i.getDestinationSku())
                    .remarks(i.getRemarks())
                    .build();
            getPriorDeliveryFactoryType(build, productName);
            factoryFinishedInventoryList.add(build);
        }
        return factoryFinishedInventoryList;
    }

    /**
     * 计算需要优先发货的计划，这些计划发出去以后当在途库存，代入到发货的计算中去
     * @param productName 产品名称
     */
    private void getPriorDeliveryFactoryType(FactoryFinishedInventoryDto factory, String productName) {
            String remark = factory.getRemarks();
            if (StrUtil.isNotBlank(remark)) {
                if (remark.contains("加急")) {
                    factory.setNeedToPriorDelivery(true);
                    factory.setDeliveryType(DeliveryTypeEnum.URGENT.getCode());
                } else if (productName.contains("配件")) {
                    factory.setNeedToPriorDelivery(true);
                    factory.setDeliveryType(DeliveryTypeEnum.ACCESSORY.getCode());
                } else if (remark.contains("停发")) {
                    factory.setNeedToPriorDelivery(true);
                }
            } else {
                if (productName.contains("配件")) {
                    factory.setNeedToPriorDelivery(true);
                    factory.setDeliveryType(DeliveryTypeEnum.ACCESSORY.getCode());
                }
            }
    }

    @Override
    public void deleteByInventoryInfoId(String inventoryInfoId) {
        inventoryFactoryPlanInfoRepository.deleteByInventoryInfoId(inventoryInfoId);
    }
}
