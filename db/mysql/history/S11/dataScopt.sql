ALTER TABLE `cm_virtual_product`
    MODIFY COLUMN `operator` varchar(510) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '运营人员' AFTER `sub_type`;

CREATE TABLE `cm_sys_module`
(
    `id`               varchar(50) NOT NULL,
    `module_name`      varchar(50) DEFAULT NULL COMMENT '模块名称',
    `function_name`    varchar(50) DEFAULT NULL COMMENT '功能名称',
    `remark` varchar(255) DEFAULT NULL COMMENT '备注',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci
  ROW_FORMAT = DYNAMIC COMMENT ='系统权限模块表';

INSERT INTO `cm_sys_module` VALUES ('100', '产品', '产品管理', '产品经理，运营(自定义sku)');
INSERT INTO `cm_sys_module` VALUES ('101', '产品', '虚拟SKU管理', '运营(虚拟sku)');
INSERT INTO `cm_sys_module` VALUES ('200', '采购', '供应商管理', '跟单人');
INSERT INTO `cm_sys_module` VALUES ('300', '库存', '库存管理', '运营(自定义sku)');

CREATE TABLE `cm_sys_role_data_permission`
(
    `role_id`            varchar(50)  NOT NULL COMMENT '角色ID',
    `data_permission_id` varchar(50)  NOT NULL COMMENT 'user_id,dept_id,module_id',
    `type`               char(1) NOT NULL COMMENT '1-用户，2-部门，3-模块功能',
    PRIMARY KEY (`role_id`, `data_permission_id`, `type`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci
  ROW_FORMAT = DYNAMIC COMMENT ='用户与数据权限关联表';