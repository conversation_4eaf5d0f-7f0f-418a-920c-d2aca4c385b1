<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.purchase.purchase_server.mapper.ReplenishmentVirtualSkuPurchaseMapper">
    <resultMap id="repVirtualSkuDateMap" type="com.purchase.purchase_server.entity.bo.RepVirtualSkuDateBO">
        <id column="id" property="id"/>
        <result column="destination_sku" property="destinationSku"/>
        <result column="channel" property="channel"/>
        <result column="overseas_inventory" property="overseasInventory"/>
        <result column="overseas_shipping" property="overseasShipping"/>
        <result column="local_inventory" property="localInventory"/>
        <result column="replenishment_status" property="replenishmentStatus"/>
        <result column="advice_purchase_num" property="advicePurchaseNum"/>
        <result column="accepted_purchase_num" property="acceptedPurchaseNum"/>
        <result column="rules_id" property="rulesId"/>
    </resultMap>
<!--    <select id="selectRepVirtualSkuList" resultMap="repVirtualSkuDateMap" parameterType="com.purchase.purchase_server.entity.form.ReplenishmentProjectForm">-->
<!--        select b.id,d.virtual_sku as destination_sku,d.virtual_data->>'$.channel' as channel,-->
<!--               b.overseas_inventory,b.overseas_shipping,b.local_inventory,b.replenishment_status,b.advice_purchase_num,b.accepted_purchase_num,b.rules_id-->
<!--        from cm_replenishment_project a-->
<!--        left join cm_replenishment_virtual_sku_purchase b on a.id = b.replenishment_project_id and b.status = '0'-->
<!--        left join cm_replenishment_trial_purchase_inventory c on b.id = c.replenishment_virtual_purchase_id and c.status = '0'-->
<!--        left join cm_product_snapshot d on b.product_snapshot_id = d.id and d.status = '0'-->
<!--        where a.status = '0'-->
<!--        <if test="form.isNotReplenishment != null and form.isNotReplenishment != ''">-->
<!--            and b.replenishment_status = #{form.isNotReplenishment}-->
<!--        </if>-->
<!--        <if test="form.advicePurchaseStartDate != null and form.advicePurchaseStartDate != '' and form.advicePurchaseEndDate != null and form.advicePurchaseEndDate != ''">-->
<!--            and c.advice_purchase_date between #{form.advicePurchaseStartDate} and #{form.advicePurchaseEndDate}-->
<!--        </if>-->
<!--        <if test="form.trialStatus != null and form.trialStatus != ''">-->
<!--            and c.trial_status = #{form.trialStatus}-->
<!--        </if>-->
<!--        <if test="form.replenishmentProjectId != null and form.replenishmentProjectId != ''">-->
<!--            and a.id = #{form.replenishmentProjectId}-->
<!--        </if>-->
<!--        group by-->
<!--        b.id,d.virtual_sku,d.virtual_data->>'$.channel',b.overseas_inventory,b.overseas_shipping,b.local_inventory,b.sold_out_date,-->
<!--        b.replenishment_status,b.advice_purchase_num,b.accepted_purchase_num,b.rules_id-->
<!--        order by b.replenishment_status desc-->
<!--    </select>-->
    <update id="updateAcceptedNumVirtualSku" parameterType="com.purchase.purchase_server.entity.form.AdvicePurchaseForm">
        update cm_replenishment_virtual_sku_purchase a
            ,(
            select c.id,COALESCE(sum(b.advice_purchase_num),0)as advice_purchase_num
            from cm_replenishment_virtual_sku_purchase as c
                     left join cm_replenishment_trial_purchase_inventory b on c.id = b.replenishment_virtual_purchase_id and b.status = '0' and b.trial_status = '1'
            <where> c.replenishment_project_id = #{form.repProjectDto.replenishmentProjectId} and c.status = '0' and c.replenishment_status = '1' </where>
            group by c.id )as d
        <set> a.accepted_purchase_num = d.advice_purchase_num,update_date = NOW(),update_by = #{form.updateBy}</set>
        where a.id = d.id and a.status = '0';
    </update>

    <!-- 批量删除 -->
    <delete id="deleteByIds">
        DELETE FROM cm_replenishment_virtual_sku_purchase WHERE id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <update id="updateVirtualSkuPurchase">
        UPDATE cm_replenishment_virtual_sku_purchase
        <set>confirmed_reason = #{val.confirmedReason}
        </set>
        WHERE id = #{val.id}
    </update>
</mapper>
