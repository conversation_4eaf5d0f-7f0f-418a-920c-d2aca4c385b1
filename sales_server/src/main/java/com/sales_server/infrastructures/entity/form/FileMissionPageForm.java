package com.sales_server.infrastructures.entity.form;

import com.crafts_mirror.utils.dp.BasePageForm;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * @Description 文档任务中心列表页
 * <AUTHOR>
 * @Date 2023/12/12 14:17
 **/
@EqualsAndHashCode(callSuper = true)
@Data
public class FileMissionPageForm extends BasePageForm implements Serializable {
    @Serial
    private static final long serialVersionUID = -5439495840202434L;
    /**
     * 类型
     */
    private List<String> type;
}
