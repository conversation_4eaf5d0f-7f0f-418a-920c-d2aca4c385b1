package com.inventory_server.applications.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;
import java.util.Objects;

/**
 * @Description 工厂交货数量
 * <AUTHOR>
 * @Date 2024/1/2 10:24
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FactoryFinishedInventoryDto implements Serializable {
    private String contractCode;
    private String virtualSku;

    private String isOldStatus;

    private String virtualSkuId;

    private Date factoryFinishedDate;

    private Double shippingNum;
    /**
     * 备注
     */
    private String remark;

    private Boolean needPriorDelivery;


    public int newHashCode() {
        return Objects.hash(contractCode, virtualSku, factoryFinishedDate, remark);
    }
}
