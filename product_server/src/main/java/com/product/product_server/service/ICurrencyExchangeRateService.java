package com.product.product_server.service;

import com.product.product_server.model.currency.dto.ExchangeRateDto;

public interface ICurrencyExchangeRateService {

    ExchangeRateDto updateExchangeRateFromNetworkFromUSD(String toCurrency);

    ExchangeRateDto updateOfficialMiddleExchangeRate(String fromCurrency);

    void saveExchangeRate(ExchangeRateDto exchangeRate);

    ExchangeRateDto getNewestExchangeRate(String fromCurrency, String toCurrency);
}
