<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.purchase.purchase_server.mapper.ShippingRecordMapper">

    <resultMap id="BaseResultMap" type="com.purchase.purchase_server.entity.dataObject.ShippingRecordDO">
        <id property="id" column="id" jdbcType="VARCHAR"/>
        <result property="shipping_start_date" column="shipping_start_date" jdbcType="TIMESTAMP"/>
        <result property="shipping_end_date" column="shipping_end_date" jdbcType="TIMESTAMP"/>
        <result property="trial_status" column="trial_status" jdbcType="CHAR"/>
        <result property="data_source_type" column="data_source_type" jdbcType="CHAR"/>
        <result property="data_source_id" column="data_source_id" jdbcType="VARCHAR"/>
    </resultMap>

    <!-- 批量删除 -->
    <delete id="deleteById">
        DELETE FROM cm_shipping_record WHERE id = #{recordId}
    </delete>
</mapper>
