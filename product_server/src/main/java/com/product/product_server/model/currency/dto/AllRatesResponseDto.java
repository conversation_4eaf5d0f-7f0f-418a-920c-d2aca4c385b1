package com.product.product_server.model.currency.dto;

import com.alibaba.fastjson2.annotation.JSONField;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2025/4/10 11:47
 **/
@Data
public class AllRatesResponseDto implements Serializable {
    @Serial
    private static final long serialVersionUID = 14365464536534654L;

    private Integer count;

    private Map<String, RateDetail> rates;

    @JSONField(name = "update_at")
    private LocalDateTime updateAt;

}