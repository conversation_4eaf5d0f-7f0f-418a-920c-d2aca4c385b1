package com.auth.authorization_server.service.impl;


import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.auth.authorization_server.assembler.RoleAssembler;
import com.auth.authorization_server.domain.dto.DataPermissionDTO;
import com.auth.authorization_server.domain.dto.DeptDTO;
import com.auth.authorization_server.domain.dto.RoleDTO;
import com.auth.authorization_server.domain.vo.UserVO;
import com.auth.authorization_server.enums.DataPermissionTypeEnum;
import com.auth.authorization_server.exception.BaseException;
import com.auth.authorization_server.model.dataObject.*;
import com.auth.authorization_server.model.vo.TreeSelect;
import com.auth.authorization_server.repository.*;
import com.auth.authorization_server.req.RoleParams;
import com.auth.authorization_server.req.UserParams;
import com.auth.authorization_server.service.ISysDeptService;
import com.auth.authorization_server.service.ISysRoleService;
import com.auth.authorization_server.service.ISysUserService;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.crafts_mirror.common.security.utils.SecurityUtils;
import com.crafts_mirror.utils.common.entity.LoginVo;
import com.crafts_mirror.utils.constant.RedisKeyConstant;
import com.crafts_mirror.utils.enums.ResponseCodeEnum;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.crafts_mirror.utils.constant.SecurityConstants.*;

/**
 * 角色 业务层处理
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class SysRoleServiceImpl implements ISysRoleService {


    @Resource
    private RoleRepositoryImpl roleRepository;

    @Resource
    private RoleMenuRepositoryImpl roleMenuRepository;

    @Resource
    private UserRoleRepositoryImpl userRoleRepository;

    @Resource
    private RoleDataPermissionRepositoryImpl roleDataPermissionRepository;

    @Resource
    private ModuleRepositoryImpl moduleRepository;

    @Lazy
    @Resource
    private ISysUserService userService;

    @Resource
    private ISysDeptService deptService;
    @Resource
    private RoleAssembler roleAssembler;

    @Resource
    private RedisTemplate<String, LoginVo> redisTemplate;

    @Override
    public IPage<RoleDTO> selectRoleList(RoleParams role) {
        IPage<RoleDTO> roleDTOIPage = roleRepository.selectRoleList(role);

        Page<RoleDTO> objectPage = new Page<>(role.getCurrent(), role.getSize(), roleDTOIPage.getTotal());
        if (CollectionUtil.isEmpty(roleDTOIPage.getRecords())) {
            return objectPage;
        }
        List<RoleDTO> newRoleList = new ArrayList<>();
        for (RoleDTO roleDTO : roleDTOIPage.getRecords()) {
            if (RoleDO.isAdmin(roleDTO.getId())) {
                newRoleList.addFirst(roleDTO);
            } else {
                newRoleList.add(roleDTO);
            }
        }
        objectPage.setRecords(newRoleList);
        return objectPage;
    }

    @Override
    public RoleDTO selectRoleById(String roleId) {
        return roleRepository.selectRoleById(roleId);
    }

    /**
     * 通过角色ID查询数据权限
     *
     * @param roleId 角色ID
     * @return 权限对象信息
     */
    @Override
    public DataPermissionDTO selectRoleDataPermissionById(String roleId) {
        List<RoleDataPermissionDO> roleData = roleDataPermissionRepository.getRoleDataByRoleIds(Collections.singletonList(roleId));

        List<UserVO> userVOS = userService.selectUserInfo(new UserParams());

        List<TreeSelect> deptTree = deptService.selectDeptTreeList();
        deptTree.addFirst(new TreeSelect("0", "本部门", new ArrayList<>()));
        List<ModuleDO> moduleList = moduleRepository.list();

        record PermissionData(List<String> users, List<String> depts, List<String> modules) {
        }

        TreeMap<String, List<String>> permissionData = roleData.stream()
                .collect(Collectors.groupingBy(
                        RoleDataPermissionDO::getType,
                        TreeMap::new,
                        Collectors.mapping(RoleDataPermissionDO::getDataPermissionId, Collectors.toList())
                ));

        PermissionData checkedData = new PermissionData(
                permissionData.getOrDefault(DataPermissionTypeEnum.USER.getCode(), List.of()),
                permissionData.getOrDefault(DataPermissionTypeEnum.DEPT.getCode(), List.of()),
                permissionData.getOrDefault(DataPermissionTypeEnum.MODULE.getCode(), List.of())
        );

        Map<String, String> deptMap = deptService.selectDeptList().stream()
                .collect(Collectors.toMap(DeptDTO::getId, DeptDTO::getDeptName));
        deptMap.put("0", "本部门");
        List<TreeSelect> deptResult = checkedData.depts().stream()
                .map(dept -> new TreeSelect(dept, deptMap.get(dept), new ArrayList<>()))
                .collect(Collectors.toList());
        return DataPermissionDTO.builder()
                .userList(userVOS.stream().filter(u -> !UserDO.isAdmin(u.getId())).collect(Collectors.toList()))
                .checkedUserList(checkedData.users())
                .deptTree(deptTree)
                .checkedDeptList(deptResult)
                .moduleList(moduleList)
                .checkedModuleList(checkedData.modules())
                .build();
    }

    /**
     * 校验角色名称是否唯一
     *
     * @param role 角色信息
     * @return 结果
     */
    @Override
    public boolean checkRoleNameUnique(RoleParams role) {
        String roleId = StrUtil.isBlank(role.getId()) ? "-1" : role.getId();
        RoleDTO info = roleRepository.checkRoleNameUnique(role.getRoleName());
        return ObjectUtil.isEmpty(info) || roleId.equals(info.getId());
    }

    /**
     * 校验角色是否允许操作
     *
     * @param role 角色信息
     */
    @Override
    public void checkRoleAllowed(RoleParams role) {
        if (StrUtil.isNotEmpty(role.getId()) && RoleDO.isAdmin(role.getId())) {
            throw new BaseException(ResponseCodeEnum.BAD_REQUEST, "不允许操作超级管理员角色");
        }
    }

    /**
     * 新增保存角色信息
     *
     * @param role 角色信息
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean insertRole(RoleParams role) {
        RoleDO roleDO = roleAssembler.roleParamsToDo(role);
        roleDO.setCreateBy(SecurityUtils.getUsername());
        // 新增角色信息
        roleRepository.save(roleDO);
        role.setId(roleDO.getId());
        if (CollectionUtil.isEmpty(role.getModuleIds())) {
            role.setModuleIds(List.of(
                    ROLE_DATA_PER_SELF,
                    ROLE_DATA_PER_VIR,
                    ROLE_DATA_PER_FACTORY,
                    ROLE_DATA_PER_INVENTORY
            ));
        }
        return insertRoleMenu(role) && insertRoleDataPermission(role);
    }

    /**
     * 修改保存角色信息
     *
     * @param role 角色信息
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateRole(RoleParams role) {
        RoleDO roleDO = roleAssembler.roleParamsToDo(role);
        roleDO.setUpdateBy(SecurityUtils.getUsername());
        // 修改角色信息
        roleRepository.updateById(roleDO);
        // 删除角色与菜单关联
        roleMenuRepository.deleteRoleMenuByRoleId(roleDO.getId());
        // 删除角色与数据权限关联
        roleDataPermissionRepository.deleteRoleDataByRoleId(roleDO.getId());
        insertRoleMenu(role);
        insertRoleDataPermission(role);
        updateCacheByRoleIds(roleDO.getId());
        return true;
    }


    private void updateCacheByRoleIds(String roleId) {
        UserParams userParams = new UserParams();
        userParams.setRoleId(roleId);
        userParams.setSize(-1);
        userParams.setCurrent(-1);
        IPage<UserVO> userDTOIPage = userService.selectAllocatedRoleList(userParams);
        List<UserVO> userVOS = userDTOIPage.getRecords();
        if (CollectionUtil.isNotEmpty(userVOS)) {
            userVOS.forEach(i -> {
                // 更新缓存用户信息
                Set<String> keys = redisTemplate.keys(RedisKeyConstant.USER_TOKEN_KEY + i.getId() + ":*");
                if (CollectionUtil.isNotEmpty(keys)) {
                    List<LoginVo> loginVos = redisTemplate.opsForValue().multiGet(keys);
                    if (CollectionUtil.isNotEmpty(loginVos)) {
                        loginVos.forEach(loginVo -> {
                            LoginVo newLoginVo = userService.info(i.getUserName());
                            newLoginVo.setToken(loginVo.getToken());
                            redisTemplate.opsForValue().set(RedisKeyConstant.USER_TOKEN_KEY + i.getId() + ":" + loginVo.getToken(), newLoginVo, 6, TimeUnit.HOURS);
                        });
                    }
                }
            });
        }
    }

    /**
     * 修改角色状态
     *
     * @param role 角色信息
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateRoleStatus(RoleParams role) {
        RoleDO roleDO = roleAssembler.roleParamsToDo(role);
        roleDO.setUpdateBy(SecurityUtils.getUsername());
        roleRepository.updateById(roleDO);
        updateCacheByRoleIds(roleDO.getId());

        return true;
    }

    /**
     * 新增角色菜单信息
     *
     * @param role 角色对象
     */
    public boolean insertRoleMenu(RoleParams role) {
        boolean flag = false;
        // 新增用户与角色管理
        List<RoleMenuDO> list = new ArrayList<RoleMenuDO>();
        for (String menuId : role.getMenuIds()) {
            RoleMenuDO rm = new RoleMenuDO();
            rm.setRoleId(role.getId());
            rm.setMenuId(menuId);
            list.add(rm);
        }
        if (!list.isEmpty()) {
            flag = roleMenuRepository.saveBatch(list);
        }
        return flag;
    }

    /**
     * 新增角色数据权限信息
     *
     * @param role 角色对象
     */
    public boolean insertRoleDataPermission(RoleParams role) {
        List<RoleDataPermissionDO> list = new ArrayList<>();

        addPermissions(role.getUserIds(), role.getId(), DataPermissionTypeEnum.USER, list);
        addPermissions(role.getDeptIds(), role.getId(), DataPermissionTypeEnum.DEPT, list);
        addPermissions(role.getModuleIds(), role.getId(), DataPermissionTypeEnum.MODULE, list);

        return !list.isEmpty() && roleDataPermissionRepository.saveBatch(list);
    }

    private void addPermissions(List<String> ids, String roleId, DataPermissionTypeEnum type, List<RoleDataPermissionDO> list) {
        if (CollectionUtil.isNotEmpty(ids)) {
            ids.stream()
                    .map(id -> createRoleDataPermission(roleId, id, type))
                    .forEach(list::add);
        }
    }

    private RoleDataPermissionDO createRoleDataPermission(String roleId, String dataPermissionId, DataPermissionTypeEnum type) {
        RoleDataPermissionDO rdp = new RoleDataPermissionDO();
        rdp.setRoleId(roleId);
        rdp.setDataPermissionId(dataPermissionId);
        rdp.setType(type.getCode());
        return rdp;
    }

    /**
     * 批量删除角色信息
     *
     * @param roleIds 需要删除的角色ID
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteRoleByIds(List<String> roleIds) {
        for (String roleId : roleIds) {
            RoleParams roleParams = new RoleParams();
            roleParams.setId(roleId);
            checkRoleAllowed(roleParams);
            checkRoleDataScope(roleId);
            RoleDTO role = selectRoleById(roleId);
            if (countUserRoleByRoleId(roleId) > 0L) {
                throw new BaseException(ResponseCodeEnum.BAD_REQUEST, String.format("%1$s已分配,不能删除", role.getRoleName()));
            }
        }
        // 删除角色与菜单关联
        roleMenuRepository.deleteRoleMenu(roleIds);
        roleRepository.removeBatchByIds(roleIds);
        roleIds.forEach(this::updateCacheByRoleIds);

        return true;
    }

    /**
     * 通过角色ID查询角色使用数量
     *
     * @param roleId 角色ID
     * @return 结果
     */
    @Override
    public long countUserRoleByRoleId(String roleId) {
        return userRoleRepository.countUserRoleByRoleId(roleId);
    }

    /**
     * 批量取消授权用户角色
     *
     * @param roleId  角色ID
     * @param userIds 需要取消授权的用户数据ID
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteAuthUsers(String roleId, List<String> userIds) {
        userRoleRepository.deleteUserRoleInfos(roleId, userIds);
        userService.updateCacheByUserIds(userIds);
    }

    /**
     * 校验角色是否有数据权限
     *
     * @param roleIds 角色id
     */
    @Override
    public void checkRoleDataScope(String... roleIds) {
        if (!UserDO.isAdmin(SecurityUtils.getUserId())) {
            if (roleIds != null) {
                for (String roleId : roleIds) {
                    RoleParams role = new RoleParams();
                    role.setId(roleId);
                    role.setCurrent(1);
                    role.setSize(-1);
                    IPage<RoleDTO> roles = selectRoleList(role);
                    if (CollectionUtil.isEmpty(roles.getRecords())) {
                        throw new BaseException(ResponseCodeEnum.BAD_REQUEST, "没有权限访问角色数据！");
                    }
                }
            }
        }
    }

    /**
     * 批量选择授权用户角色
     *
     * @param roleId  角色ID
     * @param userIds 需要授权的用户数据ID
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean insertAuthUsers(String roleId, List<String> userIds) {
        // 新增用户与角色管理
        List<UserRoleDO> list = new ArrayList<UserRoleDO>();
        for (String userId : userIds) {
            UserRoleDO ur = new UserRoleDO();
            ur.setUserId(userId);
            ur.setRoleId(roleId);
            list.add(ur);
        }
        userRoleRepository.saveBatch(list);
        userService.updateCacheByUserIds(userIds);
        return true;
    }

    /**
     * 查询所有角色
     *
     * @return 角色列表
     */
    @Override
    public List<RoleDTO> selectRoleAll() {
        RoleParams role = new RoleParams();
        role.setSize(-1);
        role.setCurrent(1);
        return selectRoleList(role).getRecords();
    }

    /**
     * 根据用户ID查询角色
     *
     * @param userId 用户ID
     * @return 角色列表
     */
    @Override
    public List<RoleDTO> selectRolesByUserId(String userId) {
        List<RoleDTO> userRoles = roleRepository.selectRolePermissionByUserId(userId);
        List<RoleDTO> roles = selectRoleAll();
        for (RoleDTO role : roles) {
            for (RoleDTO userRole : userRoles) {
                if (role.getId().equals(userRole.getId())) {
                    role.setFlag(true);
                    break;
                }
            }
        }
        return roles;
    }

    /**
     * 根据菜单ID查询角色
     *
     * @param menuIds 菜单ID
     * @return 角色列表
     */
    @Override
    public List<RoleDO> selectRolesByMenuIds(List<String> menuIds) {
        return roleRepository.selectRolesByMenuIds(menuIds);
    }
}
