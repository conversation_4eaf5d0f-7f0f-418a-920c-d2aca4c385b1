package com.purchase.purchase_server.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.crafts_mirror.common.security.annotation.RequiresPermissions;
import com.crafts_mirror.utils.enums.UserPostEnum;
import com.purchase.purchase_server.aop.PreventReSubmit;
import com.purchase.purchase_server.entity.consts.FactoryDefaultConstants;
import com.purchase.purchase_server.entity.dto.factory.*;
import com.purchase.purchase_server.entity.form.UserParams;
import com.purchase.purchase_server.entity.response.ResultDTO;
import com.purchase.purchase_server.entity.vo.FactoryUserSearchVo;
import com.purchase.purchase_server.entity.vo.UserSearchVo;
import com.purchase.purchase_server.service.factory.IFactoryAppService;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/6/5
 **/
@RestController
@RequestMapping(value = "factory")
@Slf4j
public class FactoryController {

    @Resource
    private IFactoryAppService factoryAppService;

    @Resource
    private FactoryDefaultConstants factoryDefaultConstants;

    @RequiresPermissions("purchase:factory:list")
    @PostMapping("/import")
    @PreventReSubmit
    public ResultDTO<String> importFactoryApp(@RequestParam("file") MultipartFile file, HttpServletRequest request) {
        log.warn("供应商-------------准备开始导入日志");

        try {
            factoryAppService.importFactoryApp(file.getInputStream(), file.getBytes(), file.getOriginalFilename());
        } catch (IOException e) {
            log.error("导入供应商excel失败，异常信息：", e);
        }
        return ResultDTO.success("正在导入");
    }

    @RequiresPermissions("purchase:factory:list")
    @PostMapping(value = "/pageList")
    public ResultDTO<IPage<FactoryInfoIPageDto>> pageList(@RequestBody FactoryDataPageQuery query) {
        return ResultDTO.success(factoryAppService.pageList(query));
    }

    @RequiresPermissions("purchase:factory:list")
    @PostMapping(value = "/detail")
    public ResultDTO<FactoryDetailDataDto> detail(@RequestBody @Valid FactoryInfoQuery query) {
        return ResultDTO.success(factoryAppService.queryDetail(query));
    }

    @RequiresPermissions("purchase:factory:list")
    @PostMapping(value = "/insertOrUpdateFactory")
    public ResultDTO<Boolean> insertOrUpdateFactory(@RequestBody @Valid FactoryDataCommand factoryDataCommand) {
        return ResultDTO.success(factoryAppService.insertFactory(factoryDataCommand));
    }

    @RequiresPermissions("purchase:factory:list")
    @GetMapping(value = "/delete/factoryInfo")
    public ResultDTO<Boolean> deleteFactoryInfo(@RequestParam("factoryInfoId") @NotNull String factoryInfoId){
        return ResultDTO.success(factoryAppService.deleteInfo(factoryInfoId));
    }

    @RequiresPermissions("purchase:factory:list")
    @GetMapping(value = "/delete/factoryContainer")
    public ResultDTO<Boolean> deleteFactoryContainer(@RequestParam("containerId") @NotNull String containerId){
        return ResultDTO.success(factoryAppService.deleteFactoryContainer(containerId));
    }

    /**
     * 获取采购员列表
     */
    @GetMapping ("/purchasers/set")
    @ResponseBody
    public ResultDTO<FactoryUserSearchVo> getPurchasersSet() {
        UserParams params = new UserParams();
        params.setPostId(UserPostEnum.PURCHASER.getCode());
        return ResultDTO.success(FactoryUserSearchVo.builder()
                .userList(factoryAppService.getUserSet(params))
                .defaultNickName(factoryDefaultConstants.getPurchaserName())
                .defaultUserName(factoryDefaultConstants.getPurchaser())
                .build());
    }

    /**
     * 获取跟单人列表
     */
    @GetMapping ("/orderTracker/set")
    @ResponseBody
    public ResultDTO<FactoryUserSearchVo> getOrderTrackerSet() {
        UserParams params = new UserParams();
        params.setPostId(UserPostEnum.MERCHANDISER.getCode());
        return ResultDTO.success(FactoryUserSearchVo.builder()
                .userList(factoryAppService.getUserSet(params))
                .defaultNickName(factoryDefaultConstants.getOrderTrackerName())
                .defaultUserName(factoryDefaultConstants.getOrderTracker())
                .build());
    }

    /**
     * 获取产品经理列表
     */
    @GetMapping ("/buyer/set")
    @ResponseBody
    public ResultDTO<List<UserSearchVo>> getBuyerSet() {
        UserParams params = new UserParams();
        params.setPostId(UserPostEnum.PRODUCT_MANAGER.getCode());
        return ResultDTO.success(factoryAppService.getUserSet(params));
    }

    @GetMapping("/refresh/BuyerFromSelfProduct")
    public ResultDTO<Boolean> updateBuyerFromSelfProduct() {
        return ResultDTO.success(factoryAppService.updateBuyerFromSelfProduct());
    }
}
