package com.sales_server.interfaces.scheduled;

import com.sales_server.model.targetSales.service.ITargetSalesService;
import com.xxl.job.core.handler.annotation.XxlJob;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @Date 2024/11/18 16:42
 **/
@Service
public class TargetSalesSchedule {

    @Resource
    private ITargetSalesService targetSalesService;

    @XxlJob("createPartitionByYear")
    public void createPartitionByYear() {
        targetSalesService.createPartitionByYear();
    }

}
