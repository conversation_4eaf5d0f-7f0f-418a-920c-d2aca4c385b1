package com.purchase.purchase_server.entity.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.util.Map;

/**
 * @Description 补货列表详情基本参数
 * <AUTHOR>
 * @Date 2024/1/19 19:19
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RepProjectBaseParamDto implements Serializable {

    @Serial
    private static final long serialVersionUID = 8774342146996893807L;

    private String ruleId;
    /**
     * 生产交期
     */
    private String purchaseDate;
    /**
     * 采购周期
     */
    private Integer purchaseCircle;

    /**
     * 发货周期
     */
    private Integer shippingCircle;

    /**
     * 安全天数
     */
    private Integer safeDays;

    /**
     * 可调配安全天数
     */
    private Integer changeableSafeDays;

    /**
     * 采购下单天数
     */
    private Integer purchaseDays;

    /**
     * 中转天数
     */
    private Integer transitDays;

    /**
     * 头程时间
     */
    private Map<String, Integer> headShippingDays;

    /**
     * 发货比例
     */
    private Map<String, Double> shippingRatio;

}
