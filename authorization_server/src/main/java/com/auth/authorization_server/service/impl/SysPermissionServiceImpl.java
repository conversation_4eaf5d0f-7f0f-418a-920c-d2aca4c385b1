package com.auth.authorization_server.service.impl;


import cn.hutool.core.collection.CollectionUtil;
import com.auth.authorization_server.enums.RoleStatusEnum;
import com.auth.authorization_server.model.dataObject.UserDO;
import com.auth.authorization_server.domain.dto.RoleDTO;
import com.auth.authorization_server.service.ISysMenuService;
import com.auth.authorization_server.service.ISysPermissionService;
import com.auth.authorization_server.domain.dto.UserDTO;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * 用户权限处理
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class SysPermissionServiceImpl implements ISysPermissionService {

    @Resource
    private ISysMenuService menuService;

    /**
     * 获取角色数据权限
     *
     * @param userId 用户Id
     * @return 角色权限信息
     */
    //@Override
    //public Set<String> getRolePermission(SysUser user) {
    //    Set<String> roles = new HashSet<String>();
    //    // 管理员拥有所有权限
    //    if (UserDO.isAdmin(user.getId())) {
    //        roles.add("admin");
    //    } else {
    //        roles.addAll(roleService.selectRolePermissionByUserId(user.getId()));
    //    }
    //    return roles;
    //}

    /**
     * 获取菜单数据权限
     *
     * @param user 用户Id
     * @return 菜单权限信息
     */
    @Override
    public Set<String> getMenuPermission(UserDTO user) {
        Set<String> perms = new HashSet<String>();
        // 管理员拥有所有权限
        if (UserDO.isAdmin(user.getId())) {
            perms.add("*:*:*");
        } else {
            List<RoleDTO> roles = user.getRoles();
            if (CollectionUtil.isNotEmpty(roles)) {
                // 多角色设置permissions属性，以便数据权限匹配权限
                for (RoleDTO role : roles) {
                    if (role.getRoleStatus().equals(RoleStatusEnum.YES_STATUS.getCode())) {
                        Set<String> rolePerms = menuService.selectMenuPermsByRoleId(role.getId());
                        role.setPermissions(rolePerms);
                        perms.addAll(rolePerms);
                    }
                }
            } else {
                perms.addAll(menuService.selectMenuPermsByUserId(user.getId()));
            }
        }
        return perms;
    }
}
