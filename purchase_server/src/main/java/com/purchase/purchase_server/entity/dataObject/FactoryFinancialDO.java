package com.purchase.purchase_server.entity.dataObject;


import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.purchase.purchase_server.entity.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;


/**
 * @Description 供应商-财务信息
 * <AUTHOR>
 * @Date 2024/6/5 17:16
 **/
@EqualsAndHashCode(callSuper = true)
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@TableName("cm_factory_financial")
public class FactoryFinancialDO extends BaseEntity {
    /**
     * 省份
     */
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String province;
    /**
     * 城市
     */
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String city;
    /**
     * 详情地址
     */
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String detailedAddress;
    /**
     * 结算类型
     */
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String settlementType;
    /**
     * 定金比例
     */
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private Double depositRatio;
    /**
     * 定金支付时间
     */
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String paymentTime;
    /**
     * 尾款账期
     */
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private Integer finalPaymentTerms;
    /**
     * 备注
     */
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String remark;
    /**
     * 供应商id
     */
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String factoryInfoId;
}
