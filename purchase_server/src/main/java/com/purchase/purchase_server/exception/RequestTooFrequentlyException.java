package com.purchase.purchase_server.exception;

import com.crafts_mirror.utils.enums.ResponseCodeEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @Description 防重提交异常处理
 * <AUTHOR>
 * @Date 2024/1/20 18:23
 **/
@Data
@EqualsAndHashCode(callSuper = true)
public class RequestTooFrequentlyException extends RuntimeException {
    private ResponseCodeEnum responseCode;

    public RequestTooFrequentlyException(String message) {
        super(message);
        this.responseCode = ResponseCodeEnum.ERROR;
    }

    public RequestTooFrequentlyException(ResponseCodeEnum responseCode, String message) {
        super(message);

        setResponseCode(responseCode);
    }
}
