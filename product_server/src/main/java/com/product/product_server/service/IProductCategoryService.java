package com.product.product_server.service;

import com.product.product_server.entity.dto.ProductCategoryDTO;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 针对表【cm_product_category(产品分类表)】的数据库操作Service
 * @createDate 2025-02-07 09:57:30
 */
public interface IProductCategoryService {

    /**
     * 查询产品品类树结构信息
     *
     * @return 产品品类信息集合
     */
    List<ProductCategoryDTO> selectCategoryTree();

    /**
     * 查询子节点对应父节点数据
     *
     * @return 产品品类信息集合
     */
    Map<String, ProductCategoryDTO> selectCategoryLeafTree();

}
