package com.product.product_server.entity.form;


import cn.hutool.core.util.StrUtil;

import java.io.Serializable;
import java.util.List;
import java.util.Objects;

/**
 * @Description 创建父spu表单
 * <AUTHOR>
 * @Date 2023/12/15 20:12
 **/
public record SpuInfoSaveForm(String spuId, String spu, String spuProductName, String remarks, List<String> virtualSkuList) implements Serializable {

    public SpuInfoSaveForm(String spuId, String spu, String spuProductName, String remarks, List<String> virtualSkuList) {
        // spuId以后在有编辑的时候需要用到，此处先保留
        this.spuId = spuId;
        this.spu = Objects.requireNonNull(spu, "父SPU不能为空");
        this.spuProductName = Objects.requireNonNull(spuProductName, "父SPU品名不能为空");
        if(spuProductName.length() > 50 ) {
            throw new IllegalArgumentException("父spu品名长度不能超过50字符");
        }
        this.remarks = remarks;
        if(StrUtil.isNotBlank(remarks) && remarks.length() > 100 ) {
            throw new IllegalArgumentException("父spu备注长度不能超过100字符");
        }
        this.virtualSkuList = Objects.requireNonNull(virtualSkuList, "虚拟sku不能为空");
    }
}
