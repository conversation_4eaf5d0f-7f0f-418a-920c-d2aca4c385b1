package com.inventory_server.model.redundancy.repository.dataRepository;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.crafts_mirror.common.security.dataPermission.DataPermission;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.inventory_server.applications.cqe.InventoryInfoQuery;
import com.inventory_server.model.product.entity.dos.ProductSnapshotDO;
import com.inventory_server.model.product.entity.dos.VirtualProductDO;
import com.inventory_server.model.redundancy.entity.aggregate.InventoryFactoryInfoA;
import com.inventory_server.model.redundancy.entity.dos.InventorySaleRulesDO;
import com.inventory_server.model.redundancy.entity.dos.RedundantInventoryDO;
import com.inventory_server.model.redundancy.enums.DirectionAndSortEnum;
import com.inventory_server.model.redundancy.enums.StatusEnum;
import com.inventory_server.model.redundancy.mapper.RedundantInventoryMapper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

import static com.crafts_mirror.utils.constant.SecurityConstants.ROLE_DATA_PER_INVENTORY;

/**
 * @Description 冗余库存持久层repository类
 * <AUTHOR>
 * @Date 2024/5/7 17:12
 **/
@Service
public class RedundantInventoryRepository extends ServiceImpl<RedundantInventoryMapper, RedundantInventoryDO> {

    public IPage<InventoryFactoryInfoA> pageList(InventoryInfoQuery form) {
        IPage<InventoryFactoryInfoA> iPage = new Page<>(form.getCurrent(), form.getSize());
        return baseMapper.selectJoinPage(iPage, InventoryFactoryInfoA.class, buildFlowPageQueryWrapper(form));
    }

    public static MPJLambdaWrapper<RedundantInventoryDO> buildFlowPageQueryWrapper(InventoryInfoQuery form) {
        MPJLambdaWrapper<RedundantInventoryDO> lambdaWrapper = new MPJLambdaWrapper<>();
        String dataPermissionSql = new DataPermission().getDataPermissionUser(ROLE_DATA_PER_INVENTORY, "t1");
        lambdaWrapper.selectAs(RedundantInventoryDO::getId, "inventoryInfoId")
                .select("t1.self_data->>'$.image' as image")
                .select("t1.virtual_data->>'$.virtualSku' as virtualSku")
                .select("t1.virtual_data->>'$.channel' as channel")
                .select("t1.self_data->>'$.sku' as selfSku")
                .select("t1.self_data->>'$.id' as selfSkuId")
                .select("t1.self_data->>'$.productName' as productName")
                .select(RedundantInventoryDO::getForeignTheoreticalSoldOutDate)
                .select(RedundantInventoryDO::getDaysBeforeSoldOut)
                .select(RedundantInventoryDO::getFullLinkTheoreticalSoldOutDate)
                .select(RedundantInventoryDO::getFullLinkDaysBeforeSoldOut)
                .select(RedundantInventoryDO::getForeignRedundantInventory)
                .select(RedundantInventoryDO::getOnShippingRedundantInventory)
                .select(RedundantInventoryDO::getFactoryRedundantInventory)
                .select(RedundantInventoryDO::getCalFinishedDate)
                .select(RedundantInventoryDO::getUpdateDate)
                .select("t1.virtual_data->>'$.subType' as subType")
                .select("t1.virtual_data->>'$.productType' as productType")
                .select("t1.virtual_data->>'$.productStatus' as productStatus")
                .select("t1.virtual_data->>'$.borrowingStrategy' as borrowingStrategy")
                .select(ProductSnapshotDO::getUpgradeType)
                .select(ProductSnapshotDO::getUpgradeVirtualId)
                .select(VirtualProductDO::getOperator)
                .select(InventorySaleRulesDO::getSevenDaySales)
                .select(InventorySaleRulesDO::getFourteenDaySales)
                .select(InventorySaleRulesDO::getThirtyDaySales)
                .select(InventorySaleRulesDO::getActualDailySales)
                .select(InventorySaleRulesDO::getTargetSalesNum)

                .select(InventorySaleRulesDO::getSubEntityRate)
                .select(RedundantInventoryDO::getSalableDays)
                .select(RedundantInventoryDO::getFullLinkSalableDays)

                .leftJoin(ProductSnapshotDO.class, ProductSnapshotDO::getId, RedundantInventoryDO::getSnapshotId)
                .leftJoin(VirtualProductDO.class, VirtualProductDO::getId, ProductSnapshotDO::getVirtualSkuId)
                .leftJoin(InventorySaleRulesDO.class, InventorySaleRulesDO::getInventoryInfoId, RedundantInventoryDO::getId)
                .eq(RedundantInventoryDO::getStatus, StatusEnum.ALIVE.getCode())
                .in(CollectionUtil.isNotEmpty(form.getVirtualSkuList()), ProductSnapshotDO::getVirtualSku, form.getVirtualSkuList())
                .in(CollectionUtil.isNotEmpty(form.getOldSkuList()), "t1.virtual_data->>'$.oldSku'", form.getOldSkuList())
                .in(CollectionUtil.isNotEmpty(form.getSelfSkuList()), ProductSnapshotDO::getSelfSku, form.getSelfSkuList())
                .in(CollectionUtil.isNotEmpty(form.getSelfSkuIdList()), ProductSnapshotDO::getSelfSkuId, form.getSelfSkuIdList())
                .eq(StrUtil.isNotEmpty(form.getChannel()), "t1.virtual_data->>'$.channel'", form.getChannel())
                .eq(StrUtil.isNotEmpty(form.getSubType()), "t1.virtual_data->>'$.subType'", form.getSubType())
                .eq(StrUtil.isNotEmpty(form.getProductStatus()), "t1.virtual_data->>'$.productStatus'", form.getProductStatus())
                .in(CollectionUtil.isNotEmpty(form.getProductStatusList()), "t1.virtual_data->>'$.productStatus'", form.getProductStatusList())
                .eq(StrUtil.isNotEmpty(form.getProductType()), "t1.virtual_data->>'$.productType'", form.getProductType())
                .like(StrUtil.isNotEmpty(form.getProductName()), "t1.self_data->>'$.productName'", Optional.ofNullable(form.getProductName()).map(String::strip).orElse(null))
                .eq(StrUtil.isNotEmpty(form.getCategoryId()), "t1.self_data->>'$.categoryId'", form.getCategoryId())
                .apply(StrUtil.isNotBlank(form.getOperator()), "FIND_IN_SET('" + form.getOperator() + "',t2.operator)")
                .apply(StrUtil.isNotBlank(dataPermissionSql), dataPermissionSql);

        if (StrUtil.isNotBlank(form.getSort()) && StrUtil.isNotBlank(form.getDirection())) {
            lambdaWrapper.last("ORDER BY " + DirectionAndSortEnum.ofCode(form.getSort()).getTableField() + " IS NULL ASC, " + DirectionAndSortEnum.ofCode(form.getSort()).getTableField() + " " + form.getDirection() +
                    " , update_date DESC");
        } else {
            lambdaWrapper.orderByDesc(RedundantInventoryDO::getUpdateDate);
        }

        return lambdaWrapper;
    }

    @Transactional(rollbackFor = Exception.class)
    public String saveRedundantInventoryInfo(RedundantInventoryDO redundantInventoryDO) {
        var wrapper = Wrappers.<RedundantInventoryDO>lambdaQuery().eq(RedundantInventoryDO::getVirtualSkuId, redundantInventoryDO.getVirtualSkuId());
        RedundantInventoryDO exist = baseMapper.selectOne(wrapper);
        if (exist != null) {
            redundantInventoryDO.setId(exist.getId());
            baseMapper.updateById(redundantInventoryDO);
            return exist.getId();
        }
        baseMapper.insert(redundantInventoryDO);
        return redundantInventoryDO.getId();
    }

    public List<InventoryFactoryInfoA> selectVirtualByInfoIds(Set<String> infoIds) {

        return baseMapper.selectJoinList(InventoryFactoryInfoA.class, new MPJLambdaWrapper<RedundantInventoryDO>()
                .selectAs(RedundantInventoryDO::getId, "inventoryInfoId")
                .selectAs(ProductSnapshotDO::getVirtualSkuId, "virtualSkuId")
                .select(ProductSnapshotDO::getVirtualSku)
                .select("t1.virtual_data->>'$.channel' as channel")
                .select("t1.virtual_data->>'$.borrowingStrategy' as borrowingStrategy")
                .leftJoin(ProductSnapshotDO.class, ProductSnapshotDO::getId, RedundantInventoryDO::getSnapshotId)
                .in(CollectionUtil.isNotEmpty(infoIds), RedundantInventoryDO::getId, infoIds));
    }

    public List<RedundantInventoryDO> getAllRedundancyList() {
        return baseMapper.selectList(Wrappers.lambdaQuery());
    }

    public RedundantInventoryDO getRedundancyByVirtualSkuId(List<String> virtualSkuIds) {
        return baseMapper.selectOne(Wrappers.<RedundantInventoryDO>lambdaQuery()
                .in(RedundantInventoryDO::getVirtualSkuId, virtualSkuIds)
                .last("limit 1"));
    }

    public List<RedundantInventoryDO> getRedundancyByVirtualSkuIdList(List<String> virtualSkuIds) {
        return baseMapper.selectList(Wrappers.<RedundantInventoryDO>lambdaQuery()
                .in(RedundantInventoryDO::getVirtualSkuId, virtualSkuIds));
    }

    public Set<String> getSnapIds(InventoryInfoQuery infoQuery) {
        List<RedundantInventoryDO> list = baseMapper.selectList(Wrappers.<RedundantInventoryDO>lambdaQuery()
                .select(RedundantInventoryDO::getSnapshotId)
                .between(StrUtil.isNotBlank(infoQuery.getCreateStartDate()) && StrUtil.isNotBlank(infoQuery.getCreateEndDate()),
                        RedundantInventoryDO::getUpdateDate, infoQuery.getCreateStartDate(), infoQuery.getCreateEndDate()));
        return CollectionUtil.isEmpty(list) ? Collections.emptySet() :
                list.stream()
                        .map(RedundantInventoryDO::getSnapshotId)
                        .collect(Collectors.toSet());
    }
}
