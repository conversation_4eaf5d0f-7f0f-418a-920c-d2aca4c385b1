package com.purchase.purchase_server.entity.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * @Description 工厂剩余库存
 * <AUTHOR>
 * @Date 2023/12/28 19:34
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FactoryRemainInventoryDto implements Serializable {

    /**
     * 货号
     */
    private String shipmentCode;

    /**
     * 虚拟sku
     */
    private String virtualSku;

    /**
     * 仓库
     */
    private String warehouse;

    /**
     * 库存可用日期
     */
    private Date enableUsingDate;

    /**
     * 在途量
     */
    private Double storeNum;

    /**
     * 发货量区间
     */
    private String deliveryDateRange;

    /**
     * 出货时间
     */
    private Date startShippingTime;

    /**
     * 备注
     */
    private String remarks;

    /**
     * 发货计划中的哈希值
     */
    private Integer factoryHashCode;

    /**
     * 快照id
     */
    private String productSnapshotId;
    /**
     * 虚拟skuId
     */
    private String virtualSkuId;
    /**
     * sku类型
     * @see com.purchase.purchase_server.enums.IsOldStatusEnum
     */
    private String isOldStatus;
}
