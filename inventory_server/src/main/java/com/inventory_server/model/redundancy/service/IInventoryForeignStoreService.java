package com.inventory_server.model.redundancy.service;

import com.inventory_server.applications.dto.FactoryRemainInventoryDto;
import com.inventory_server.model.redundancy.entity.dos.InventoryForeignStoreDO;

import java.util.List;

public interface IInventoryForeignStoreService {

    List<InventoryForeignStoreDO> getForeignStoreListByInfoIds(List<String> infoIds);

    List<InventoryForeignStoreDO> getForeignStoreListByInfoId(String infoId);

    List<InventoryForeignStoreDO> saveForeignStoreInfo(String inventoryId, List<FactoryRemainInventoryDto> list);

    List<InventoryForeignStoreDO> getPlanInfoByIdList(List<String> idList);

    void deleteForeignStoreInfoByInfoId(String inventoryId);
}
