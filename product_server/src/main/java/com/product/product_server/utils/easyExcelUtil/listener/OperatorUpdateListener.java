package com.product.product_server.utils.easyExcelUtil.listener;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.metadata.data.ReadCellData;
import com.alibaba.excel.util.ConverterUtils;
import com.crafts_mirror.utils.enums.UserPostEnum;
import com.crafts_mirror.utils.utils.StrUtils;
import com.product.product_server.entity.dataObject.VirtualProductDO;
import com.product.product_server.entity.excelObject.OperatorExcel;
import com.product.product_server.entity.vo.BuyerSearchVo;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * @Description 更新子体类型
 * <AUTHOR>
 * @Date 2024/8/8 11:15
 **/
@Slf4j
public class OperatorUpdateListener extends AbstractUpdateVirtualProductInfoListener<OperatorExcel> {
    public OperatorUpdateListener(String fileName, String importExcelId) {
        super(fileName, importExcelId);
    }

    @Override
    public void invoke(OperatorExcel data, AnalysisContext context) {
        log.warn("更新运营人员-------------导入excel进行中，成功解析excel");
        Integer approximateRowNumber = context.readSheetHolder().getApproximateTotalRowNumber();
        if (approximateRowNumber != null && approximateRowNumber > 10000) {
            throw new RuntimeException("导入数据超过10000行，请缩小导入数据量");
        }

        VirtualProductDO info = getVirtualProductInfo(data.getVirtualSku());
        String operator = data.getOperator();
        Objects.requireNonNull(operator, "运营人员为空，请填写运营人员");
        List<String> nickNameList = StrUtils.convertStringToList(operator);
        if (CollectionUtil.isEmpty(nickNameList)) {
            throw new IllegalArgumentException("运营人员填写错误");
        }
        StringBuffer operatorBuffer = new StringBuffer();
        nickNameList.stream().distinct().forEach(nickName -> {

            List<BuyerSearchVo> userList = validNickNameExisted(nickName, UserPostEnum.OPERATIONS_MANAGER);
            userList.forEach(i -> {
                if (!operatorBuffer.isEmpty()) {
                    operatorBuffer.append(",");
                }
                operatorBuffer.append(i.getUserName());
            });
        });
        updateVirtualProduct(info.getId(), null, null, null, String.valueOf(operatorBuffer));
    }

    @Override
    public void invokeHead(Map<Integer, ReadCellData<?>> headMap, AnalysisContext context) {
        Map<Integer, String> map = ConverterUtils.convertToStringMap(headMap, context);
        for (Map.Entry<Integer, String> entry : map.entrySet()) {
            if (map.size() == 2) {
                String value = entry.getValue();
                if (!Objects.equals(value, "* 虚拟SKU") && !Objects.equals(value, "* 运营")) {
                    throw new RuntimeException("表头错误，请检查表头是否正确");
                }
            } else {
                throw new RuntimeException("表头错误，请检查表头是否正确");
            }
        }
        super.invokeHead(headMap, context);
    }
}
