ALTER TABLE `crafts_mirror`.`cm_inventory_info`
    ADD COLUMN `full_link_theoretical_sold_out_date` date NULL DEFAULT NULL COMMENT '全链路海外仓理论售罄时间' AFTER `days_before_sold_out`,
ADD COLUMN `full_link_days_before_sold_out` int NULL DEFAULT NULL COMMENT '全链路售罄前断货天数' AFTER `full_link_theoretical_sold_out_date`;

ALTER TABLE `crafts_mirror`.`cm_inventory_sold_out_days`
    ADD COLUMN `type` char NOT NULL COMMENT '0：售罄时间；1：全链路售罄时间' AFTER `update_by`;

-- 处理历史数据（不执行该sql的话，需要冗余库存重新试算一次所有商品）
UPDATE cm_inventory_sold_out_days SET type = '0' WHERE `status` = '0' AND type != '1';