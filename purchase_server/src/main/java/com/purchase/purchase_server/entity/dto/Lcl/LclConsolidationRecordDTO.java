package com.purchase.purchase_server.entity.dto.Lcl;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.math.BigDecimal;
import java.util.Date;


@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class LclConsolidationRecordDTO {
    private String id;
    /**
     * 发货记录id
     */
    private String shippingRecordId;

    /**
     * 发货记录名称
     */
    private String shippingRecordName;

    /**
     * 最早发货装柜时间
     */
    private Date shippingStartDate;

    /**
     * 最晚发货装柜时间
     */
    private Date shippingEndDate;

    /**
     * 状态（-2：作废；-1：已取消；0：草稿；1：数据整理中；2：待排柜；3：排柜中；4：已保存）
     * @see com.purchase.purchase_server.enums.lcl.LclStatusEnum
     */
    private String lclStatus;

    /**
     * 最小体积阈值(立方米)
     */
    private BigDecimal minVolume;
}