package com.purchase.purchase_server.entity.form;

import com.crafts_mirror.utils.aop.validator.PositiveDate;
import jakarta.validation.constraints.NotBlank;
import lombok.*;

import java.io.Serial;
import java.util.Date;
import java.util.List;

import static com.crafts_mirror.utils.constant.DateFormatConstant.YYYY_MM_DD_DATE_FORMAT_HYPHEN;
import static com.crafts_mirror.utils.constant.DateFormatConstant.YYYY_MM_DD_DATE_FORMAT_SLASH;

/**
 * @Description 货件计划查询
 * <AUTHOR>
 * @Date 2023/12/11 14:31
 **/
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ShipmentPlanForm extends BasePageForm {

    /**
     * 货件计划Id
     */
    @NotBlank(message = "货件计划Id不能为空")
    private String planId;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * SKU
     */
    private List<String> selfSkuList;

    /**
     * 虚拟SKU
     */
    private List<String> virtualSkuList;

    /**
     * 货件号
     */
    private String shipmentCode;
    /**
     * 合同号
     */
    private String contractNo;
    /**
     * 位置
     */
    private String warehouseId;
    /**
     * 实际装柜时间
     */
    @PositiveDate(pattern = YYYY_MM_DD_DATE_FORMAT_HYPHEN, message = "实际装柜时间格式不正确")
    private String actualLoadingTime;

    /**
     * 开船时间
     */
    @PositiveDate(pattern = YYYY_MM_DD_DATE_FORMAT_HYPHEN, message = "开船时间格式不正确")
    private String shippingDate;

    /**
     * 预计到港时间
     */
    @PositiveDate(pattern = YYYY_MM_DD_DATE_FORMAT_HYPHEN, message = "预计到港时间格式不正确")
    private String estimatedArrivalTime;

    /**
     * 实际到港时间
     */
    @PositiveDate(pattern = YYYY_MM_DD_DATE_FORMAT_HYPHEN, message = "实际到港时间格式不正确")
    private String actualArrivalTime;

    /**
     * 实际签收时间
     */
    @PositiveDate(pattern = YYYY_MM_DD_DATE_FORMAT_HYPHEN, message = "实际签收时间格式不正确")
    private String actualSignTime;

    /**
     * 预计签收时间
     */
    @PositiveDate(pattern = YYYY_MM_DD_DATE_FORMAT_HYPHEN, message = "预计签收时间格式不正确")
    private String estimatedSignTime;

    /**
     * 海外仓上架时间
     */
    @PositiveDate(pattern = YYYY_MM_DD_DATE_FORMAT_HYPHEN, message = "海外仓上架时间格式不正确")
    private String warehouseShelfTime;

    /**
     * 实际签收
     */
    @PositiveDate(pattern = YYYY_MM_DD_DATE_FORMAT_HYPHEN, message = "实际签收格式不正确")
    private String actualSign;
}
