package com.inventory_server.model.redundancy.repository.dataRepository;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.inventory_server.model.redundancy.entity.dos.InventoryMockTableDO;
import com.inventory_server.model.redundancy.mapper.InventoryMockTableMapper;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/5/10 17:12
 **/
@Service
public class InventoryMockTableRepository extends ServiceImpl<InventoryMockTableMapper, InventoryMockTableDO> {

    public List<InventoryMockTableDO> getInventoryMockTableByInfoId(List<String> infoIds) {
        return baseMapper.selectList(Wrappers.<InventoryMockTableDO>lambdaQuery()
                .in(InventoryMockTableDO::getInventoryInfoId, infoIds)
                .orderByAsc(InventoryMockTableDO::getType)
                .orderByAsc(InventoryMockTableDO::getMockDate)
                .orderByAsc(InventoryMockTableDO::getWarehouse)
        );
    }

    /**
     * 物理删除
     * @param inventoryInfoId 库存id
     */
    public void deleteInventoryMockTableByInfoId(String inventoryInfoId) {
        baseMapper.delete(Wrappers.<InventoryMockTableDO>lambdaQuery()
                .eq(InventoryMockTableDO::getInventoryInfoId, inventoryInfoId));
    }
}
