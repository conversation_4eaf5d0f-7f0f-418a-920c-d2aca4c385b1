package com.purchase.purchase_server.entity.excelObject;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.alibaba.excel.enums.poi.HorizontalAlignmentEnum;
import com.alibaba.excel.enums.poi.VerticalAlignmentEnum;
import lombok.*;

import java.io.Serial;
import java.io.Serializable;

import static com.crafts_mirror.utils.constant.DateFormatConstant.YYYY_MM_DD_DATE_FORMAT_SLASH;

/**
 * @Description excel导出发货计划-未发货分析结果 转换成的实体列
 * <AUTHOR>
 * @Date 2024/1/3 17:00
 **/
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
//excel内容居中
@ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER,verticalAlignment = VerticalAlignmentEnum.CENTER)
//excel字体大小
//@ContentFontStyle(fontHeightInPoints = 15)
public class DeliveryRemainExportInfoExcel extends BaseExcel implements Serializable {
    @Serial
    private static final long serialVersionUID = -1439425840202434L;

    @ExcelProperty(value = "合同号",index = 0)
    private String contractCode;

    @ExcelProperty(value = "虚拟SKU",index = 1)
    private String virtualSku;

    @ExcelProperty(value = "发货装柜时间",index = 2)
    private String shippingStartDate;

    @ExcelProperty(value = "工厂交期",index = 3)
    @DateTimeFormat(YYYY_MM_DD_DATE_FORMAT_SLASH)
    private String factoryFinishedDate;

    @ExcelProperty(value = "发货数量",index = 4)
    private Integer factoryShippingNum;

    @ExcelProperty(value = "未安排数量",index = 5)
    private Integer remainNum;

    @ExcelProperty(value = "说明", index = 6)
    private String reason;

    @ExcelProperty(value = "备注", index = 7)
    private String remarks;
}
