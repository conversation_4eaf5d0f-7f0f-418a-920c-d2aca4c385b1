package com.crafts_mirror.common.security.interceptor;


import cn.hutool.core.util.ObjectUtil;
import com.crafts_mirror.common.security.auth.AuthUtil;
import com.crafts_mirror.common.security.utils.ServletUtils;
import com.crafts_mirror.utils.common.entity.LoginVo;
import com.crafts_mirror.utils.constant.SecurityConstants;
import com.crafts_mirror.utils.context.SecurityContextHolder;
import io.micrometer.common.util.StringUtils;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.stereotype.Component;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.AsyncHandlerInterceptor;

import static com.crafts_mirror.utils.constant.Constants.AUTHORIZATION;


/**
 * 自定义请求头拦截器，将Header数据封装到线程变量中方便获取
 * 注意：此拦截器会同时验证当前用户有效期自动刷新有效期
 *
 * <AUTHOR>
 */
@Component
public class HeaderInterceptor implements AsyncHandlerInterceptor {


    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        if (!(handler instanceof HandlerMethod)) {
            return true;
        }
        String userId = ServletUtils.getHeader(request, SecurityConstants.DETAILS_USER_ID);
        SecurityContextHolder.setUserId(userId);
        SecurityContextHolder.setUserName(ServletUtils.getHeader(request, SecurityConstants.DETAILS_USERNAME));
        String token = request.getHeader(AUTHORIZATION);
        if (StringUtils.isNotEmpty(token)) {
            LoginVo loginVo = AuthUtil.getLoginUser(userId, token);
            if (ObjectUtil.isNotEmpty(loginVo)) {
                SecurityContextHolder.set(SecurityConstants.LOGIN_USER, loginVo);
            }
        }

        return true;
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex)
            throws Exception {
        SecurityContextHolder.remove();
    }
}
