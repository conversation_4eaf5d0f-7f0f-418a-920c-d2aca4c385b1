package com.inventory_server.model.redundancy.repository.dataRepository;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.inventory_server.applications.dto.InventoryFactoryRedundantInfoDto;
import com.inventory_server.model.redundancy.entity.dos.InventoryFactoryRedundantInfoDO;
import com.inventory_server.model.redundancy.entity.dos.InventoryLocalWatchBoardDO;
import com.inventory_server.model.redundancy.mapper.InventoryFactoryRedundantInfoMapper;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/5/10 17:12
 **/
@Service
public class InventoryFactoryRedundantInfoRepository extends ServiceImpl<InventoryFactoryRedundantInfoMapper, InventoryFactoryRedundantInfoDO> {

    public List<InventoryFactoryRedundantInfoDO> getFactoryRedundantList(String infoId) {
        return baseMapper.selectJoinList(InventoryFactoryRedundantInfoDO.class, new MPJLambdaWrapper<InventoryFactoryRedundantInfoDO>()
                .selectAll(InventoryFactoryRedundantInfoDO.class)
                .leftJoin(InventoryLocalWatchBoardDO.class, InventoryLocalWatchBoardDO::getId, InventoryFactoryRedundantInfoDO::getWatchBoardId)
                .eq(InventoryLocalWatchBoardDO::getInventoryInfoId, infoId));
    }

    public List<InventoryFactoryRedundantInfoDto> getFactoryRedundantInfoList(List<String> infoIdList) {
        return baseMapper.selectJoinList(InventoryFactoryRedundantInfoDto.class, new MPJLambdaWrapper<InventoryFactoryRedundantInfoDO>()
                .select(InventoryLocalWatchBoardDO::getInventoryInfoId)
                .select(InventoryFactoryRedundantInfoDO::getWatchBoardId)
                .select(InventoryFactoryRedundantInfoDO::getRedundantNum)
                .select(InventoryFactoryRedundantInfoDO::getContractCode)
                .selectAs(InventoryFactoryRedundantInfoDO::getVirtualSku, "destinationSku")
                .select(InventoryFactoryRedundantInfoDO::getFactoryFinishedDate)
                .leftJoin(InventoryLocalWatchBoardDO.class, InventoryLocalWatchBoardDO::getId, InventoryFactoryRedundantInfoDO::getWatchBoardId)
                .in(InventoryLocalWatchBoardDO::getInventoryInfoId, infoIdList)
                .orderByAsc(InventoryFactoryRedundantInfoDO::getFactoryFinishedDate)
                .orderByAsc(InventoryFactoryRedundantInfoDO::getContractCode)
                .orderByAsc(InventoryFactoryRedundantInfoDO::getVirtualSku));
    }

    /**
     * 物理删除
     *
     * @param watchBoardIds 试算看板表格id
     */
    public void deleteByWatchBoardIds(List<String> watchBoardIds) {
        baseMapper.delete(Wrappers.<InventoryFactoryRedundantInfoDO>lambdaQuery()
                .in(InventoryFactoryRedundantInfoDO::getWatchBoardId, watchBoardIds));
    }
}
