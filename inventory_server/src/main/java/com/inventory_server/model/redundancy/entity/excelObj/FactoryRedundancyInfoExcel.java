package com.inventory_server.model.redundancy.entity.excelObj;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.alibaba.excel.enums.BooleanEnum;
import com.alibaba.excel.enums.poi.HorizontalAlignmentEnum;
import com.alibaba.excel.enums.poi.VerticalAlignmentEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.io.Serial;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2024/10/14 11:37
 **/
@EqualsAndHashCode(callSuper = true)
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@ContentStyle(wrapped = BooleanEnum.TRUE,
        horizontalAlignment = HorizontalAlignmentEnum.CENTER,
        verticalAlignment = VerticalAlignmentEnum.CENTER)
public class FactoryRedundancyInfoExcel extends RedundancyInfoBaseExcel implements Serializable {
    @Serial
    private static final long serialVersionUID = -7429965496550484991L;

    @ExcelProperty(value = "借货策略")
    private String borrowingStrategy;

    @ExcelProperty(value = "合同号")
    private String contractCode;

    @ExcelProperty(value = "冗余虚拟sku")
    private String virtualSku;

    @ExcelProperty(value = "工厂交期")
    private String factoryFinishedDate;

    @ExcelProperty(value = "冗余量")
    private Double redundancy;
}
