package com.product.product_server.entity.dataObject;


import com.baomidou.mybatisplus.annotation.TableName;
import com.product.product_server.entity.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;


/**
 * @Description 供应商-装柜信息表
 * <AUTHOR>
 * @Date 2024/6/5 17:16
 **/
@EqualsAndHashCode(callSuper = true)
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@TableName("cm_factory_container")
public class FactoryContainerDO extends BaseEntity {
    /**
     * 省份
     */
    private String province;
    /**
     * 城市
     */
    private String city;
    /**
     * 详情地址
     */
    private String detailedAddress;
    /**
     * 地址代号
     */
    private String addressCode;
    /**
     * 默认标识
     */
    private String defaultIdentifier;

    /**
     * 供应商id
     */
    private String factoryInfoId;
}
