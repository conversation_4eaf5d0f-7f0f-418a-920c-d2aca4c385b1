package com.operation.logs.operation_logs.controller;

import com.crafts_mirror.utils.web.domain.ResultDTO;
import com.operation.logs.operation_logs.model.visitLog.entity.VisitLogDO;
import com.operation.logs.operation_logs.service.IVisitLogService;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.*;

/**
 * @Description 访问日志内部访问controller层
 * <AUTHOR>
 * @Date 2024/8/20 10:55
 **/
@RestController
@RequestMapping("interior/visitLog")
public class VisitLogInteriorController {

    @Resource
    private IVisitLogService visitLogService;

    @PostMapping("save")
    @ResponseBody
    public ResultDTO<String> saveVisitLog(@RequestBody VisitLogDO visitLog) {
        visitLogService.saveVisitLog(visitLog);
        return ResultDTO.success("save successfully");
    }
}
