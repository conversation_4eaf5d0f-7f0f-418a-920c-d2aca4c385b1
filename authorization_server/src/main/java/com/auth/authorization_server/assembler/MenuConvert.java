package com.auth.authorization_server.assembler;


import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;

import java.util.Date;

/**
 * 转换工具类
 *
 * <AUTHOR>
 * @date 2021/8/26
 */

public class MenuConvert {

    public static Date StringToDate(String value) {
        if (ObjectUtil.isNull(value)) {
            return null;
        }
        return DateUtil.parse(value);
    }
}
