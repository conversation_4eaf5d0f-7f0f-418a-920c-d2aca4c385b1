package com.purchase.purchase_server.service.purchaseOrder.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelReader;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.read.metadata.ReadSheet;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.crafts_mirror.common.security.utils.SecurityUtils;
import com.crafts_mirror.utils.enums.ResponseCodeEnum;
import com.crafts_mirror.utils.utils.RestTemplateUtils;
import com.crafts_mirror.utils.utils.SnowflakeIdWorker;
import com.purchase.purchase_server.entity.dataObject.YicangPurchaseOrderVirtualDO;
import com.purchase.purchase_server.entity.dataObject.YicangPurchaseOrdersDO;
import com.purchase.purchase_server.entity.dto.PurchaseOrder.OrderSaveScheduleDto;
import com.purchase.purchase_server.entity.dto.PurchaseOrder.OrdersSummaryDto;
import com.purchase.purchase_server.entity.dto.PurchaseOrder.PurchaseOrderImportDTO;
import com.purchase.purchase_server.entity.dto.PurchaseOrder.YicangPurchaseOrderScheduleDto;
import com.purchase.purchase_server.entity.excelObject.PoDeliveryScheduleExcel;
import com.purchase.purchase_server.entity.excelObject.PurchaseOrdersExportInfoExcel;
import com.purchase.purchase_server.entity.form.FileMissionForm;
import com.purchase.purchase_server.entity.form.PurchaseOrdersForm;
import com.purchase.purchase_server.entity.response.ResultDTO;
import com.purchase.purchase_server.entity.vo.UserInteriorVO;
import com.purchase.purchase_server.entity.vo.purchaseOrder.YicangPurchaseOrderVirtualVO;
import com.purchase.purchase_server.entity.vo.purchaseOrder.YicangPurchaseOrdersRemainNumVO;
import com.purchase.purchase_server.entity.vo.purchaseOrder.YicangPurchaseOrdersVO;
import com.purchase.purchase_server.enums.purchaseOrders.*;
import com.purchase.purchase_server.repository.dataRepository.YicangPurchaseOrdersRepositoryImpl;
import com.purchase.purchase_server.service.FileCenterService;
import com.purchase.purchase_server.service.channel.IChannelInfoService;
import com.purchase.purchase_server.service.purchaseOrder.IPurchaseOrdersService;
import com.purchase.purchase_server.service.purchaseOrder.IYicangPurchaseOrderDeliveryScheduleService;
import com.purchase.purchase_server.service.purchaseOrder.IYicangPurchaseOrderDetailService;
import com.purchase.purchase_server.service.purchaseOrder.IYicangPurchaseOrderVirtualService;
import com.purchase.purchase_server.service.replenishment.ISysUserInteriorService;
import com.purchase.purchase_server.utils.easyExcelUtil.listener.purchaseOrder.PoScheduleImportListener;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import java.io.InputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.util.*;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.stream.Collectors;

import static com.crafts_mirror.utils.constant.DateFormatConstant.YYYY_MM_DD_DATE_FORMAT_HYPHEN;
import static com.crafts_mirror.utils.constant.SystemConstant.FILE_SYSTEM_MISSION_CENTER_URL;
import static com.crafts_mirror.utils.constant.SystemConstant.FILE_SYSTEM_PUT_OBJECT_BY_FILE;
import static com.purchase.purchase_server.entity.consts.FilePathConstant.FILE_PATH_PURCHASE_ORDER;

/**
 * <AUTHOR>
 * @description 针对表【cm_yicang_purchase_orders(易仓采购单表)】的数据库操作Service实现
 * @createDate 2024-11-01 14:30:43
 */
@Service
@Slf4j
public class PurchaseOrdersServiceImpl implements IPurchaseOrdersService {

    @Resource
    private YicangPurchaseOrdersRepositoryImpl purchaseOrdersRepository;

    @Resource
    private IYicangPurchaseOrderDetailService purchaseOrderDetailService;

    @Resource
    private IYicangPurchaseOrderVirtualService purchaseOrderVirtualService;

    @Resource
    private IYicangPurchaseOrderDeliveryScheduleService purchaseOrderDeliveryScheduleService;

    @Resource
    private ISysUserInteriorService sysUserInteriorService;

    @Resource
    protected RestTemplate restTemplate;

    @Resource
    private SnowflakeIdWorker snowflakeIdWorker;

    @Resource
    private FileCenterService fileCenterService;

    @Resource
    private IChannelInfoService channelInfoService;

    @Override
    public List<YicangPurchaseOrdersDO> select(OrdersSummaryDto dto) {
        return purchaseOrdersRepository.select(dto);
    }

    @Override
    public void updateById(YicangPurchaseOrdersDO yicangPurchaseOrdersDO) {
        purchaseOrdersRepository.updateById(yicangPurchaseOrdersDO);
    }

    @Override
    public void save(YicangPurchaseOrdersDO ordersDO) {
        purchaseOrdersRepository.save(ordersDO);
    }

    @Override
    public IPage<YicangPurchaseOrdersVO> pageList(PurchaseOrdersForm form) {
        // 检查是否为再次审核
        if (Objects.equals(form.getPoStaus(), String.valueOf(PoStatusEnum.UNDER_REVIEW_AGAIN.getCode()))) {
            form.setPoStaus(String.valueOf(PoStatusEnum.UNDER_REVIEW.getCode()));
            form.setIsReExamine(String.valueOf(IsReExamineEnum.REQUIRED.getCode()));
        }
        // 获取分页数据
        IPage<YicangPurchaseOrdersVO> ordersPage = purchaseOrdersRepository.pageList(form);
        List<UserInteriorVO> userList = sysUserInteriorService.getUserList();
        Map<String, String> collect = userList.stream().collect(Collectors.toMap(UserInteriorVO::getUserName, UserInteriorVO::getNickName));

        // 处理订单记录
        ordersPage.getRecords().forEach(order -> {
            // 设置订单状态描述
            order.setPoStatusDesc(
                    order.getIsReExamine().equals(IsReExamineEnum.REQUIRED.getCode()) &&
                            order.getPoStaus().equals(PoStatusEnum.UNDER_REVIEW.getCode())
                            ? PoStatusEnum.UNDER_REVIEW_AGAIN.getDesc()
                            : PoStatusEnum.ofCode(order.getPoStaus()).getDesc()
            );
            if (order.getQtyEtaAll() != null){
                order.setPendingDeliveryQty(order.getQtyEtaAll() - order.getQtyRecevingAll());
            }
            order.setPayStatus(PayStatusEnum.ofCode(order.getPayStatus()).getDesc());

            String orderTracker = order.getOrderTracker();
            order.setOrderTracker(StrUtil.isNotBlank(collect.get(orderTracker)) ? collect.get(orderTracker) : orderTracker);
            String purchaser = order.getPurchaser();
            order.setPurchaser(StrUtil.isNotBlank(collect.get(purchaser)) ? collect.get(purchaser) : purchaser);

        });
        return ordersPage;
    }

    @Override
    public IPage<YicangPurchaseOrdersRemainNumVO> pageListByRemainNum(PurchaseOrdersForm form) {
        // 检查是否为再次审核
        //if (Objects.equals(form.getPoStaus(), String.valueOf(PoStatusEnum.UNDER_REVIEW_AGAIN.getCode()))) {
        //    form.setPoStaus(String.valueOf(PoStatusEnum.UNDER_REVIEW.getCode()));
        //    form.setIsReExamine(String.valueOf(IsReExamineEnum.REQUIRED.getCode()));
        //}
        // 获取分页数据
        IPage<YicangPurchaseOrdersRemainNumVO> ordersPage = purchaseOrdersRepository.pageListByRemainNum(form);

        List<UserInteriorVO> userList = sysUserInteriorService.getUserList();
        Map<String, String> collect = userList.stream().collect(Collectors.toMap(UserInteriorVO::getUserName, UserInteriorVO::getNickName));

        Map<String, String> channelIdNameMap = channelInfoService.getChannelIdNameMap();

        ordersPage.getRecords().forEach(item -> {
            String operator = item.getOperator();
            String operatorNames = "";
            if (StrUtil.isNotBlank(operator)) {
                // 使用String.split()方法的新重载,直接返回Stream
                operatorNames = Arrays.stream(operator.split(",", 0))
                        .map(op -> StrUtil.isNotBlank(collect.get(op)) ? collect.get(op) : op)
                        .collect(Collectors.joining(","));
            }
            item.setOperator(operatorNames);
            String buyer = item.getBuyer();
            item.setBuyer(StrUtil.isNotBlank(collect.get(buyer)) ? collect.get(buyer) : buyer);

            String orderTracker = item.getOrderTracker();
            item.setOrderTracker(StrUtil.isNotBlank(collect.get(orderTracker)) ? collect.get(orderTracker) : orderTracker);

            String updateBy = item.getUpdateBy();
            item.setUpdateBy(StrUtil.isNotBlank(collect.get(updateBy)) ? collect.get(updateBy) : updateBy);

            item.setChannel(channelIdNameMap.getOrDefault(item.getChannel(), item.getChannel()));

            // 设置订单状态描述
            item.setPoStatusDesc(
                    item.getIsReExamine().equals(IsReExamineEnum.REQUIRED.getCode()) &&
                            item.getPoStaus().equals(PoStatusEnum.UNDER_REVIEW.getCode())
                            ? PoStatusEnum.UNDER_REVIEW_AGAIN.getDesc()
                            : PoStatusEnum.ofCode(item.getPoStaus()).getDesc()
            );
            item.setExpectedDeliveryDate(DateUtil.formatDate(DateUtil.parse(item.getExpectedDeliveryDate())));
        });
        return ordersPage;
    }

    @Override
    public List<YicangPurchaseOrderVirtualVO> getOrderDetail(PurchaseOrdersForm form) {
        List<YicangPurchaseOrderVirtualVO> orderDetail = purchaseOrderDetailService.getOrderDetail(form);

        //运营
        List<UserInteriorVO> userList = sysUserInteriorService.getUserList();
        Map<String, String> collect = userList.stream().collect(Collectors.toMap(UserInteriorVO::getUserName, UserInteriorVO::getNickName));

        // 渠道
        Map<String, String> channelIdNameMap = channelInfoService.getChannelIdNameMap();

        LocalDate currentDate = LocalDate.now();
        orderDetail.forEach(item -> {
            String operator = item.getOperator();
            String operatorNames = "";
            if (StrUtil.isNotBlank(operator)) {
                // 使用String.split()方法的新重载,直接返回Stream
                operatorNames = Arrays.stream(operator.split(",", 0))
                        .map(op -> StrUtil.isNotBlank(collect.get(op)) ? collect.get(op) : op)
                        .collect(Collectors.joining(","));
            }
            item.setOperator(operatorNames);
            String buyer = item.getBuyer();
            item.setBuyer(StrUtil.isNotBlank(collect.get(buyer)) ? collect.get(buyer) : buyer);

            item.setChannel(channelIdNameMap.getOrDefault(item.getChannel(), item.getChannel()));

            List<YicangPurchaseOrderScheduleDto> list = item.getScheduleList().stream()
                    .sorted(Comparator.comparing(
                            YicangPurchaseOrderScheduleDto::getExpectedDeliveryDate,
                            Comparator.nullsLast(Comparator.reverseOrder())))
                    .peek(schedule -> {
                        if (schedule.getExpectedDeliveryDate() != null) {
                            String format = DateUtil.format(DateUtil.parse(schedule.getExpectedDeliveryDate()), YYYY_MM_DD_DATE_FORMAT_HYPHEN);
                            schedule.setExpectedDeliveryDate(format);
                        }
                    })
                    .toList();
            item.setScheduleList(list);


            // 查找大于等于当前日期的最近日期的订单
            list.stream()
                    .filter(schedule -> schedule.getExpectedDeliveryDate() != null)
                    .map(schedule -> {
                        try {
                            return new AbstractMap.SimpleEntry<>(
                                    schedule,
                                    LocalDate.parse(schedule.getExpectedDeliveryDate())
                            );
                        } catch (Exception e) {
                            return null;
                        }
                    })
                    .filter(Objects::nonNull)
                    .filter(entry -> !entry.getValue().isBefore(currentDate))
                    .min(Map.Entry.comparingByValue())
                    .ifPresent(entry -> item.setLatestDeliverySchedule(entry.getKey()));
            if (CollectionUtil.isNotEmpty(item.getScheduleList())){
                item.setExpectedDeliveryQuantity(
                        item.getScheduleList().stream()
                                .map(YicangPurchaseOrderScheduleDto::getExpectedDeliveryQuantity)
                                .filter(Objects::nonNull)
                                .reduce(0, Integer::sum));
            }
        });

        return orderDetail;
    }

    @Override
    @Async
    public void importPurchaseOrderInfo(InputStream file, byte[] fileBytes, String fileName) {
        RestTemplateUtils restTemplateUtil = new RestTemplateUtils(restTemplate, SecurityUtils.getToken());
        //上传oss
        String snowId = String.valueOf(snowflakeIdWorker.nextId());
        String key = snowId + "." + Objects.requireNonNull(fileName).substring(fileName.lastIndexOf(".") + 1);
        MultiValueMap<String, Object> httpEntity = fileCenterService.putFile(fileBytes, fileName, FILE_PATH_PURCHASE_ORDER + key, DateUtil.offsetDay(new Date(), 730).toString());
        ResultDTO<String> resultDTO = restTemplateUtil.post(httpEntity, ResultDTO.class, FILE_SYSTEM_PUT_OBJECT_BY_FILE);
        if (!Objects.equals(resultDTO.getStatus(), ResponseCodeEnum.OK.getCode())) {
            log.error("导入采购交货计划时上传oss失败，异常原因：{}", resultDTO.getMessage());
            throw new RuntimeException("导入发货计划时上传oss失败，异常原因：" + resultDTO.getMessage());
        }
        ResultDTO<String> restResult = restTemplateUtil.post(
                FileMissionForm.builder().fileName(fileName)
                        .importStatus("导入分析中").type("采购交货计划导入")
                        .filePath(key)
                        .build(),
                ResultDTO.class, FILE_SYSTEM_MISSION_CENTER_URL
        );
        if (!Objects.equals(restResult.getStatus(), ResponseCodeEnum.OK.getCode())) {
            log.error("导入采购交货计划时插入文件中心失败，异常原因：{}", restResult.getMessage());
            throw new RuntimeException("导入采购交货计划时插入文件中心失败，异常原因：" + restResult.getMessage());
        }

        List<String> errorList = new CopyOnWriteArrayList<>();
        int countSum = 0;
        ExcelReader excelReader = null;
        try {
            List<PurchaseOrderImportDTO> purchaseOrderImportDto = new ArrayList<>();

            ReadSheet readSheet =
                    EasyExcel.readSheet(0).headRowNumber(1).head(PoDeliveryScheduleExcel.class)
                            .registerReadListener(new PoScheduleImportListener(errorList, purchaseOrderImportDto)).autoTrim(true).build();
            excelReader = EasyExcel.read(file).build();
            excelReader.read(readSheet);

            if (CollectionUtil.isNotEmpty(purchaseOrderImportDto)) {
                countSum = purchaseOrderImportDto.size();
                List<PurchaseOrderImportDTO> purchaseOrderImportDTOS = processGroupedData(purchaseOrderImportDto);
                purchaseOrderImportDTOS.forEach(dto -> {
                    if (StrUtil.isNotBlank(dto.getDestinationSku())) {
                        YicangPurchaseOrderVirtualDO poVirtualDO = new YicangPurchaseOrderVirtualDO();
                        purchaseOrderVirtualService.saveOrderVirtualByImport(dto, poVirtualDO);
                        if (StrUtil.isNotBlank(dto.getDeliveryDate())) {
                            purchaseOrderDeliveryScheduleService.saveOrderVirtualByImport(dto, poVirtualDO.getId());
                        }
                    } else {
                        List<YicangPurchaseOrderVirtualVO> orderVirtualList = purchaseOrderVirtualService.getOrderVirtualDetail(PurchaseOrdersForm.builder().sbPoId(dto.getSbPoId()).build());
                        List<String> orderVirtualIds = orderVirtualList.stream().map(YicangPurchaseOrderVirtualVO::getId).collect(Collectors.toList());
                        List<String> orderDetailIds = orderVirtualList.stream().map(YicangPurchaseOrderVirtualVO::getSbPoDetailId).collect(Collectors.toList());
                        purchaseOrderDeliveryScheduleService.removeBySbPoVirtualIds(orderVirtualIds);
                        purchaseOrderVirtualService.removeBySbPoDetailIds(orderDetailIds);
                    }
                    YicangPurchaseOrdersDO ordersDO = new YicangPurchaseOrdersDO();
                    ordersDO.setId(dto.getSbPoId());
                    ordersDO.setIsImported(IsImportedEnum.IMPORT.getCode());
                    purchaseOrdersRepository.updateById(ordersDO);
                });
            }
            restTemplateUtil.post(
                    FileMissionForm.builder()
                            .missionId(restResult.getData())
                            .importStatus("导入成功")
                            .type("采购交货计划导入")
                            .failedResultList(errorList)
                            .importResult(String.format(String.format("成功：%s,失败：%s",
                                    countSum, errorList.size())))
                            .finishDate(new Date())
                            .build(),
                    ResultDTO.class, FILE_SYSTEM_MISSION_CENTER_URL);
        } catch (Exception e) {
            log.error("导入采购交货计划异常" + e);
            restTemplateUtil.post(
                    FileMissionForm.builder()
                            .missionId(restResult.getData())
                            .importStatus("导入失败")
                            .type("采购交货计划导入")
                            .failedResultList(Collections.singletonList(e.getMessage()))
                            .finishDate(new Date())
                            .build(),
                    ResultDTO.class, FILE_SYSTEM_MISSION_CENTER_URL);
        } finally {
            if (excelReader != null) {
                log.info("导入采购交货计划关闭");
                excelReader.finish();
            }
        }
    }

    @Override
    public void exportOrderInfo(PurchaseOrdersForm form, HttpServletResponse response) {
        List<PurchaseOrdersExportInfoExcel> orderExcel = new ArrayList<>();
        form.setSize(-1);
        form.setCurrent(1);

        if (Boolean.FALSE.equals(form.getExportFlag())) {
            IPage<YicangPurchaseOrdersVO> ordersVOPage = pageList(form);
            List<YicangPurchaseOrdersVO> records = ordersVOPage.getRecords();
            List<String> orderIdList = records.stream().map(YicangPurchaseOrdersVO::getId).toList();
            var orderDetailList = purchaseOrderVirtualService.getOrderVirtualDetail(PurchaseOrdersForm.builder().sbPoIdList(orderIdList).build());

            Map<String, List<YicangPurchaseOrderVirtualVO>> orderVirtualMap = orderDetailList.stream()
                    .collect(Collectors.groupingBy(YicangPurchaseOrderVirtualVO::getSbPoId));

            records.forEach(order -> {
                if (!orderVirtualMap.containsKey(order.getId())) {
                    orderExcel.add(createBaseExcel(order));
                    return;
                }
                orderVirtualMap.get(order.getId()).forEach(virtualOrder -> {
                    List<YicangPurchaseOrderScheduleDto> scheduleList = virtualOrder.getScheduleList();

                    if (CollectionUtil.isEmpty(scheduleList)) {
                        orderExcel.add(createExcelWithVirtual(order, virtualOrder));
                        return;
                    }
                    scheduleList.forEach(schedule ->
                            orderExcel.add(createExcelWithSchedule(order, virtualOrder, schedule))
                    );
                });
            });
        } else {
            IPage<YicangPurchaseOrdersRemainNumVO> ordersVOPage = pageListByRemainNum(form);

            ordersVOPage.getRecords().forEach(order -> orderExcel.add(
                    PurchaseOrdersExportInfoExcel.builder()
                            .refNo(order.getRefNo())
                            .poCode(order.getPoCode())
                            .poStatus(order.getPoStatusDesc())
                            .supplierCode(order.getSupplierCode())
                            .qtyEtaAll(order.getQtyEtaAll())
                            .qtyRecevingAll(order.getQtyRecevingAll())
                            .pendingDeliveryQty(order.getQtyEtaAll() - order.getQtyRecevingAll())
                            .destinationSku(order.getDestinationSku())
                            .qtyEta(order.getQtyEta())
                            .expectedDeliveryDate(order.getExpectedDeliveryDate())
                            .expectedDeliveryQuantity(order.getExpectedDeliveryQuantity())
                            .build()));
        }
        ExcelWriter excelWriter = null;
        try {
            response.setContentType("application/vnd.ms-excel");
            response.setCharacterEncoding("utf-8");
            String fileName = URLEncoder.encode("交货计划导出", StandardCharsets.UTF_8).replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
            excelWriter = EasyExcel.write(response.getOutputStream()).build();

            WriteSheet writeSheet = EasyExcel.writerSheet(1, "交货计划").head(PurchaseOrdersExportInfoExcel.class)
                    .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                    .build();

            excelWriter.write(orderExcel, writeSheet);
        } catch (Exception e) {
            log.error("获取输出流异常", e);
            throw new RuntimeException("获取输出流异常", e);
        } finally {
            if (excelWriter != null) {
                excelWriter.finish();
            }
        }
    }

    // 使用 Stream API 处理分组汇总
    private List<PurchaseOrderImportDTO> processGroupedData(List<PurchaseOrderImportDTO> list) {
        // 第一步：按 refNo 和 destinationSku 分组，计算 qtyEta 合计
        List<String> notNullRefNoList = new ArrayList<>();
        Map<String, PurchaseOrderImportDTO> refAndSkuGroup = list.stream()
                .filter(dto -> dto.getDestinationSku() != null) // 过滤掉 destinationSku 为 null 的数据
                .collect(Collectors.groupingBy(
                        dto -> dto.getRefNo() + "_" + dto.getDestinationSku(),
                        Collectors.reducing(
                                new PurchaseOrderImportDTO(),
                                (dto1, dto2) -> {
                                    String refNo = dto1.getRefNo() == null ? dto2.getRefNo() : dto1.getRefNo();
                                    notNullRefNoList.add(refNo);
                                    return PurchaseOrderImportDTO.builder()
                                            .refNo(refNo)
                                            .detailId(dto1.getDetailId() == null ? dto2.getDetailId() : dto1.getDetailId())
                                            .sbPoId(dto1.getSbPoId() == null ? dto2.getSbPoId() : dto1.getSbPoId())
                                            .destinationSku(dto1.getDestinationSku() == null ? dto2.getDestinationSku() : dto1.getDestinationSku())
                                            .isOldStatus(dto1.getIsOldStatus() == null ? dto2.getIsOldStatus() : dto1.getIsOldStatus())
                                            .qtyEta(String.valueOf(
                                                    Integer.parseInt(dto1.getQtyEta() == null ? "0" : dto1.getQtyEta()) +
                                                            Integer.parseInt(dto2.getQtyEta() == null ? "0" : dto2.getQtyEta())
                                            ))
                                            .deliveryDate(dto2.getDeliveryDate())
                                            .deliveryQuantity(dto2.getDeliveryQuantity())
                                            .build();
                                }
                        )
                ));

        // 第二步：按 refNo、destinationSku 和 deliveryDate 分组，计算 deliveryQuantity 合计
        List<String> notNullDestinationSkuList = new ArrayList<>();
        Map<String, PurchaseOrderImportDTO> finalGroup = list.stream()
                .filter(dto -> dto.getDestinationSku() != null && dto.getDeliveryDate() != null)
                .collect(Collectors.groupingBy(
                        dto -> dto.getRefNo() + "_" + dto.getDestinationSku() + "_" + dto.getDeliveryDate(),
                        Collectors.reducing(
                                new PurchaseOrderImportDTO(),
                                (dto1, dto2) -> {
                                    // 从第一次分组结果中获取 qtyEta
                                    String groupKey = dto2.getRefNo() + "_" + dto2.getDestinationSku();
                                    String totalQtyEta = refAndSkuGroup.get(groupKey).getQtyEta();
                                    String refNo = dto1.getRefNo() == null ? dto2.getRefNo() : dto1.getRefNo();
                                    String destinationSku = dto1.getDestinationSku() == null ? dto2.getDestinationSku() : dto1.getDestinationSku();
                                    notNullDestinationSkuList.add(refNo + "_" + destinationSku);
                                    return PurchaseOrderImportDTO.builder()
                                            .refNo(refNo)
                                            .detailId(dto1.getDetailId() == null ? dto2.getDetailId() : dto1.getDetailId())
                                            .sbPoId(dto1.getSbPoId() == null ? dto2.getSbPoId() : dto1.getSbPoId())
                                            .destinationSku(destinationSku)
                                            .isOldStatus(dto1.getIsOldStatus() == null ? dto2.getIsOldStatus() : dto1.getIsOldStatus())
                                            .qtyEta(totalQtyEta)
                                            .deliveryDate(dto1.getDeliveryDate() == null ? dto2.getDeliveryDate() : dto1.getDeliveryDate())
                                            .deliveryQuantity(String.valueOf(
                                                    Integer.parseInt(dto1.getDeliveryQuantity() == null ? "0" : dto1.getDeliveryQuantity()) +
                                                            Integer.parseInt(dto2.getDeliveryQuantity() == null ? "0" : dto2.getDeliveryQuantity())
                                            ))
                                            .build();
                                }
                        )
                ));

        // 收集最终结果
        List<PurchaseOrderImportDTO> result = new ArrayList<>(finalGroup.values());

        // 添加 destinationSku 为 null 的数据（只保留 refNo，其他字段都为 null）
        list.stream()
                .filter(dto -> dto.getDestinationSku() == null && !notNullRefNoList.contains(dto.getRefNo()))
                .map(dto -> PurchaseOrderImportDTO.builder()
                        .refNo(dto.getRefNo())
                        .sbPoId(dto.getSbPoId())
                        .build())
                .forEach(result::add);

        // 添加 destinationSku 不为 null 但 deliveryDate 为 null 的数据
        list.stream()
                .filter(dto -> dto.getDestinationSku() != null && dto.getDeliveryDate() == null && !notNullDestinationSkuList.contains(dto.getRefNo() + "_" + dto.getDestinationSku()))
                .forEach(dto -> {
                    // 获取第一次分组的 qtyEta 合计值
                    String groupKey = dto.getRefNo() + "_" + dto.getDestinationSku();
                    PurchaseOrderImportDTO groupedDto = refAndSkuGroup.get(groupKey);
                    // 使用第一次分组的 qtyEta 值，其他值保持不变
                    dto.setQtyEta(groupedDto.getQtyEta());
                    result.add(dto);
                });

        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveOrderSchedule(OrderSaveScheduleDto val) {
        purchaseOrderDeliveryScheduleService.saveOrderSchedule(val);
        List<YicangPurchaseOrderVirtualVO> orderDetail = purchaseOrderVirtualService.getOrderVirtualDetail(PurchaseOrdersForm.builder().sbPoVirtualId(val.getSbPoVirtualId()).build());
        YicangPurchaseOrdersDO ordersDO = new YicangPurchaseOrdersDO();
        ordersDO.setId(orderDetail.getFirst().getSbPoId());
        ordersDO.setIsImported(IsImportedEnum.IMPORT.getCode());
        purchaseOrdersRepository.updateById(ordersDO);
        return true;
    }

    @Override
    public Integer getNotAbandonedDetail(String virtualSku) {
        Long count = purchaseOrdersRepository.selectNotIncludeStatusCount(PurchaseOrdersForm.builder().virtualSkuId(virtualSku).poStaus("9").build());
        return count.intValue();
    }

    @Override
    public List<YicangPurchaseOrdersDO> getNotAbandonedDetailList(String virtualSku) {
        return purchaseOrdersRepository.selectNotIncludeStatusList(PurchaseOrdersForm.builder().virtualSkuId(virtualSku).poStaus(String.valueOf(PoStatusEnum.CANCELLED.getCode())).build());
    }


    // 抽取的辅助方法
    private PurchaseOrdersExportInfoExcel createBaseExcel(YicangPurchaseOrdersVO order) {
        var excel = new PurchaseOrdersExportInfoExcel();
        excel.setRefNo(order.getRefNo());
        excel.setPoCode(order.getPoCode());
        excel.setPoStatus(order.getPoStatusDesc());
        excel.setSupplierCode(order.getSupplierCode());
        excel.setQtyEtaAll(order.getQtyEtaAll());
        excel.setQtyRecevingAll(order.getQtyRecevingAll());
        excel.setPendingDeliveryQty(order.getPendingDeliveryQty());
        return excel;
    }

    private PurchaseOrdersExportInfoExcel createExcelWithVirtual(YicangPurchaseOrdersVO order, YicangPurchaseOrderVirtualVO virtualOrder) {
        var excel = createBaseExcel(order);
        excel.setDestinationSku(virtualOrder.getDestinationSku());
        excel.setQtyEta(virtualOrder.getQtyEta());
        return excel;
    }

    private PurchaseOrdersExportInfoExcel createExcelWithSchedule(YicangPurchaseOrdersVO order,
                                                                  YicangPurchaseOrderVirtualVO virtualOrder, YicangPurchaseOrderScheduleDto schedule) {
        var excel = createExcelWithVirtual(order, virtualOrder);
        excel.setExpectedDeliveryDate(schedule.getExpectedDeliveryDate());
        excel.setExpectedDeliveryQuantity(schedule.getExpectedDeliveryQuantity());
        return excel;
    }
}




