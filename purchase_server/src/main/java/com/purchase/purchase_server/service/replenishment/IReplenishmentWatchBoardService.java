package com.purchase.purchase_server.service.replenishment;

import java.util.Date;

public interface IReplenishmentWatchBoardService {

    boolean checkEnableArrivingInTime(String advicePurchaseDate, String expectedFactoryDate, Integer purchaseDays, String produceDays);

    boolean checkEnableArrivingInTime(Date advicePurchaseDate, Date expectedFactoryDate, Integer purchaseDays, String produceDays);

    String getRemarks(Date advicePurchaseDate, Date expectedFactoryDate, Integer purchaseDays, String produceDays,
                      String isChangedArrivingDate, Integer productStatus);
}
