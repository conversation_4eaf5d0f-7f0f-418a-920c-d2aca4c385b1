package com.purchase.purchase_server.scheduled;

import com.purchase.purchase_server.entity.consts.NacosConstants;
import com.purchase.purchase_server.service.purchaseOrder.IFetchOrdersFromECService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

/**
 * @Description 定时任务类
 * <AUTHOR>
 * @Date 2024/11/8 13:53
 **/
@Service
public class PurchaseOrdersScheduledTask {

    @Resource
    private IFetchOrdersFromECService fetchOrdersFromECService;
    @Resource
    private NacosConstants constant;

    @XxlJob("fetchPurchaseOrders")
    public void fetchPurchaseOrdersSummaryList() {
        fetchOrdersFromECService.fetchPurchaseOrdersSummaryList(XxlJobHelper.getJobParam());
    }
}
