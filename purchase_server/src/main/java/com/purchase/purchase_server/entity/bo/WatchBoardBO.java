package com.purchase.purchase_server.entity.bo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.Date;

/**
 * @Description 发货试算表格小表格数据
 * <AUTHOR>
 * @Date 2024/1/10 15:07
 **/
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class WatchBoardBO {
    private String shippingProjectId;

    private Date realShippingStartDate;
    /**
     * 虚拟sku或老sku
     */
    private String destinationSku;
    /**
     * 发货数量
     */
    private Integer shippingNum;
    /**
     * 箱数
     */
    private Integer packageNum;

    /**
     * 发货量统计区间
     */
    private String deliveryDateRange;

    /**
     * 目标仓
     */
    private String destinationWarehouse;
    /**
     * 预计到货时间
     */
    private Date expectedArrivingDate;
    /**
     * 备注
     */
    private String remarks;
    /**
     * 外箱尺寸长
     */
    private Integer containerLoad;
    /**
     * 外箱尺寸长
     */
    private Double caseLength;
    /**
     * 外箱尺寸宽
     */
    private Double caseWidth;
    /**
     * 外箱尺寸高
     */
    private Double caseHeight;
    /**
     * 单箱毛重
     */
    private Double singleCaseGrossWeight;
    /**
     * 工厂交期
     */
    private Date factoryFinishedDate;

    private String contractCode;

    private String shippingInventoryId;

    private String factoryFinishedId;

    private Integer deliveryType;
}
