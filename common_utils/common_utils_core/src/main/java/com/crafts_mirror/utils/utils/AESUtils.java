package com.crafts_mirror.utils.utils;

import lombok.extern.slf4j.Slf4j;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.util.Base64;

import static com.google.common.base.Preconditions.checkNotNull;

/**
 * @Description AES加解密工具类
 * <AUTHOR>
 * @Date 2024/4/16 11:37
 **/
@Slf4j
public class AESUtils {
    private static final String AES_ALGORITHM = "AES/CBC/PKCS5Padding";
    private static final String ENCODING = "UTF-8";

    public static String encrypt(String value, String secretKey, String initVector) {
        try {
            Cipher cipher = createCipher(secretKey, initVector, Cipher.ENCRYPT_MODE);
            byte[] encrypted = cipher.doFinal(value.getBytes());

            return Base64.getEncoder().encodeToString(encrypted);
        } catch (Exception ex) {
            log.error("AES加密异常，异常信息：", ex);
            throw new RuntimeException("AES加密异常，异常信息：", ex);
        }
    }

    public static String decrypt(String encrypted, String secretKey, String initVector) {
        try {
            Cipher cipher = createCipher(secretKey, initVector, Cipher.DECRYPT_MODE);
            byte[] original = cipher.doFinal(Base64.getDecoder().decode(encrypted));
            return new String(original);
        } catch (Exception ex) {
            log.error("AES解密异常，异常信息：", ex);
            throw new RuntimeException("AES解密异常，异常信息：", ex);
        }
    }

    private static Cipher createCipher(String secretKey, String initVector, int type) {
        try {
            IvParameterSpec iv = new IvParameterSpec(checkNotNull(initVector, "请输入偏移量").getBytes(ENCODING));
            SecretKeySpec skeySpec = new SecretKeySpec(checkNotNull(secretKey, "请输入密钥").getBytes(ENCODING), "AES");

            Cipher cipher = Cipher.getInstance(AES_ALGORITHM);
            cipher.init(type, skeySpec, iv);

            return cipher;
        } catch (Exception ex) {
            log.error("AES加解密异常，异常信息：", ex);
            throw new RuntimeException("AES加解密异常，异常信息：", ex);
        }
    }
}
