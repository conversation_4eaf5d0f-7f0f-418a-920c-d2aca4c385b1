package com.inventory_server.model.warehouse.entity.dos;
import lombok.Data;
import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import java.util.Date;
import com.inventory_server.infrastructures.entity.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.experimental.SuperBuilder;


/**
 * <AUTHOR>
 * @Date 2024/5/8 9:15
 **/
@EqualsAndHashCode(callSuper = true)
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@TableName("cm_warehouse_service_id_name_relationship")
public class WarehouseServiceIdNameRelationshipDO extends BaseEntity{

  private String warehouseServiceId;
  private String yicangWarehouseName;

}
