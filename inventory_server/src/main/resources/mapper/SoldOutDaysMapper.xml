<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.inventory_server.model.redundancy.mapper.SoldOutDaysMapper">

    <select id="sumLackNumByInventoryIdList" resultType="java.util.Map">
        SELECT inventory_info_id AS inventoryInfoId, SUM(CEIL(lack_num)) AS totalLackNum FROM `cm_inventory_sold_out_days`
        <where>
            type = #{type} AND
            inventory_info_id in
            <foreach collection="inventoryInfoIdList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </where>
        GROUP BY inventory_info_id
    </select>
</mapper>