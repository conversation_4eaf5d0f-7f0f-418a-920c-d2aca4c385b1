package com.purchase.purchase_server.entity.bo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.time.LocalDate;

/**
 * <AUTHOR>
 * @date 2024/5/11
 **/
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class InventoryOnShippingRedundantBO {
    private String inventoryInfoId;
    private String shipmentCode;
    private String warehouse;
    private LocalDate startShippingDate;
    private LocalDate enableUsingDate;
    private Integer redundantNum;
    private String initialRedundancyWatchBoardId;
    private String onShippingWatchBoardId;

    /**
     * 已调拨数量
     */
    private Integer addRedundantNum;
    /**
     * 虚拟sku
     */
    private String virtualSku;
}
